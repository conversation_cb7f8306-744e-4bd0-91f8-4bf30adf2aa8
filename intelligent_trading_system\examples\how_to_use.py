#!/usr/bin/env python3
"""
🧠 M&M AI Trading System - دليل الاستخدام العملي
كيفية استخدام النظام بطرق مختلفة
"""

import requests
import json
import time
import websocket
import threading
from datetime import datetime

# إعدادات الاتصال
BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

class TradingSystemClient:
    """عميل للتفاعل مع نظام التداول"""
    
    def __init__(self, base_url=BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
    
    def get_system_health(self):
        """فحص صحة النظام"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def get_market_data(self):
        """الحصول على بيانات السوق"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/demo-data")
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def get_trading_signals(self):
        """الحصول على إشارات التداول"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/demo-signals")
            return response.json()
        except Exception as e:
            return {"error": str(e)}

def example_1_basic_usage():
    """مثال 1: الاستخدام الأساسي"""
    print("🔥 مثال 1: الاستخدام الأساسي")
    print("=" * 50)
    
    client = TradingSystemClient()
    
    # فحص صحة النظام
    print("1️⃣ فحص صحة النظام:")
    health = client.get_system_health()
    print(f"   الحالة: {health.get('status', 'غير معروف')}")
    print(f"   الوقت: {health.get('timestamp', 'غير معروف')}")
    print()
    
    # الحصول على بيانات السوق
    print("2️⃣ بيانات السوق:")
    market_data = client.get_market_data()
    if 'data' in market_data:
        for symbol, data in market_data['data'].items():
            print(f"   {symbol}: السعر ${data['price']:.2f} | التغيير {data['change']:+.2f}%")
    print()
    
    # الحصول على إشارات التداول
    print("3️⃣ إشارات التداول:")
    signals = client.get_trading_signals()
    if 'signals' in signals:
        for signal in signals['signals']:
            print(f"   {signal['symbol']}: {signal['action']} | الثقة {signal['confidence']:.0%}")
    print()

def example_2_detailed_analysis():
    """مثال 2: تحليل مفصل"""
    print("📊 مثال 2: تحليل مفصل للبيانات")
    print("=" * 50)
    
    client = TradingSystemClient()
    
    # تحليل بيانات السوق
    market_data = client.get_market_data()
    if 'data' in market_data:
        print("📈 تحليل السوق:")
        
        # حساب إحصائيات
        prices = [data['price'] for data in market_data['data'].values()]
        changes = [data['change'] for data in market_data['data'].values()]
        
        avg_price = sum(prices) / len(prices)
        avg_change = sum(changes) / len(changes)
        
        print(f"   متوسط الأسعار: ${avg_price:.2f}")
        print(f"   متوسط التغيير: {avg_change:+.2f}%")
        
        # أفضل وأسوأ أداء
        best_performer = max(market_data['data'].items(), key=lambda x: x[1]['change'])
        worst_performer = min(market_data['data'].items(), key=lambda x: x[1]['change'])
        
        print(f"   أفضل أداء: {best_performer[0]} ({best_performer[1]['change']:+.2f}%)")
        print(f"   أسوأ أداء: {worst_performer[0]} ({worst_performer[1]['change']:+.2f}%)")
    print()

def example_3_trading_strategy():
    """مثال 3: استراتيجية تداول بسيطة"""
    print("🎯 مثال 3: استراتيجية تداول بسيطة")
    print("=" * 50)
    
    client = TradingSystemClient()
    
    # الحصول على الإشارات
    signals = client.get_trading_signals()
    market_data = client.get_market_data()
    
    if 'signals' in signals and 'data' in market_data:
        print("💡 توصيات التداول:")
        
        for signal in signals['signals']:
            symbol = signal['symbol']
            action = signal['action']
            confidence = signal['confidence']
            
            # الحصول على بيانات السوق للرمز
            if symbol in market_data['data']:
                price = market_data['data'][symbol]['price']
                change = market_data['data'][symbol]['change']
                
                # تقييم الإشارة
                if confidence > 0.7:  # ثقة عالية
                    risk_level = "منخفض"
                elif confidence > 0.5:  # ثقة متوسطة
                    risk_level = "متوسط"
                else:  # ثقة منخفضة
                    risk_level = "عالي"
                
                print(f"   {symbol}:")
                print(f"     الإجراء: {action}")
                print(f"     السعر الحالي: ${price:.2f}")
                print(f"     التغيير: {change:+.2f}%")
                print(f"     مستوى الثقة: {confidence:.0%}")
                print(f"     مستوى المخاطر: {risk_level}")
                print()

def example_4_websocket_realtime():
    """مثال 4: البيانات الفورية عبر WebSocket"""
    print("⚡ مثال 4: البيانات الفورية")
    print("=" * 50)
    
    def on_message(ws, message):
        try:
            data = json.loads(message)
            timestamp = datetime.now().strftime("%H:%M:%S")
            print(f"[{timestamp}] بيانات فورية: {data}")
        except Exception as e:
            print(f"خطأ في معالجة الرسالة: {e}")
    
    def on_error(ws, error):
        print(f"خطأ WebSocket: {error}")
    
    def on_close(ws, close_status_code, close_msg):
        print("تم إغلاق اتصال WebSocket")
    
    def on_open(ws):
        print("✅ تم الاتصال بـ WebSocket")
        
        # الاشتراك في بيانات السوق
        subscribe_message = {
            "type": "subscribe",
            "topic": "market_data"
        }
        ws.send(json.dumps(subscribe_message))
        print("📡 تم الاشتراك في بيانات السوق")
    
    try:
        # إنشاء اتصال WebSocket
        ws = websocket.WebSocketApp(WS_URL,
                                  on_open=on_open,
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_close=on_close)
        
        # تشغيل WebSocket في خيط منفصل
        ws_thread = threading.Thread(target=ws.run_forever)
        ws_thread.daemon = True
        ws_thread.start()
        
        print("⏳ الاستماع للبيانات الفورية لمدة 10 ثوانٍ...")
        time.sleep(10)
        
        ws.close()
        print("🔚 تم إنهاء الاستماع")
        
    except Exception as e:
        print(f"خطأ في WebSocket: {e}")

def example_5_custom_requests():
    """مثال 5: طلبات مخصصة"""
    print("🔧 مثال 5: طلبات مخصصة")
    print("=" * 50)
    
    # طلبات مخصصة لـ APIs مختلفة
    endpoints = [
        ("/", "الصفحة الرئيسية"),
        ("/health", "فحص الصحة"),
        ("/api/v1/demo-data", "بيانات السوق"),
        ("/api/v1/demo-signals", "إشارات التداول"),
    ]
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            status = "✅ نجح" if response.status_code == 200 else f"❌ فشل ({response.status_code})"
            print(f"   {description}: {status}")
        except Exception as e:
            print(f"   {description}: ❌ خطأ - {e}")
    print()

def main():
    """الدالة الرئيسية"""
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                    🧠 M&M AI Trading System 🧠                              ║
║                        دليل الاستخدام العملي                               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # تشغيل جميع الأمثلة
    try:
        example_1_basic_usage()
        time.sleep(2)
        
        example_2_detailed_analysis()
        time.sleep(2)
        
        example_3_trading_strategy()
        time.sleep(2)
        
        example_5_custom_requests()
        time.sleep(2)
        
        # WebSocket مثال (اختياري)
        print("هل تريد تجربة البيانات الفورية؟ (y/n): ", end="")
        choice = input().lower()
        if choice == 'y':
            example_4_websocket_realtime()
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    print("\n🎉 انتهى دليل الاستخدام!")
    print("💡 يمكنك الآن استخدام النظام بالطرق المختلفة")

if __name__ == "__main__":
    main()
