/**
 * @file market_data_processor.h
 * @brief High-performance market data processing engine header
 * <AUTHOR> AI Trading System
 * @version 1.0.0
 */

#ifndef MARKET_DATA_PROCESSOR_H
#define MARKET_DATA_PROCESSOR_H

#include "common.h"
#include <functional>

namespace TradingEngine {

// Market statistics structure
struct MarketStatistics {
    Price mean_price;
    Price mean_volume;
    Price volatility;
    Price high;
    Price low;
    Price mean_return;
    Price return_volatility;
    
    MarketStatistics() : mean_price(0), mean_volume(0), volatility(0),
                        high(0), low(0), mean_return(0), return_volatility(0) {}
};

// Real-time statistics
struct RealtimeStats {
    Timestamp last_update;
    long long tick_count;
    Price min_price;
    Price max_price;
    Volume total_volume;
    
    RealtimeStats() : tick_count(0), min_price(0), max_price(0), total_volume(0) {}
};

/**
 * @class MarketDataProcessor
 * @brief High-performance market data processing engine
 * 
 * This class provides real-time market data processing capabilities including:
 * - High-frequency data ingestion
 * - Technical indicator calculations
 * - Statistical analysis
 * - Multi-threaded processing
 * - Lock-free data structures for optimal performance
 */
class MarketDataProcessor {
private:
    // Configuration
    size_t buffer_size_;
    size_t processing_thread_count_;
    
    // Thread management
    std::atomic<bool> running_;
    std::vector<std::thread> processing_threads_;
    std::mutex mutex_;
    std::condition_variable condition_;
    
    // Data storage
    std::unordered_map<Symbol, std::vector<MarketData>> market_data_buffers_;
    std::unordered_map<Symbol, MarketData> latest_data_;
    
    // High-frequency processing queue
    LockFreeQueue<MarketData> processing_queue_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    std::unordered_map<Symbol, RealtimeStats> realtime_stats_;
    
    // Subscribers
    mutable std::mutex subscribers_mutex_;
    std::vector<std::function<void(const MarketData&)>> subscribers_;
    
    // Worker methods
    void processingWorker();
    void processMarketData(const MarketData& data);
    void updateRealtimeStatistics(const MarketData& data);
    void notifySubscribers(const MarketData& data);
    
public:
    /**
     * @brief Constructor
     * @param buffer_size Maximum number of data points to keep per symbol
     */
    explicit MarketDataProcessor(size_t buffer_size = 10000);
    
    /**
     * @brief Destructor
     */
    ~MarketDataProcessor();
    
    // Non-copyable
    MarketDataProcessor(const MarketDataProcessor&) = delete;
    MarketDataProcessor& operator=(const MarketDataProcessor&) = delete;
    
    /**
     * @brief Start the market data processor
     */
    void start();
    
    /**
     * @brief Stop the market data processor
     */
    void stop();
    
    /**
     * @brief Add market data for processing
     * @param data Market data to add
     * @return true if successful, false otherwise
     */
    bool addMarketData(const MarketData& data);
    
    /**
     * @brief Get historical market data for a symbol
     * @param symbol Symbol to get data for
     * @param count Number of data points to retrieve
     * @return Vector of market data
     */
    std::vector<MarketData> getMarketData(const Symbol& symbol, size_t count = 100) const;
    
    /**
     * @brief Get the latest market data for a symbol
     * @param symbol Symbol to get data for
     * @return Latest market data
     */
    MarketData getLatestData(const Symbol& symbol) const;
    
    /**
     * @brief Get list of available symbols
     * @return Vector of symbols
     */
    std::vector<Symbol> getAvailableSymbols() const;
    
    // Technical Indicators
    
    /**
     * @brief Calculate Simple Moving Average
     * @param symbol Symbol to calculate for
     * @param period Period for calculation
     * @return Indicator result
     */
    IndicatorResult calculateSMA(const Symbol& symbol, size_t period) const;
    
    /**
     * @brief Calculate Exponential Moving Average
     * @param symbol Symbol to calculate for
     * @param period Period for calculation
     * @return Indicator result
     */
    IndicatorResult calculateEMA(const Symbol& symbol, size_t period) const;
    
    /**
     * @brief Calculate Relative Strength Index
     * @param symbol Symbol to calculate for
     * @param period Period for calculation (default: 14)
     * @return Indicator result
     */
    IndicatorResult calculateRSI(const Symbol& symbol, size_t period = 14) const;
    
    /**
     * @brief Calculate MACD (Moving Average Convergence Divergence)
     * @param symbol Symbol to calculate for
     * @param fast_period Fast EMA period (default: 12)
     * @param slow_period Slow EMA period (default: 26)
     * @param signal_period Signal line period (default: 9)
     * @return Pair of MACD line and signal line
     */
    std::pair<IndicatorResult, IndicatorResult> calculateMACD(
        const Symbol& symbol, 
        size_t fast_period = 12, 
        size_t slow_period = 26, 
        size_t signal_period = 9) const;
    
    /**
     * @brief Calculate Bollinger Bands
     * @param symbol Symbol to calculate for
     * @param period Period for calculation (default: 20)
     * @param std_dev_multiplier Standard deviation multiplier (default: 2.0)
     * @return Pair of upper and lower bands
     */
    std::pair<IndicatorResult, IndicatorResult> calculateBollingerBands(
        const Symbol& symbol, 
        size_t period = 20, 
        double std_dev_multiplier = 2.0) const;
    
    // Statistical Analysis
    
    /**
     * @brief Calculate market statistics for a symbol
     * @param symbol Symbol to calculate for
     * @param period Period for calculation
     * @return Market statistics
     */
    MarketStatistics calculateStatistics(const Symbol& symbol, size_t period = 100) const;
    
    /**
     * @brief Get real-time statistics for a symbol
     * @param symbol Symbol to get stats for
     * @return Real-time statistics
     */
    RealtimeStats getRealtimeStats(const Symbol& symbol) const;
    
    // Subscription system
    
    /**
     * @brief Subscribe to market data updates
     * @param callback Callback function to call on data updates
     */
    void subscribe(std::function<void(const MarketData&)> callback);
    
    // Performance metrics
    
    /**
     * @brief Get processing performance metrics
     * @return Performance metrics
     */
    struct PerformanceMetrics {
        long long total_processed;
        double avg_processing_time_us;
        double max_processing_time_us;
        size_t queue_size;
        size_t active_symbols;
    };
    
    PerformanceMetrics getPerformanceMetrics() const;
};

} // namespace TradingEngine

#endif // MARKET_DATA_PROCESSOR_H
