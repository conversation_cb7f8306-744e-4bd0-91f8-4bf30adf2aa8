#!/usr/bin/env python3
"""
🚀 نظام التداول الذكي المتكامل - التطبيق الرئيسي
Intelligent Trading System (ITS) - Main Application

نظام تداول متطور يدمج:
- الذكاء الاصطناعي والتعلم الآلي
- التحليل الفني والأساسي المتقدم
- إدارة المخاطر الذكية
- واجهات برمجة تطبيقات شاملة
- تحليلات الأداء المتقدمة
"""

import asyncio
import sys
import os
import argparse
import logging
from pathlib import Path
from datetime import datetime
import signal
import uvicorn
from typing import Optional
import warnings
warnings.filterwarnings('ignore')

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/its_main.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ITS_Main")

class IntelligentTradingSystem:
    """النظام الرئيسي للتداول الذكي المتكامل"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self.config = self._load_config()
        self.components = {}
        self.running = False
        
        # إنشاء المجلدات المطلوبة
        self._create_directories()
        
        logger.info("🧠 تم تهيئة نظام التداول الذكي المتكامل")
    
    def _load_config(self) -> dict:
        """تحميل التكوين"""
        default_config = {
            "system": {
                "name": "Intelligent Trading System",
                "version": "1.0.0",
                "environment": "development",
                "debug": True
            },
            "api": {
                "host": "0.0.0.0",
                "port": 8000,
                "reload": True,
                "workers": 1
            },
            "database": {
                "type": "sqlite",
                "path": "data/its_database.db",
                "backup_enabled": True,
                "backup_interval": 3600
            },
            "ai": {
                "models_path": "models/",
                "training_enabled": True,
                "auto_retrain": True,
                "retrain_interval": 86400
            },
            "trading": {
                "paper_trading": True,
                "max_positions": 10,
                "risk_per_trade": 0.02,
                "max_daily_loss": 0.05
            },
            "data": {
                "sources": ["yahoo", "alpha_vantage"],
                "update_interval": 300,
                "cache_enabled": True,
                "real_time_enabled": True
            },
            "monitoring": {
                "enabled": True,
                "metrics_interval": 60,
                "alerts_enabled": True,
                "performance_tracking": True
            }
        }
        
        # تحميل التكوين من ملف إذا كان متوفراً
        if self.config_file and os.path.exists(self.config_file):
            try:
                import yaml
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f)
                    default_config.update(file_config)
                logger.info(f"تم تحميل التكوين من: {self.config_file}")
            except Exception as e:
                logger.warning(f"خطأ في تحميل ملف التكوين: {e}")
        
        return default_config
    
    def _create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            "logs",
            "data",
            "models",
            "backups",
            "reports",
            "cache"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        logger.info("✅ تم إنشاء المجلدات المطلوبة")
    
    async def initialize_components(self):
        """تهيئة مكونات النظام"""
        logger.info("🔧 بدء تهيئة مكونات النظام...")
        
        try:
            # تهيئة قاعدة البيانات
            await self._initialize_database()
            
            # تهيئة جامع البيانات
            await self._initialize_data_collector()
            
            # تهيئة نماذج الذكاء الاصطناعي
            await self._initialize_ai_models()
            
            # تهيئة محرك التداول
            await self._initialize_trading_engine()
            
            # تهيئة نظام المراقبة
            await self._initialize_monitoring()
            
            logger.info("✅ تم تهيئة جميع مكونات النظام بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة المكونات: {e}")
            raise
    
    async def _initialize_database(self):
        """تهيئة قاعدة البيانات"""
        logger.info("📊 تهيئة قاعدة البيانات...")
        
        try:
            # إنشاء قاعدة بيانات SQLite للاختبار
            import sqlite3
            
            db_path = self.config["database"]["path"]
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # إنشاء الجداول الأساسية
            tables = [
                """
                CREATE TABLE IF NOT EXISTS market_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    open_price REAL NOT NULL,
                    high_price REAL NOT NULL,
                    low_price REAL NOT NULL,
                    close_price REAL NOT NULL,
                    volume INTEGER NOT NULL,
                    source TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
                """,
                """
                CREATE TABLE IF NOT EXISTS trading_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    strategy TEXT NOT NULL,
                    action TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    entry_price REAL,
                    target_price REAL,
                    stop_loss REAL,
                    reasoning TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
                """,
                """
                CREATE TABLE IF NOT EXISTS portfolios (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    initial_balance REAL NOT NULL,
                    current_balance REAL NOT NULL,
                    total_value REAL NOT NULL,
                    risk_tolerance TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
                """,
                """
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    portfolio_id INTEGER,
                    symbol TEXT NOT NULL,
                    action TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    price REAL NOT NULL,
                    commission REAL DEFAULT 0,
                    timestamp DATETIME NOT NULL,
                    status TEXT DEFAULT 'executed',
                    FOREIGN KEY (portfolio_id) REFERENCES portfolios (id)
                )
                """,
                """
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    portfolio_id INTEGER,
                    date DATE NOT NULL,
                    total_return REAL,
                    daily_return REAL,
                    volatility REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    win_rate REAL,
                    FOREIGN KEY (portfolio_id) REFERENCES portfolios (id)
                )
                """
            ]
            
            for table_sql in tables:
                cursor.execute(table_sql)
            
            conn.commit()
            conn.close()
            
            self.components["database"] = {"status": "initialized", "path": db_path}
            logger.info("✅ تم تهيئة قاعدة البيانات")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            raise
    
    async def _initialize_data_collector(self):
        """تهيئة جامع البيانات"""
        logger.info("📈 تهيئة جامع البيانات...")
        
        try:
            from data_layer.ingestion.market_data_collector import (
                AdvancedMarketDataCollector, 
                MarketDataConfig
            )
            
            config = MarketDataConfig(
                update_interval=self.config["data"]["update_interval"],
                cache_enabled=self.config["data"]["cache_enabled"],
                data_validation=True
            )
            
            collector = AdvancedMarketDataCollector(config)
            
            self.components["data_collector"] = {
                "instance": collector,
                "status": "initialized",
                "sources": self.config["data"]["sources"]
            }
            
            logger.info("✅ تم تهيئة جامع البيانات")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة جامع البيانات: {e}")
            raise
    
    async def _initialize_ai_models(self):
        """تهيئة نماذج الذكاء الاصطناعي"""
        logger.info("🧠 تهيئة نماذج الذكاء الاصطناعي...")
        
        try:
            from trading_engine.strategy_engine.ai_strategies import (
                LSTMPredictionStrategy,
                ReinforcementLearningStrategy,
                EnsembleAIStrategy
            )
            
            # إنشاء الاستراتيجيات
            strategies = {
                "lstm": LSTMPredictionStrategy(),
                "reinforcement_learning": ReinforcementLearningStrategy()
            }
            
            # إنشاء الاستراتيجية المجمعة
            ensemble_strategy = EnsembleAIStrategy(
                list(strategies.values()),
                {"voting_method": "weighted", "min_confidence": 0.6}
            )
            
            strategies["ensemble"] = ensemble_strategy
            
            self.components["ai_models"] = {
                "strategies": strategies,
                "status": "initialized",
                "models_path": self.config["ai"]["models_path"]
            }
            
            logger.info("✅ تم تهيئة نماذج الذكاء الاصطناعي")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة نماذج الذكاء الاصطناعي: {e}")
            raise
    
    async def _initialize_trading_engine(self):
        """تهيئة محرك التداول"""
        logger.info("⚙️ تهيئة محرك التداول...")
        
        try:
            trading_config = {
                "paper_trading": self.config["trading"]["paper_trading"],
                "max_positions": self.config["trading"]["max_positions"],
                "risk_per_trade": self.config["trading"]["risk_per_trade"],
                "max_daily_loss": self.config["trading"]["max_daily_loss"]
            }
            
            self.components["trading_engine"] = {
                "config": trading_config,
                "status": "initialized",
                "active_positions": {},
                "daily_pnl": 0.0
            }
            
            logger.info("✅ تم تهيئة محرك التداول")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة محرك التداول: {e}")
            raise
    
    async def _initialize_monitoring(self):
        """تهيئة نظام المراقبة"""
        logger.info("📊 تهيئة نظام المراقبة...")
        
        try:
            monitoring_config = {
                "enabled": self.config["monitoring"]["enabled"],
                "metrics_interval": self.config["monitoring"]["metrics_interval"],
                "alerts_enabled": self.config["monitoring"]["alerts_enabled"]
            }
            
            self.components["monitoring"] = {
                "config": monitoring_config,
                "status": "initialized",
                "metrics": {},
                "alerts": []
            }
            
            logger.info("✅ تم تهيئة نظام المراقبة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة نظام المراقبة: {e}")
            raise
    
    async def start_background_tasks(self):
        """بدء المهام الخلفية"""
        logger.info("🔄 بدء المهام الخلفية...")
        
        tasks = []
        
        # مهمة جمع البيانات
        if self.config["data"]["real_time_enabled"]:
            tasks.append(asyncio.create_task(self._data_collection_task()))
        
        # مهمة توليد الإشارات
        tasks.append(asyncio.create_task(self._signal_generation_task()))
        
        # مهمة المراقبة
        if self.config["monitoring"]["enabled"]:
            tasks.append(asyncio.create_task(self._monitoring_task()))
        
        # مهمة النسخ الاحتياطي
        if self.config["database"]["backup_enabled"]:
            tasks.append(asyncio.create_task(self._backup_task()))
        
        logger.info(f"✅ تم بدء {len(tasks)} مهمة خلفية")
        
        return tasks
    
    async def _data_collection_task(self):
        """مهمة جمع البيانات"""
        while self.running:
            try:
                # جمع البيانات من المصادر المختلفة
                symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "BTC-USD"]
                
                collector = self.components["data_collector"]["instance"]
                data = await collector.collect_all_data(symbols)
                
                logger.debug(f"تم جمع البيانات لـ {len(symbols)} رمز")
                
                await asyncio.sleep(self.config["data"]["update_interval"])
                
            except Exception as e:
                logger.error(f"خطأ في مهمة جمع البيانات: {e}")
                await asyncio.sleep(60)
    
    async def _signal_generation_task(self):
        """مهمة توليد الإشارات"""
        while self.running:
            try:
                # توليد إشارات التداول
                strategies = self.components["ai_models"]["strategies"]
                
                for strategy_name, strategy in strategies.items():
                    if strategy.is_trained:
                        # محاكاة توليد إشارة
                        logger.debug(f"توليد إشارة من استراتيجية {strategy_name}")
                
                await asyncio.sleep(300)  # كل 5 دقائق
                
            except Exception as e:
                logger.error(f"خطأ في مهمة توليد الإشارات: {e}")
                await asyncio.sleep(60)
    
    async def _monitoring_task(self):
        """مهمة المراقبة"""
        while self.running:
            try:
                # جمع مقاييس الأداء
                metrics = {
                    "timestamp": datetime.now().isoformat(),
                    "system_status": "running",
                    "active_components": len([c for c in self.components.values() 
                                            if c.get("status") == "initialized"]),
                    "memory_usage": "N/A",  # يمكن إضافة مراقبة الذاكرة
                    "cpu_usage": "N/A"      # يمكن إضافة مراقبة المعالج
                }
                
                self.components["monitoring"]["metrics"] = metrics
                
                await asyncio.sleep(self.config["monitoring"]["metrics_interval"])
                
            except Exception as e:
                logger.error(f"خطأ في مهمة المراقبة: {e}")
                await asyncio.sleep(60)
    
    async def _backup_task(self):
        """مهمة النسخ الاحتياطي"""
        while self.running:
            try:
                # إنشاء نسخة احتياطية من قاعدة البيانات
                import shutil
                
                source = self.config["database"]["path"]
                backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                backup_path = f"backups/{backup_name}"
                
                if os.path.exists(source):
                    shutil.copy2(source, backup_path)
                    logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
                
                await asyncio.sleep(self.config["database"]["backup_interval"])
                
            except Exception as e:
                logger.error(f"خطأ في مهمة النسخ الاحتياطي: {e}")
                await asyncio.sleep(3600)
    
    async def start_api_server(self):
        """بدء خادم API"""
        logger.info("🌐 بدء خادم API...")
        
        try:
            from api_layer.rest_api.main_api import app
            
            config = uvicorn.Config(
                app,
                host=self.config["api"]["host"],
                port=self.config["api"]["port"],
                reload=self.config["api"]["reload"],
                log_level="info"
            )
            
            server = uvicorn.Server(config)
            await server.serve()
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء خادم API: {e}")
            raise
    
    async def run(self):
        """تشغيل النظام الكامل"""
        logger.info("🚀 بدء تشغيل نظام التداول الذكي المتكامل")
        
        try:
            self.running = True
            
            # تهيئة المكونات
            await self.initialize_components()
            
            # بدء المهام الخلفية
            background_tasks = await self.start_background_tasks()
            
            # بدء خادم API
            await self.start_api_server()
            
        except KeyboardInterrupt:
            logger.info("🛑 تم إيقاف النظام بواسطة المستخدم")
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل النظام: {e}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """إغلاق النظام"""
        logger.info("🔄 إغلاق نظام التداول الذكي المتكامل...")
        
        self.running = False
        
        # إغلاق المكونات
        if "data_collector" in self.components:
            collector = self.components["data_collector"].get("instance")
            if collector:
                collector.close()
        
        logger.info("✅ تم إغلاق النظام بنجاح")

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description="نظام التداول الذكي المتكامل")
    parser.add_argument("--config", "-c", help="ملف التكوين")
    parser.add_argument("--port", "-p", type=int, default=8000, help="منفذ الخادم")
    parser.add_argument("--host", default="0.0.0.0", help="عنوان الخادم")
    parser.add_argument("--debug", action="store_true", help="وضع التطوير")
    
    args = parser.parse_args()
    
    # إنشاء النظام
    its = IntelligentTradingSystem(config_file=args.config)
    
    # تحديث التكوين من المعاملات
    its.config["api"]["port"] = args.port
    its.config["api"]["host"] = args.host
    its.config["system"]["debug"] = args.debug
    
    # إعداد معالج الإشارات للإغلاق الآمن
    def signal_handler(signum, frame):
        logger.info("تم استلام إشارة الإغلاق")
        its.running = False
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # تشغيل النظام
    try:
        asyncio.run(its.run())
    except KeyboardInterrupt:
        logger.info("تم إيقاف النظام")
    except Exception as e:
        logger.error(f"خطأ في تشغيل النظام: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
