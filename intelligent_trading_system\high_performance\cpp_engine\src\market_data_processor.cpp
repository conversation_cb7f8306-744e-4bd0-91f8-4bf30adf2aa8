/**
 * @file market_data_processor.cpp
 * @brief High-performance market data processing engine
 * <AUTHOR> AI Trading System
 * @version 1.0.0
 */

#include "../include/market_data_processor.h"
#include <algorithm>
#include <numeric>
#include <cmath>
#include <future>

namespace TradingEngine {

// Initialize static members
std::mutex Logger::log_mutex;
std::ofstream Logger::log_file;
bool Logger::initialized = false;

void Logger::initialize(const std::string& filename) {
    std::lock_guard<std::mutex> lock(log_mutex);
    if (!initialized) {
        log_file.open(filename, std::ios::app);
        initialized = true;
    }
}

void Logger::log(Level level, const std::string& message) {
    std::lock_guard<std::mutex> lock(log_mutex);
    if (!initialized) initialize();
    
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::string level_str;
    switch (level) {
        case DEBUG: level_str = "DEBUG"; break;
        case INFO: level_str = "INFO"; break;
        case WARNING: level_str = "WARNING"; break;
        case ERROR: level_str = "ERROR"; break;
    }
    
    log_file << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S")
             << " [" << level_str << "] " << message << std::endl;
    log_file.flush();
}

MarketDataProcessor::MarketDataProcessor(size_t buffer_size)
    : buffer_size_(buffer_size), running_(false), processing_thread_count_(4) {
    
    Logger::initialize("market_data_processor.log");
    Logger::info("MarketDataProcessor initialized with buffer size: " + std::to_string(buffer_size));
    
    // Initialize processing threads
    for (size_t i = 0; i < processing_thread_count_; ++i) {
        processing_threads_.emplace_back(&MarketDataProcessor::processingWorker, this);
    }
}

MarketDataProcessor::~MarketDataProcessor() {
    stop();
    
    // Join all processing threads
    for (auto& thread : processing_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    
    Logger::info("MarketDataProcessor destroyed");
}

void MarketDataProcessor::start() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!running_) {
        running_ = true;
        Logger::info("MarketDataProcessor started");
    }
}

void MarketDataProcessor::stop() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (running_) {
        running_ = false;
        condition_.notify_all();
        Logger::info("MarketDataProcessor stopped");
    }
}

bool MarketDataProcessor::addMarketData(const MarketData& data) {
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (!running_) {
            return false;
        }
        
        // Add to symbol-specific buffer
        auto& buffer = market_data_buffers_[data.symbol];
        buffer.push_back(data);
        
        // Maintain buffer size limit
        if (buffer.size() > buffer_size_) {
            buffer.erase(buffer.begin(), buffer.begin() + (buffer.size() - buffer_size_));
        }
        
        // Update latest data
        latest_data_[data.symbol] = data;
        
        // Add to processing queue
        processing_queue_.enqueue(data);
    }
    
    condition_.notify_one();
    return true;
}

std::vector<MarketData> MarketDataProcessor::getMarketData(const Symbol& symbol, size_t count) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = market_data_buffers_.find(symbol);
    if (it == market_data_buffers_.end()) {
        return {};
    }
    
    const auto& buffer = it->second;
    if (buffer.empty()) {
        return {};
    }
    
    size_t start_idx = (count >= buffer.size()) ? 0 : buffer.size() - count;
    return std::vector<MarketData>(buffer.begin() + start_idx, buffer.end());
}

MarketData MarketDataProcessor::getLatestData(const Symbol& symbol) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = latest_data_.find(symbol);
    if (it != latest_data_.end()) {
        return it->second;
    }
    
    return MarketData{};
}

std::vector<Symbol> MarketDataProcessor::getAvailableSymbols() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::vector<Symbol> symbols;
    symbols.reserve(market_data_buffers_.size());
    
    for (const auto& pair : market_data_buffers_) {
        symbols.push_back(pair.first);
    }
    
    return symbols;
}

IndicatorResult MarketDataProcessor::calculateSMA(const Symbol& symbol, size_t period) const {
    auto data = getMarketData(symbol, period);
    IndicatorResult result;
    
    if (data.size() < period) {
        return result;
    }
    
    std::vector<Price> prices;
    prices.reserve(data.size());
    
    for (const auto& md : data) {
        prices.push_back(md.close);
    }
    
    // Calculate Simple Moving Average
    for (size_t i = period - 1; i < prices.size(); ++i) {
        Price sum = 0.0;
        for (size_t j = i - period + 1; j <= i; ++j) {
            sum += prices[j];
        }
        result.values.push_back(sum / period);
    }
    
    return result;
}

IndicatorResult MarketDataProcessor::calculateEMA(const Symbol& symbol, size_t period) const {
    auto data = getMarketData(symbol, period * 2); // Get more data for EMA calculation
    IndicatorResult result;
    
    if (data.size() < period) {
        return result;
    }
    
    std::vector<Price> prices;
    prices.reserve(data.size());
    
    for (const auto& md : data) {
        prices.push_back(md.close);
    }
    
    // Calculate Exponential Moving Average
    Price multiplier = 2.0 / (period + 1);
    Price ema = prices[0]; // Start with first price
    
    for (size_t i = 1; i < prices.size(); ++i) {
        ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
        if (i >= period - 1) {
            result.values.push_back(ema);
        }
    }
    
    return result;
}

IndicatorResult MarketDataProcessor::calculateRSI(const Symbol& symbol, size_t period) const {
    auto data = getMarketData(symbol, period * 2);
    IndicatorResult result;
    
    if (data.size() < period + 1) {
        return result;
    }
    
    std::vector<Price> prices;
    prices.reserve(data.size());
    
    for (const auto& md : data) {
        prices.push_back(md.close);
    }
    
    // Calculate price changes
    std::vector<Price> gains, losses;
    for (size_t i = 1; i < prices.size(); ++i) {
        Price change = prices[i] - prices[i-1];
        gains.push_back(change > 0 ? change : 0);
        losses.push_back(change < 0 ? -change : 0);
    }
    
    // Calculate RSI
    for (size_t i = period - 1; i < gains.size(); ++i) {
        Price avg_gain = 0.0, avg_loss = 0.0;
        
        for (size_t j = i - period + 1; j <= i; ++j) {
            avg_gain += gains[j];
            avg_loss += losses[j];
        }
        
        avg_gain /= period;
        avg_loss /= period;
        
        if (avg_loss == 0.0) {
            result.values.push_back(100.0);
        } else {
            Price rs = avg_gain / avg_loss;
            Price rsi = 100.0 - (100.0 / (1.0 + rs));
            result.values.push_back(rsi);
        }
    }
    
    return result;
}

std::pair<IndicatorResult, IndicatorResult> MarketDataProcessor::calculateMACD(
    const Symbol& symbol, size_t fast_period, size_t slow_period, size_t signal_period) const {
    
    auto fast_ema = calculateEMA(symbol, fast_period);
    auto slow_ema = calculateEMA(symbol, slow_period);
    
    IndicatorResult macd_line, signal_line;
    
    if (fast_ema.values.empty() || slow_ema.values.empty()) {
        return {macd_line, signal_line};
    }
    
    // Calculate MACD line
    size_t min_size = std::min(fast_ema.values.size(), slow_ema.values.size());
    for (size_t i = 0; i < min_size; ++i) {
        macd_line.values.push_back(fast_ema.values[i] - slow_ema.values[i]);
    }
    
    // Calculate signal line (EMA of MACD line)
    if (macd_line.values.size() >= signal_period) {
        Price multiplier = 2.0 / (signal_period + 1);
        Price signal_ema = macd_line.values[0];
        
        for (size_t i = 1; i < macd_line.values.size(); ++i) {
            signal_ema = (macd_line.values[i] * multiplier) + (signal_ema * (1 - multiplier));
            if (i >= signal_period - 1) {
                signal_line.values.push_back(signal_ema);
            }
        }
    }
    
    return {macd_line, signal_line};
}

std::pair<IndicatorResult, IndicatorResult> MarketDataProcessor::calculateBollingerBands(
    const Symbol& symbol, size_t period, double std_dev_multiplier) const {
    
    auto sma = calculateSMA(symbol, period);
    auto data = getMarketData(symbol, period * 2);
    
    IndicatorResult upper_band, lower_band;
    
    if (sma.values.empty() || data.size() < period) {
        return {upper_band, lower_band};
    }
    
    std::vector<Price> prices;
    for (const auto& md : data) {
        prices.push_back(md.close);
    }
    
    // Calculate Bollinger Bands
    for (size_t i = 0; i < sma.values.size(); ++i) {
        size_t data_start = prices.size() - sma.values.size() + i - period + 1;
        
        // Calculate standard deviation for this period
        Price mean = sma.values[i];
        Price variance = 0.0;
        
        for (size_t j = 0; j < period; ++j) {
            Price diff = prices[data_start + j] - mean;
            variance += diff * diff;
        }
        
        Price std_dev = std::sqrt(variance / period);
        
        upper_band.values.push_back(mean + (std_dev_multiplier * std_dev));
        lower_band.values.push_back(mean - (std_dev_multiplier * std_dev));
    }
    
    return {upper_band, lower_band};
}

MarketStatistics MarketDataProcessor::calculateStatistics(const Symbol& symbol, size_t period) const {
    auto data = getMarketData(symbol, period);
    MarketStatistics stats;
    
    if (data.empty()) {
        return stats;
    }
    
    std::vector<Price> prices, volumes;
    prices.reserve(data.size());
    volumes.reserve(data.size());
    
    Price high = data[0].high;
    Price low = data[0].low;
    
    for (const auto& md : data) {
        prices.push_back(md.close);
        volumes.push_back(static_cast<Price>(md.volume));
        
        high = std::max(high, md.high);
        low = std::min(low, md.low);
    }
    
    // Calculate statistics
    stats.mean_price = std::accumulate(prices.begin(), prices.end(), 0.0) / prices.size();
    stats.mean_volume = std::accumulate(volumes.begin(), volumes.end(), 0.0) / volumes.size();
    stats.high = high;
    stats.low = low;
    stats.volatility = Utils::standardDeviation(prices);
    
    // Calculate returns
    std::vector<Price> returns;
    for (size_t i = 1; i < prices.size(); ++i) {
        returns.push_back((prices[i] - prices[i-1]) / prices[i-1]);
    }
    
    if (!returns.empty()) {
        stats.mean_return = std::accumulate(returns.begin(), returns.end(), 0.0) / returns.size();
        stats.return_volatility = Utils::standardDeviation(returns);
    }
    
    return stats;
}

void MarketDataProcessor::processingWorker() {
    Logger::info("Processing worker thread started");
    
    while (running_) {
        MarketData data;
        
        if (processing_queue_.dequeue(data)) {
            // Process the market data
            processMarketData(data);
        } else {
            // No data available, wait a bit
            std::this_thread::sleep_for(std::chrono::microseconds(100));
        }
    }
    
    Logger::info("Processing worker thread stopped");
}

void MarketDataProcessor::processMarketData(const MarketData& data) {
    // Update real-time statistics
    updateRealtimeStatistics(data);
    
    // Trigger any registered callbacks
    notifySubscribers(data);
    
    // Log high-frequency data (optional, for debugging)
    if (Logger::initialized) {
        Logger::debug("Processed market data for " + data.symbol + 
                     " at price " + std::to_string(data.close));
    }
}

void MarketDataProcessor::updateRealtimeStatistics(const MarketData& data) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    auto& stats = realtime_stats_[data.symbol];
    stats.last_update = data.timestamp;
    stats.tick_count++;
    
    // Update price statistics
    if (stats.tick_count == 1) {
        stats.min_price = stats.max_price = data.close;
    } else {
        stats.min_price = std::min(stats.min_price, data.close);
        stats.max_price = std::max(stats.max_price, data.close);
    }
    
    // Update volume
    stats.total_volume += data.volume;
}

void MarketDataProcessor::notifySubscribers(const MarketData& data) {
    std::lock_guard<std::mutex> lock(subscribers_mutex_);
    
    for (auto& callback : subscribers_) {
        try {
            callback(data);
        } catch (const std::exception& e) {
            Logger::error("Subscriber callback failed: " + std::string(e.what()));
        }
    }
}

void MarketDataProcessor::subscribe(std::function<void(const MarketData&)> callback) {
    std::lock_guard<std::mutex> lock(subscribers_mutex_);
    subscribers_.push_back(std::move(callback));
}

RealtimeStats MarketDataProcessor::getRealtimeStats(const Symbol& symbol) const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    auto it = realtime_stats_.find(symbol);
    if (it != realtime_stats_.end()) {
        return it->second;
    }
    
    return RealtimeStats{};
}

} // namespace TradingEngine
