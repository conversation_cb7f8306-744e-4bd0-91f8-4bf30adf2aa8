"""
🤖 استراتيجيات التداول بالذكاء الاصطناعي
AI-Powered Trading Strategies

يحتوي على استراتيجيات تداول متقدمة تعتمد على:
- التعلم العميق للتنبؤ بالأسعار
- التعلم المعزز لاتخاذ القرارات
- معالجة اللغة الطبيعية لتحليل المشاعر
- الرؤية الحاسوبية لتحليل الأنماط
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod
import logging
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

@dataclass
class TradingSignal:
    """إشارة التداول"""
    symbol: str
    action: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float  # 0-1
    entry_price: float
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    position_size: float = 0.1
    timestamp: datetime = None
    reasoning: str = ""
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class BaseAIStrategy(ABC):
    """الفئة الأساسية لاستراتيجيات الذكاء الاصطناعي"""
    
    def __init__(self, name: str, config: Dict = None):
        self.name = name
        self.config = config or {}
        self.logger = self._setup_logger()
        self.model = None
        self.is_trained = False
        
    def _setup_logger(self):
        """إعداد نظام السجلات"""
        logger = logging.getLogger(f'AIStrategy_{self.name}')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    @abstractmethod
    def train(self, data: pd.DataFrame) -> bool:
        """تدريب النموذج"""
        pass
    
    @abstractmethod
    def generate_signal(self, data: pd.DataFrame) -> TradingSignal:
        """توليد إشارة التداول"""
        pass
    
    @abstractmethod
    def update_model(self, new_data: pd.DataFrame) -> bool:
        """تحديث النموذج ببيانات جديدة"""
        pass
    
    def save_model(self, filepath: str):
        """حفظ النموذج"""
        if self.model is not None:
            joblib.dump({
                'model': self.model,
                'config': self.config,
                'is_trained': self.is_trained
            }, filepath)
            self.logger.info(f"تم حفظ النموذج في: {filepath}")
    
    def load_model(self, filepath: str):
        """تحميل النموذج"""
        try:
            saved_data = joblib.load(filepath)
            self.model = saved_data['model']
            self.config = saved_data['config']
            self.is_trained = saved_data['is_trained']
            self.logger.info(f"تم تحميل النموذج من: {filepath}")
        except Exception as e:
            self.logger.error(f"خطأ في تحميل النموذج: {e}")

class LSTMPredictionStrategy(BaseAIStrategy):
    """استراتيجية التنبؤ باستخدام LSTM"""
    
    def __init__(self, config: Dict = None):
        default_config = {
            'sequence_length': 60,
            'prediction_horizon': 5,
            'confidence_threshold': 0.6,
            'features': ['open', 'high', 'low', 'close', 'volume'],
            'target': 'close',
            'model_params': {
                'hidden_units': [128, 64, 32],
                'dropout_rate': 0.2,
                'learning_rate': 0.001,
                'epochs': 100
            }
        }
        
        if config:
            default_config.update(config)
        
        super().__init__("LSTM_Prediction", default_config)
        
    def train(self, data: pd.DataFrame) -> bool:
        """تدريب نموذج LSTM"""
        
        try:
            from ..ai_core.deep_learning.lstm_models import AdvancedLSTMPredictor
            
            self.logger.info("بدء تدريب نموذج LSTM...")
            
            # إنشاء النموذج
            self.model = AdvancedLSTMPredictor(self.config['model_params'])
            
            # تدريب النموذج
            self.model.train(data, self.config['target'])
            
            self.is_trained = True
            self.logger.info("تم تدريب نموذج LSTM بنجاح")
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تدريب نموذج LSTM: {e}")
            return False
    
    def generate_signal(self, data: pd.DataFrame) -> TradingSignal:
        """توليد إشارة تداول باستخدام LSTM"""
        
        if not self.is_trained or self.model is None:
            return TradingSignal(
                symbol=data.get('symbol', 'UNKNOWN'),
                action='HOLD',
                confidence=0.0,
                entry_price=data['close'].iloc[-1],
                reasoning="النموذج غير مدرب"
            )
        
        try:
            # التنبؤ بالأسعار المستقبلية
            predictions = self.model.predict(
                data.tail(self.config['sequence_length']),
                steps_ahead=self.config['prediction_horizon']
            )
            
            current_price = data['close'].iloc[-1]
            predicted_price = predictions[-1]
            
            # حساب التغيير المتوقع
            price_change = (predicted_price - current_price) / current_price
            
            # تحديد العمل والثقة
            if abs(price_change) < 0.01:  # تغيير أقل من 1%
                action = 'HOLD'
                confidence = 0.3
            elif price_change > 0.02:  # ارتفاع متوقع أكثر من 2%
                action = 'BUY'
                confidence = min(abs(price_change) * 10, 1.0)
            elif price_change < -0.02:  # انخفاض متوقع أكثر من 2%
                action = 'SELL'
                confidence = min(abs(price_change) * 10, 1.0)
            else:
                action = 'HOLD'
                confidence = 0.5
            
            # حساب أهداف الربح ووقف الخسارة
            if action == 'BUY':
                target_price = current_price * 1.03  # هدف 3%
                stop_loss = current_price * 0.98     # وقف خسارة 2%
            elif action == 'SELL':
                target_price = current_price * 0.97  # هدف 3%
                stop_loss = current_price * 1.02     # وقف خسارة 2%
            else:
                target_price = None
                stop_loss = None
            
            return TradingSignal(
                symbol=data.get('symbol', 'UNKNOWN'),
                action=action,
                confidence=confidence,
                entry_price=current_price,
                target_price=target_price,
                stop_loss=stop_loss,
                reasoning=f"LSTM توقع تغيير {price_change:.2%} في السعر"
            )
            
        except Exception as e:
            self.logger.error(f"خطأ في توليد إشارة LSTM: {e}")
            return TradingSignal(
                symbol=data.get('symbol', 'UNKNOWN'),
                action='HOLD',
                confidence=0.0,
                entry_price=data['close'].iloc[-1],
                reasoning=f"خطأ في النموذج: {str(e)}"
            )
    
    def update_model(self, new_data: pd.DataFrame) -> bool:
        """تحديث نموذج LSTM بالبيانات الجديدة"""
        
        if not self.is_trained:
            return self.train(new_data)
        
        try:
            # إعادة تدريب النموذج بالبيانات الجديدة
            self.model.train(new_data, self.config['target'])
            self.logger.info("تم تحديث نموذج LSTM")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث نموذج LSTM: {e}")
            return False

class ReinforcementLearningStrategy(BaseAIStrategy):
    """استراتيجية التعلم المعزز"""
    
    def __init__(self, config: Dict = None):
        default_config = {
            'agent_type': 'DQN',  # 'DQN', 'PPO', 'A3C'
            'environment_config': {
                'initial_balance': 100000,
                'transaction_cost': 0.001,
                'max_position': 1.0,
                'lookback_window': 30
            },
            'training_episodes': 1000,
            'update_frequency': 100
        }
        
        if config:
            default_config.update(config)
        
        super().__init__("Reinforcement_Learning", default_config)
        self.environment = None
        self.agent = None
    
    def train(self, data: pd.DataFrame) -> bool:
        """تدريب وكيل التعلم المعزز"""
        
        try:
            from ..ai_core.reinforcement_learning.trading_environment import AdvancedTradingEnvironment
            from ..ai_core.reinforcement_learning.dqn_agent import DQNAgent
            
            self.logger.info("بدء تدريب وكيل التعلم المعزز...")
            
            # إنشاء البيئة
            self.environment = AdvancedTradingEnvironment(
                data=data,
                **self.config['environment_config']
            )
            
            # إنشاء الوكيل
            if self.config['agent_type'] == 'DQN':
                self.agent = DQNAgent(
                    state_size=self.environment.observation_space.shape[0],
                    action_size=self.environment.action_space.shape[0]
                )
            
            # تدريب الوكيل
            for episode in range(self.config['training_episodes']):
                state = self.environment.reset()
                total_reward = 0
                
                while True:
                    action = self.agent.act(state)
                    next_state, reward, done, info = self.environment.step(action)
                    
                    self.agent.remember(state, action, reward, next_state, done)
                    state = next_state
                    total_reward += reward
                    
                    if done:
                        break
                
                # تحديث النموذج
                if episode % self.config['update_frequency'] == 0:
                    self.agent.replay()
                    self.logger.info(f"الحلقة {episode}: إجمالي المكافأة = {total_reward:.2f}")
            
            self.model = self.agent
            self.is_trained = True
            self.logger.info("تم تدريب وكيل التعلم المعزز بنجاح")
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تدريب وكيل التعلم المعزز: {e}")
            return False
    
    def generate_signal(self, data: pd.DataFrame) -> TradingSignal:
        """توليد إشارة تداول باستخدام التعلم المعزز"""
        
        if not self.is_trained or self.agent is None:
            return TradingSignal(
                symbol=data.get('symbol', 'UNKNOWN'),
                action='HOLD',
                confidence=0.0,
                entry_price=data['close'].iloc[-1],
                reasoning="الوكيل غير مدرب"
            )
        
        try:
            # إنشاء بيئة مؤقتة للحصول على الحالة
            temp_env = AdvancedTradingEnvironment(
                data=data,
                **self.config['environment_config']
            )
            
            state = temp_env.reset()
            
            # الحصول على العمل من الوكيل
            action = self.agent.act(state, training=False)
            
            current_price = data['close'].iloc[-1]
            
            # تحويل العمل إلى إشارة تداول
            if action[0] > 0.1:
                trading_action = 'BUY'
                confidence = min(action[0], 1.0)
            elif action[0] < -0.1:
                trading_action = 'SELL'
                confidence = min(abs(action[0]), 1.0)
            else:
                trading_action = 'HOLD'
                confidence = 0.5
            
            return TradingSignal(
                symbol=data.get('symbol', 'UNKNOWN'),
                action=trading_action,
                confidence=confidence,
                entry_price=current_price,
                reasoning=f"قرار وكيل التعلم المعزز: {action[0]:.3f}"
            )
            
        except Exception as e:
            self.logger.error(f"خطأ في توليد إشارة التعلم المعزز: {e}")
            return TradingSignal(
                symbol=data.get('symbol', 'UNKNOWN'),
                action='HOLD',
                confidence=0.0,
                entry_price=data['close'].iloc[-1],
                reasoning=f"خطأ في الوكيل: {str(e)}"
            )
    
    def update_model(self, new_data: pd.DataFrame) -> bool:
        """تحديث وكيل التعلم المعزز"""
        
        if not self.is_trained:
            return self.train(new_data)
        
        try:
            # تدريب إضافي بالبيانات الجديدة
            temp_env = AdvancedTradingEnvironment(
                data=new_data,
                **self.config['environment_config']
            )
            
            # تشغيل حلقات تدريب إضافية
            for episode in range(100):  # تدريب سريع
                state = temp_env.reset()
                
                while True:
                    action = self.agent.act(state)
                    next_state, reward, done, info = temp_env.step(action)
                    
                    self.agent.remember(state, action, reward, next_state, done)
                    state = next_state
                    
                    if done:
                        break
                
                if episode % 20 == 0:
                    self.agent.replay()
            
            self.logger.info("تم تحديث وكيل التعلم المعزز")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث وكيل التعلم المعزز: {e}")
            return False

class EnsembleAIStrategy(BaseAIStrategy):
    """استراتيجية مجمعة تدمج عدة نماذج ذكاء اصطناعي"""
    
    def __init__(self, strategies: List[BaseAIStrategy], config: Dict = None):
        default_config = {
            'voting_method': 'weighted',  # 'majority', 'weighted', 'confidence'
            'weights': None,  # أوزان الاستراتيجيات
            'min_confidence': 0.5
        }
        
        if config:
            default_config.update(config)
        
        super().__init__("Ensemble_AI", default_config)
        self.strategies = strategies
        
        # تعيين أوزان متساوية إذا لم تُحدد
        if self.config['weights'] is None:
            self.config['weights'] = [1.0] * len(strategies)
    
    def train(self, data: pd.DataFrame) -> bool:
        """تدريب جميع الاستراتيجيات"""
        
        self.logger.info("تدريب الاستراتيجيات المجمعة...")
        
        success_count = 0
        
        for i, strategy in enumerate(self.strategies):
            try:
                if strategy.train(data):
                    success_count += 1
                    self.logger.info(f"تم تدريب {strategy.name} بنجاح")
                else:
                    self.logger.warning(f"فشل في تدريب {strategy.name}")
            except Exception as e:
                self.logger.error(f"خطأ في تدريب {strategy.name}: {e}")
        
        self.is_trained = success_count > 0
        self.logger.info(f"تم تدريب {success_count}/{len(self.strategies)} استراتيجيات")
        
        return self.is_trained
    
    def generate_signal(self, data: pd.DataFrame) -> TradingSignal:
        """توليد إشارة مجمعة من جميع الاستراتيجيات"""
        
        if not self.is_trained:
            return TradingSignal(
                symbol=data.get('symbol', 'UNKNOWN'),
                action='HOLD',
                confidence=0.0,
                entry_price=data['close'].iloc[-1],
                reasoning="الاستراتيجيات غير مدربة"
            )
        
        signals = []
        
        # جمع إشارات من جميع الاستراتيجيات
        for strategy in self.strategies:
            try:
                if strategy.is_trained:
                    signal = strategy.generate_signal(data)
                    signals.append(signal)
            except Exception as e:
                self.logger.error(f"خطأ في الحصول على إشارة من {strategy.name}: {e}")
        
        if not signals:
            return TradingSignal(
                symbol=data.get('symbol', 'UNKNOWN'),
                action='HOLD',
                confidence=0.0,
                entry_price=data['close'].iloc[-1],
                reasoning="لا توجد إشارات صالحة"
            )
        
        # دمج الإشارات
        return self._combine_signals(signals, data)
    
    def _combine_signals(self, signals: List[TradingSignal], data: pd.DataFrame) -> TradingSignal:
        """دمج الإشارات المتعددة"""
        
        if self.config['voting_method'] == 'majority':
            return self._majority_voting(signals, data)
        elif self.config['voting_method'] == 'weighted':
            return self._weighted_voting(signals, data)
        elif self.config['voting_method'] == 'confidence':
            return self._confidence_voting(signals, data)
        else:
            return self._weighted_voting(signals, data)
    
    def _majority_voting(self, signals: List[TradingSignal], data: pd.DataFrame) -> TradingSignal:
        """التصويت بالأغلبية"""
        
        actions = [signal.action for signal in signals]
        action_counts = {action: actions.count(action) for action in set(actions)}
        
        final_action = max(action_counts, key=action_counts.get)
        confidence = action_counts[final_action] / len(signals)
        
        return TradingSignal(
            symbol=data.get('symbol', 'UNKNOWN'),
            action=final_action,
            confidence=confidence,
            entry_price=data['close'].iloc[-1],
            reasoning=f"تصويت الأغلبية: {action_counts}"
        )
    
    def _weighted_voting(self, signals: List[TradingSignal], data: pd.DataFrame) -> TradingSignal:
        """التصويت المرجح"""
        
        action_scores = {'BUY': 0, 'SELL': 0, 'HOLD': 0}
        total_weight = 0
        
        for i, signal in enumerate(signals):
            weight = self.config['weights'][i] if i < len(self.config['weights']) else 1.0
            action_scores[signal.action] += weight * signal.confidence
            total_weight += weight
        
        # تطبيع النتائج
        for action in action_scores:
            action_scores[action] /= total_weight
        
        final_action = max(action_scores, key=action_scores.get)
        confidence = action_scores[final_action]
        
        return TradingSignal(
            symbol=data.get('symbol', 'UNKNOWN'),
            action=final_action,
            confidence=confidence,
            entry_price=data['close'].iloc[-1],
            reasoning=f"تصويت مرجح: {action_scores}"
        )
    
    def _confidence_voting(self, signals: List[TradingSignal], data: pd.DataFrame) -> TradingSignal:
        """التصويت بناءً على الثقة"""
        
        # اختيار الإشارة ذات أعلى ثقة
        best_signal = max(signals, key=lambda s: s.confidence)
        
        if best_signal.confidence >= self.config['min_confidence']:
            return best_signal
        else:
            return TradingSignal(
                symbol=data.get('symbol', 'UNKNOWN'),
                action='HOLD',
                confidence=0.5,
                entry_price=data['close'].iloc[-1],
                reasoning=f"أعلى ثقة ({best_signal.confidence:.2f}) أقل من الحد الأدنى"
            )
    
    def update_model(self, new_data: pd.DataFrame) -> bool:
        """تحديث جميع الاستراتيجيات"""
        
        success_count = 0
        
        for strategy in self.strategies:
            try:
                if strategy.update_model(new_data):
                    success_count += 1
            except Exception as e:
                self.logger.error(f"خطأ في تحديث {strategy.name}: {e}")
        
        self.logger.info(f"تم تحديث {success_count}/{len(self.strategies)} استراتيجيات")
        
        return success_count > 0

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء بيانات تجريبية
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', periods=1000, freq='D')
    
    price = 100
    prices = [price]
    
    for _ in range(999):
        change = np.random.normal(0, 0.02)
        price *= (1 + change)
        prices.append(price)
    
    data = pd.DataFrame({
        'date': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, 1000),
        'symbol': 'TEST'
    })
    
    # إنشاء استراتيجية LSTM
    lstm_strategy = LSTMPredictionStrategy()
    
    # تدريب الاستراتيجية
    print("🚀 تدريب استراتيجية LSTM...")
    lstm_strategy.train(data)
    
    # توليد إشارة
    signal = lstm_strategy.generate_signal(data.tail(100))
    
    print(f"\n📊 إشارة التداول:")
    print(f"   الرمز: {signal.symbol}")
    print(f"   العمل: {signal.action}")
    print(f"   الثقة: {signal.confidence:.2f}")
    print(f"   السعر: {signal.entry_price:.2f}")
    print(f"   السبب: {signal.reasoning}")
