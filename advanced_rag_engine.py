#!/usr/bin/env python3
"""
🔗 Advanced RAG Engine
محرك RAG المتقدم للتداول

المرحلة 3: بناء محرك سؤال وجواب خاص بالتداول (RAG)
- ربط النموذج اللغوي بقاعدة المعرفة
- استرجاع ذكي للمعلومات
- توليد إجابات دقيقة ومفصلة
- دمج البيانات المباشرة مع المعرفة
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import asyncio
from dataclasses import dataclass

# استيراد المحركات المطورة
from advanced_llm_engine import AdvancedLLMEngine, TradingContext
from advanced_knowledge_base import AdvancedTradingKnowledgeBase
from real_time_data_engine import RealTimeDataEngine

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RAGResponse:
    answer: str
    sources: List[Dict]
    confidence: float
    market_data: Dict = None
    reasoning: str = ""
    recommendations: List[str] = None

class AdvancedRAGEngine:
    """محرك RAG المتقدم للتداول"""
    
    def __init__(self):
        """تهيئة محرك RAG"""
        self.llm_engine = AdvancedLLMEngine(model_type="simulation")
        self.knowledge_base = AdvancedTradingKnowledgeBase()
        self.data_engine = None
        
        # محاولة تهيئة محرك البيانات
        try:
            self.data_engine = RealTimeDataEngine()
            logger.info("✅ Real-time data engine connected")
        except Exception as e:
            logger.warning(f"⚠️ Real-time data engine not available: {str(e)}")
        
        # إعدادات RAG
        self.max_context_length = 3000
        self.min_relevance_score = 0.3
        self.max_sources = 3
        
        logger.info("🔗 Advanced RAG Engine initialized")
    
    async def process_query(self, query: str, user_context: TradingContext = None) -> RAGResponse:
        """معالجة الاستعلام الرئيسية"""
        try:
            # 1. تحليل الاستعلام
            query_analysis = self.analyze_query(query)
            
            # 2. استرجاع المعرفة ذات الصلة
            relevant_knowledge = self.retrieve_knowledge(query, query_analysis)
            
            # 3. جلب البيانات المباشرة إذا لزم الأمر
            market_data = await self.get_relevant_market_data(query, query_analysis)
            
            # 4. بناء السياق المدمج
            enhanced_context = self.build_enhanced_context(
                query, relevant_knowledge, market_data, user_context
            )
            
            # 5. توليد الإجابة
            answer = await self.generate_enhanced_answer(query, enhanced_context)
            
            # 6. تقييم الثقة
            confidence = self.calculate_confidence(relevant_knowledge, market_data)
            
            # 7. استخراج التوصيات
            recommendations = self.extract_recommendations(answer, query_analysis)
            
            return RAGResponse(
                answer=answer,
                sources=[{
                    'title': src['document'].title,
                    'author': src['document'].author,
                    'category': src['document'].category,
                    'relevance': src['score']
                } for src in relevant_knowledge],
                confidence=confidence,
                market_data=market_data,
                reasoning=self.explain_reasoning(query_analysis, relevant_knowledge),
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"❌ Error processing query: {str(e)}")
            return RAGResponse(
                answer="عذراً، حدث خطأ في معالجة استفسارك. حاول إعادة صياغة السؤال.",
                sources=[],
                confidence=0.0
            )
    
    def analyze_query(self, query: str) -> Dict:
        """تحليل الاستعلام لفهم النية والكيانات"""
        query_lower = query.lower()
        
        analysis = {
            'intent': 'general',
            'symbols': [],
            'indicators': [],
            'timeframe': None,
            'complexity': 'simple',
            'requires_market_data': False,
            'requires_analysis': False,
            'language': 'ar' if any(char in 'أبتثجحخدذرزسشصضطظعغفقكلمنهوي' for char in query) else 'en'
        }
        
        # تحديد النية
        intent_keywords = {
            'price_inquiry': ['سعر', 'كم', 'price', 'cost', 'قيمة'],
            'technical_analysis': ['تحليل', 'analyze', 'rsi', 'macd', 'مؤشر'],
            'strategy': ['استراتيجية', 'strategy', 'خطة', 'plan'],
            'education': ['ما هو', 'what is', 'شرح', 'explain', 'تعلم'],
            'prediction': ['توقع', 'predict', 'مستقبل', 'future'],
            'comparison': ['قارن', 'compare', 'أفضل', 'best'],
            'risk_management': ['مخاطر', 'risk', 'خسارة', 'loss']
        }
        
        for intent, keywords in intent_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                analysis['intent'] = intent
                break
        
        # استخراج الرموز
        symbol_patterns = {
            'aapl': 'AAPL', 'apple': 'AAPL', 'أبل': 'AAPL',
            'googl': 'GOOGL', 'google': 'GOOGL', 'جوجل': 'GOOGL',
            'msft': 'MSFT', 'microsoft': 'MSFT', 'مايكروسوفت': 'MSFT',
            'tsla': 'TSLA', 'tesla': 'TSLA', 'تسلا': 'TSLA',
            'gold': 'GC=F', 'ذهب': 'GC=F', 'الذهب': 'GC=F',
            'btc': 'BTC-USD', 'bitcoin': 'BTC-USD', 'بيتكوين': 'BTC-USD'
        }
        
        for pattern, symbol in symbol_patterns.items():
            if pattern in query_lower:
                analysis['symbols'].append(symbol)
        
        # استخراج المؤشرات
        indicator_keywords = ['rsi', 'macd', 'sma', 'ema', 'bollinger', 'fibonacci']
        for indicator in indicator_keywords:
            if indicator in query_lower:
                analysis['indicators'].append(indicator.upper())
        
        # تحديد ما إذا كانت البيانات المباشرة مطلوبة
        if analysis['intent'] in ['price_inquiry', 'technical_analysis', 'prediction']:
            analysis['requires_market_data'] = True
        
        if analysis['intent'] in ['technical_analysis', 'strategy', 'prediction']:
            analysis['requires_analysis'] = True
        
        # تحديد التعقيد
        if len(analysis['symbols']) > 1 or len(analysis['indicators']) > 2:
            analysis['complexity'] = 'complex'
        elif analysis['intent'] in ['technical_analysis', 'strategy']:
            analysis['complexity'] = 'medium'
        
        return analysis
    
    def retrieve_knowledge(self, query: str, analysis: Dict) -> List[Dict]:
        """استرجاع المعرفة ذات الصلة"""
        # البحث الأساسي
        results = self.knowledge_base.search_knowledge(query, top_k=5)
        
        # تحسين النتائج بناءً على التحليل
        if analysis['intent'] == 'education':
            # إعطاء أولوية للمحتوى التعليمي
            educational_docs = self.knowledge_base.get_document_by_category('technical_analysis')
            educational_docs.extend(self.knowledge_base.get_document_by_category('psychology'))
            
            for doc in educational_docs[:2]:
                if not any(r['document'].id == doc.id for r in results):
                    results.append({
                        'document': doc,
                        'score': 0.8,
                        'source': 'category_match'
                    })
        
        elif analysis['intent'] == 'strategy':
            # إعطاء أولوية للاستراتيجيات
            strategy_docs = self.knowledge_base.get_document_by_category('strategy')
            
            for doc in strategy_docs[:2]:
                if not any(r['document'].id == doc.id for r in results):
                    results.append({
                        'document': doc,
                        'score': 0.9,
                        'source': 'strategy_match'
                    })
        
        # تصفية النتائج حسب الصلة
        filtered_results = [r for r in results if r['score'] >= self.min_relevance_score]
        
        # ترتيب وتحديد العدد
        filtered_results.sort(key=lambda x: x['score'], reverse=True)
        
        return filtered_results[:self.max_sources]
    
    async def get_relevant_market_data(self, query: str, analysis: Dict) -> Optional[Dict]:
        """جلب البيانات المباشرة ذات الصلة"""
        if not analysis['requires_market_data'] or not self.data_engine:
            return None
        
        market_data = {}
        
        try:
            # جلب بيانات الرموز المطلوبة
            if analysis['symbols']:
                for symbol in analysis['symbols']:
                    data = self.data_engine.get_symbol_data(symbol)
                    if data:
                        market_data[symbol] = {
                            'price': data.price,
                            'change': data.change,
                            'change_percent': data.change_percent,
                            'volume': data.volume,
                            'high': data.high,
                            'low': data.low,
                            'timestamp': data.timestamp
                        }
            else:
                # جلب بيانات عامة للأسهم الرئيسية
                main_symbols = ['AAPL', 'GOOGL', 'MSFT']
                for symbol in main_symbols:
                    data = self.data_engine.get_symbol_data(symbol)
                    if data:
                        market_data[symbol] = {
                            'price': data.price,
                            'change_percent': data.change_percent
                        }
            
            return market_data if market_data else None
            
        except Exception as e:
            logger.error(f"❌ Error fetching market data: {str(e)}")
            return None
    
    def build_enhanced_context(self, query: str, knowledge: List[Dict], 
                             market_data: Dict, user_context: TradingContext) -> str:
        """بناء السياق المحسن للنموذج"""
        context_parts = []
        
        # إضافة المعرفة ذات الصلة
        if knowledge:
            context_parts.append("📚 **المعرفة ذات الصلة:**")
            for i, item in enumerate(knowledge, 1):
                doc = item['document']
                relevance = item['score']
                context_parts.append(f"\n{i}. **{doc.title}** (صلة: {relevance:.1%})")
                context_parts.append(f"المؤلف: {doc.author}")
                context_parts.append(f"المحتوى: {doc.content[:500]}...")
        
        # إضافة البيانات المباشرة
        if market_data:
            context_parts.append("\n\n📊 **البيانات المباشرة:**")
            for symbol, data in market_data.items():
                context_parts.append(f"\n{symbol}:")
                context_parts.append(f"- السعر: ${data['price']:.2f}")
                if 'change_percent' in data:
                    context_parts.append(f"- التغيير: {data['change_percent']:+.2f}%")
                if 'volume' in data:
                    context_parts.append(f"- الحجم: {data['volume']:,}")
        
        # إضافة سياق المستخدم
        if user_context:
            context_parts.append(f"\n\n👤 **سياق المستخدم:**")
            context_parts.append(f"- مستوى الخبرة: {user_context.trading_experience}")
            context_parts.append(f"- تحمل المخاطر: {user_context.risk_tolerance}")
            if user_context.preferred_assets:
                context_parts.append(f"- الأصول المفضلة: {', '.join(user_context.preferred_assets)}")
        
        # إضافة تعليمات خاصة
        context_parts.append(f"\n\n🎯 **تعليمات:**")
        context_parts.append("- قدم إجابة شاملة ومفصلة")
        context_parts.append("- استخدم البيانات المباشرة عند توفرها")
        context_parts.append("- اربط المعرفة النظرية بالتطبيق العملي")
        context_parts.append("- اذكر مستوى الثقة في التوصيات")
        context_parts.append("- حذر من المخاطر دائماً")
        
        return "\n".join(context_parts)
    
    async def generate_enhanced_answer(self, query: str, context: str) -> str:
        """توليد إجابة محسنة"""
        # بناء الرسالة المحسنة
        enhanced_query = f"""
السؤال: {query}

السياق المتاح:
{context}

المطلوب: إجابة شاملة ومفصلة تدمج المعرفة النظرية مع البيانات العملية.
"""
        
        # توليد الإجابة
        answer = await self.llm_engine.generate_response(enhanced_query)
        
        return answer
    
    def calculate_confidence(self, knowledge: List[Dict], market_data: Dict) -> float:
        """حساب مستوى الثقة في الإجابة"""
        confidence = 0.5  # قاعدة أساسية
        
        # زيادة الثقة بناءً على جودة المعرفة
        if knowledge:
            avg_relevance = sum(item['score'] for item in knowledge) / len(knowledge)
            confidence += avg_relevance * 0.3
        
        # زيادة الثقة بناءً على توفر البيانات المباشرة
        if market_data:
            confidence += 0.2
        
        # تحديد الحد الأقصى
        return min(confidence, 1.0)
    
    def extract_recommendations(self, answer: str, analysis: Dict) -> List[str]:
        """استخراج التوصيات من الإجابة"""
        recommendations = []
        
        answer_lower = answer.lower()
        
        # البحث عن كلمات التوصية
        if 'شراء' in answer_lower or 'buy' in answer_lower:
            recommendations.append("توصية شراء")
        
        if 'بيع' in answer_lower or 'sell' in answer_lower:
            recommendations.append("توصية بيع")
        
        if 'انتظار' in answer_lower or 'hold' in answer_lower:
            recommendations.append("توصية انتظار")
        
        if 'وقف خسارة' in answer_lower or 'stop loss' in answer_lower:
            recommendations.append("استخدام وقف الخسارة")
        
        return recommendations
    
    def explain_reasoning(self, analysis: Dict, knowledge: List[Dict]) -> str:
        """شرح منطق الإجابة"""
        reasoning_parts = []
        
        reasoning_parts.append(f"تم تحليل الاستعلام كـ: {analysis['intent']}")
        
        if knowledge:
            reasoning_parts.append(f"تم استرجاع {len(knowledge)} مصدر معرفة ذو صلة")
        
        if analysis['requires_market_data']:
            reasoning_parts.append("تم دمج البيانات المباشرة للحصول على تحليل دقيق")
        
        return " | ".join(reasoning_parts)

async def main():
    """دالة الاختبار"""
    print("🔗 Advanced RAG Engine Test")
    print("=" * 60)
    
    # إنشاء محرك RAG
    rag = AdvancedRAGEngine()
    
    # إنشاء سياق مستخدم تجريبي
    user_context = TradingContext(
        user_id="test_user",
        trading_experience="متوسط",
        risk_tolerance="متوسط",
        preferred_assets=["AAPL", "GOLD"]
    )
    
    # أسئلة تجريبية
    test_queries = [
        "حلل لي سهم AAPL تحليلاً فنياً شاملاً",
        "ما هو مؤشر RSI وكيف أستخدمه؟",
        "أريد استراتيجية تداول للمبتدئين",
        "كيف أدير المخاطر في محفظتي؟"
    ]
    
    for query in test_queries:
        print(f"\n❓ السؤال: {query}")
        print("=" * 60)
        
        response = await rag.process_query(query, user_context)
        
        print(f"🤖 الإجابة:")
        print(response.answer)
        
        print(f"\n📚 المصادر ({len(response.sources)}):")
        for source in response.sources:
            print(f"  • {source['title']} - {source['author']} ({source['relevance']:.1%})")
        
        print(f"\n🎯 مستوى الثقة: {response.confidence:.1%}")
        
        if response.recommendations:
            print(f"💡 التوصيات: {', '.join(response.recommendations)}")
        
        print(f"🧠 المنطق: {response.reasoning}")
        print("-" * 60)
    
    print(f"\n✅ تم اختبار محرك RAG بنجاح!")

if __name__ == "__main__":
    asyncio.run(main())
