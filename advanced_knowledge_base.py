#!/usr/bin/env python3
"""
📚 Advanced Trading Knowledge Base
قاعدة المعرفة المتقدمة للتداول

المرحلة 2: تغذية الروبوت بمحتوى التداول الحقيقي
- كتب التداول الكلاسيكية
- استراتيجيات ناجحة
- بيانات تاريخية
- محتوى تعليمي متقدم
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import asyncio
from dataclasses import dataclass
import hashlib

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TradingDocument:
    id: str
    title: str
    content: str
    category: str
    author: str
    source: str
    difficulty: str
    tags: List[str]
    embedding: List[float] = None
    metadata: Dict = None

class AdvancedTradingKnowledgeBase:
    """قاعدة المعرفة المتقدمة للتداول"""
    
    def __init__(self):
        """تهيئة قاعدة المعرفة"""
        self.documents = {}
        self.embeddings_db = None
        self.knowledge_dir = "trading_knowledge"
        self.create_directories()
        
        # تحميل المعرفة الأساسية
        self.load_core_knowledge()
        
        # محاولة تهيئة ChromaDB
        self.init_vector_db()
        
        logger.info("📚 Advanced Trading Knowledge Base initialized")
    
    def create_directories(self):
        """إنشاء مجلدات قاعدة المعرفة"""
        directories = [
            self.knowledge_dir,
            f"{self.knowledge_dir}/books",
            f"{self.knowledge_dir}/strategies", 
            f"{self.knowledge_dir}/indicators",
            f"{self.knowledge_dir}/market_data",
            "embeddings",
            "processed_books"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def init_vector_db(self):
        """تهيئة قاعدة البيانات الشعاعية"""
        try:
            import chromadb
            from chromadb.config import Settings
            
            self.chroma_client = chromadb.Client(Settings(
                chroma_db_impl="duckdb+parquet",
                persist_directory="./chroma_db"
            ))
            
            # إنشاء مجموعة للتداول
            self.collection = self.chroma_client.get_or_create_collection(
                name="trading_knowledge",
                metadata={"description": "Trading and financial knowledge base"}
            )
            
            logger.info("✅ ChromaDB initialized successfully")
            
        except Exception as e:
            logger.warning(f"⚠️ ChromaDB not available: {str(e)}")
            self.chroma_client = None
            self.collection = None
    
    def load_core_knowledge(self):
        """تحميل المعرفة الأساسية"""
        
        # كتب التداول الكلاسيكية
        trading_books = {
            "trading_in_the_zone": {
                "title": "Trading in the Zone",
                "author": "Mark Douglas",
                "category": "psychology",
                "difficulty": "intermediate",
                "content": """
                علم نفس التداول هو أهم جانب في النجاح المالي. يجب على المتداول أن يطور عقلية صحيحة تتضمن:
                
                1. قبول المخاطر: كل صفقة لها احتمالية خسارة
                2. التفكير الاحتمالي: لا توجد ضمانات في السوق
                3. الانضباط: الالتزام بالخطة مهما كانت المشاعر
                4. الصبر: انتظار الفرص المناسبة
                5. إدارة التوقعات: لا تتوقع الكمال
                
                المتداول الناجح يفهم أن السوق عشوائي في المدى القصير ولكن له أنماط في المدى الطويل.
                الهدف ليس أن تكون محقاً في كل صفقة، بل أن تحقق ربحاً إجمالياً.
                
                أهم القواعد النفسية:
                - لا تطارد السوق
                - اقطع خسائرك واتركها أرباحك تنمو
                - تداول بحجم يسمح لك بالنوم مرتاحاً
                - تعلم من أخطائك ولا تكررها
                """
            },
            
            "technical_analysis_murphy": {
                "title": "Technical Analysis of the Financial Markets",
                "author": "John J. Murphy",
                "category": "technical_analysis",
                "difficulty": "advanced",
                "content": """
                التحليل الفني يقوم على ثلاث مبادئ أساسية:
                
                1. السعر يخصم كل شيء: جميع المعلومات منعكسة في السعر
                2. الأسعار تتحرك في اتجاهات: الاتجاه صديقك
                3. التاريخ يعيد نفسه: الأنماط تتكرر
                
                المؤشرات الفنية الأساسية:
                
                المتوسطات المتحركة:
                - SMA: متوسط بسيط للأسعار
                - EMA: متوسط أسي يعطي وزن أكبر للأسعار الحديثة
                - الاستخدام: تحديد الاتجاه ونقاط الدعم/المقاومة
                
                مؤشر القوة النسبية (RSI):
                - يقيس سرعة وتغيير حركة السعر
                - القيم فوق 70: تشبع شرائي
                - القيم تحت 30: تشبع بيعي
                
                MACD:
                - يقيس العلاقة بين متوسطين متحركين
                - عبور خط الإشارة: إشارة تداول
                - تباعد: إشارة انعكاس محتملة
                
                نطاقات بولينجر:
                - تقيس التقلبات
                - النطاق العلوي: مقاومة ديناميكية
                - النطاق السفلي: دعم ديناميكي
                """
            },
            
            "elder_trading_room": {
                "title": "Come Into My Trading Room",
                "author": "Alexander Elder",
                "category": "strategy",
                "difficulty": "intermediate",
                "content": """
                نظام التداول الثلاثي لألكسندر إلدر:
                
                الشاشة الأولى - الاتجاه العام:
                - استخدم إطار زمني طويل (أسبوعي)
                - حدد الاتجاه العام باستخدام MACD
                - تداول فقط في اتجاه الترند العام
                
                الشاشة الثانية - التوقيت:
                - استخدم إطار زمني أقصر (يومي)
                - ابحث عن إشارات عكس الاتجاه قصير المدى
                - استخدم Stochastic أو RSI
                
                الشاشة الثالثة - الدخول:
                - استخدم إطار زمني أقصر (ساعات)
                - ادخل عند كسر أعلى/أدنى اليوم السابق
                
                إدارة المخاطر:
                - ضع وقف الخسارة تحت أدنى الأيام القليلة الماضية
                - استخدم قاعدة 2% (لا تخاطر بأكثر من 2% من رأس المال)
                - خذ أرباح جزئية عند مستويات مقاومة
                
                قواعد إضافية:
                - لا تتداول ضد الاتجاه العام
                - تجنب التداول في الأسواق الجانبية
                - احتفظ بسجل تداول مفصل
                """
            },
            
            "japanese_candlesticks": {
                "title": "Japanese Candlestick Charting Techniques",
                "author": "Steve Nison",
                "category": "patterns",
                "difficulty": "intermediate",
                "content": """
                الشموع اليابانية توفر معلومات أكثر من الرسوم البيانية التقليدية:
                
                أنواع الشموع الأساسية:
                
                1. الشمعة الصاعدة (البيضاء/الخضراء):
                - الإغلاق أعلى من الافتتاح
                - تشير لضغط شرائي
                
                2. الشمعة الهابطة (السوداء/الحمراء):
                - الإغلاق أقل من الافتتاح
                - تشير لضغط بيعي
                
                أنماط الانعكاس:
                
                Hammer (المطرقة):
                - جسم صغير في الأعلى
                - ظل سفلي طويل
                - إشارة انعكاس صاعد
                
                Shooting Star (النجم الساقط):
                - جسم صغير في الأسفل
                - ظل علوي طويل
                - إشارة انعكاس هابط
                
                Doji:
                - الافتتاح = الإغلاق تقريباً
                - تشير لتردد السوق
                - إشارة انعكاس محتملة
                
                أنماط الاستمرار:
                
                Spinning Top:
                - جسم صغير مع ظلال طويلة
                - تشير لاستمرار التردد
                
                Marubozu:
                - جسم طويل بدون ظلال
                - تشير لاستمرار الاتجاه
                
                قواعد التأكيد:
                - انتظر تأكيد النمط في الشمعة التالية
                - استخدم مؤشرات أخرى للتأكيد
                - راعي حجم التداول
                """
            }
        }
        
        # استراتيجيات التداول
        trading_strategies = {
            "breakout_strategy": {
                "title": "استراتيجية الكسر (Breakout)",
                "category": "strategy",
                "difficulty": "beginner",
                "content": """
                استراتيجية الكسر تعتمد على كسر مستويات الدعم والمقاومة:
                
                المفهوم:
                - الأسعار تتحرك في نطاقات
                - كسر هذه النطاقات يشير لحركة قوية
                - الهدف هو الاستفادة من هذه الحركة
                
                خطوات التطبيق:
                1. حدد مستويات الدعم والمقاومة
                2. انتظر كسر واضح مع حجم عالي
                3. ادخل في اتجاه الكسر
                4. ضع وقف خسارة تحت/فوق المستوى المكسور
                5. حدد هدف ربح بناءً على المدى السابق
                
                شروط الكسر الصحيح:
                - كسر بنسبة 3% على الأقل
                - حجم تداول أعلى من المتوسط
                - إغلاق فوق/تحت المستوى
                - عدم العودة للنطاق السابق
                
                إدارة المخاطر:
                - وقف خسارة: 2% تحت نقطة الكسر
                - هدف ربح: مسافة النطاق السابق
                - نسبة مخاطرة/مكافأة: 1:2 على الأقل
                """
            },
            
            "mean_reversion": {
                "title": "استراتيجية العودة للمتوسط",
                "category": "strategy", 
                "difficulty": "intermediate",
                "content": """
                استراتيجية العودة للمتوسط تفترض أن الأسعار تعود لمتوسطها:
                
                المفهوم:
                - الأسعار تتأرجح حول متوسط
                - الانحراف الكبير يتبعه عودة
                - نشتري عند الانخفاض ونبيع عند الارتفاع
                
                المؤشرات المستخدمة:
                - Bollinger Bands
                - RSI
                - Mean Reversion Indicator
                
                إشارات الشراء:
                - السعر يلمس النطاق السفلي لبولينجر
                - RSI أقل من 30
                - انحراف كبير عن المتوسط المتحرك
                
                إشارات البيع:
                - السعر يلمس النطاق العلوي لبولينجر
                - RSI أكبر من 70
                - السعر بعيد فوق المتوسط
                
                إدارة المخاطر:
                - وقف خسارة عند كسر المتوسط المتحرك
                - أهداف ربح عند المتوسط أو النطاق المقابل
                - تجنب الأسواق ذات الاتجاه القوي
                """
            }
        }
        
        # حفظ المعرفة في الملفات
        self.save_knowledge_to_files(trading_books, "books")
        self.save_knowledge_to_files(trading_strategies, "strategies")
        
        # إضافة للذاكرة
        for doc_id, doc_data in {**trading_books, **trading_strategies}.items():
            document = TradingDocument(
                id=doc_id,
                title=doc_data["title"],
                content=doc_data["content"],
                category=doc_data["category"],
                author=doc_data.get("author", "Unknown"),
                source="core_knowledge",
                difficulty=doc_data["difficulty"],
                tags=self.extract_tags(doc_data["content"])
            )
            self.documents[doc_id] = document
    
    def save_knowledge_to_files(self, knowledge_dict: Dict, subfolder: str):
        """حفظ المعرفة في ملفات"""
        for doc_id, doc_data in knowledge_dict.items():
            file_path = f"{self.knowledge_dir}/{subfolder}/{doc_id}.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(doc_data, f, ensure_ascii=False, indent=2)
    
    def extract_tags(self, content: str) -> List[str]:
        """استخراج العلامات من المحتوى"""
        # كلمات مفتاحية مهمة في التداول
        keywords = [
            'rsi', 'macd', 'bollinger', 'sma', 'ema', 'fibonacci',
            'support', 'resistance', 'breakout', 'trend', 'volume',
            'candlestick', 'pattern', 'strategy', 'risk', 'management',
            'psychology', 'discipline', 'money', 'position', 'stop',
            'profit', 'loss', 'market', 'trading', 'analysis'
        ]
        
        content_lower = content.lower()
        found_tags = []
        
        for keyword in keywords:
            if keyword in content_lower:
                found_tags.append(keyword)
        
        return found_tags
    
    def add_document(self, document: TradingDocument):
        """إضافة وثيقة جديدة"""
        self.documents[document.id] = document
        
        # إضافة للقاعدة الشعاعية إذا كانت متاحة
        if self.collection:
            try:
                self.collection.add(
                    documents=[document.content],
                    metadatas=[{
                        "title": document.title,
                        "author": document.author,
                        "category": document.category,
                        "difficulty": document.difficulty,
                        "source": document.source
                    }],
                    ids=[document.id]
                )
            except Exception as e:
                logger.error(f"❌ Error adding to vector DB: {str(e)}")
    
    def search_knowledge(self, query: str, top_k: int = 5) -> List[Dict]:
        """البحث في قاعدة المعرفة"""
        results = []
        
        # البحث الشعاعي إذا كان متاحاً
        if self.collection:
            try:
                vector_results = self.collection.query(
                    query_texts=[query],
                    n_results=top_k
                )
                
                for i, doc_id in enumerate(vector_results['ids'][0]):
                    if doc_id in self.documents:
                        doc = self.documents[doc_id]
                        results.append({
                            'document': doc,
                            'score': 1.0 - vector_results['distances'][0][i],
                            'source': 'vector_search'
                        })
                
            except Exception as e:
                logger.error(f"❌ Vector search error: {str(e)}")
        
        # البحث النصي كبديل
        if not results:
            results = self.text_search(query, top_k)
        
        return results
    
    def text_search(self, query: str, top_k: int = 5) -> List[Dict]:
        """البحث النصي البسيط"""
        query_lower = query.lower()
        query_words = query_lower.split()
        
        scored_docs = []
        
        for doc_id, doc in self.documents.items():
            score = 0.0
            content_lower = doc.content.lower()
            title_lower = doc.title.lower()
            
            # نقاط للعنوان
            for word in query_words:
                if word in title_lower:
                    score += 3.0
            
            # نقاط للمحتوى
            for word in query_words:
                score += content_lower.count(word) * 1.0
            
            # نقاط للعلامات
            for word in query_words:
                if word in doc.tags:
                    score += 2.0
            
            if score > 0:
                scored_docs.append({
                    'document': doc,
                    'score': score,
                    'source': 'text_search'
                })
        
        # ترتيب حسب النقاط
        scored_docs.sort(key=lambda x: x['score'], reverse=True)
        
        return scored_docs[:top_k]
    
    def get_document_by_category(self, category: str) -> List[TradingDocument]:
        """جلب الوثائق حسب الفئة"""
        return [doc for doc in self.documents.values() if doc.category == category]
    
    def get_document_by_difficulty(self, difficulty: str) -> List[TradingDocument]:
        """جلب الوثائق حسب مستوى الصعوبة"""
        return [doc for doc in self.documents.values() if doc.difficulty == difficulty]

async def main():
    """دالة الاختبار"""
    print("📚 Advanced Trading Knowledge Base Test")
    print("=" * 60)
    
    # إنشاء قاعدة المعرفة
    kb = AdvancedTradingKnowledgeBase()
    
    # أسئلة تجريبية
    test_queries = [
        "ما هو مؤشر RSI وكيف أستخدمه؟",
        "شرح لي استراتيجية الكسر",
        "كيف أدير علم نفس التداول؟",
        "ما هي الشموع اليابانية؟"
    ]
    
    for query in test_queries:
        print(f"\n🔍 البحث عن: {query}")
        print("-" * 40)
        
        results = kb.search_knowledge(query, top_k=2)
        
        for i, result in enumerate(results, 1):
            doc = result['document']
            score = result['score']
            print(f"\n📄 النتيجة {i} (نقاط: {score:.2f}):")
            print(f"العنوان: {doc.title}")
            print(f"المؤلف: {doc.author}")
            print(f"الفئة: {doc.category}")
            print(f"المحتوى: {doc.content[:200]}...")
    
    print(f"\n✅ تم اختبار قاعدة المعرفة!")
    print(f"📊 إجمالي الوثائق: {len(kb.documents)}")

if __name__ == "__main__":
    asyncio.run(main())
