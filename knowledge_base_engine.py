#!/usr/bin/env python3
"""
🧠 Trading Knowledge Base Engine
محرك قاعدة المعرفة للتداول

المرحلة 7: تحويل الكتب إلى قاعدة معرفة
- تحويل كتب التداول إلى embeddings
- البحث الدلالي في المعرفة
- الإجابة على أسئلة التداول
- دمج مع تحليل السوق
"""

import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import hashlib

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TradingKnowledgeBase:
    """قاعدة المعرفة للتداول"""
    
    def __init__(self, collection_name: str = "trading_knowledge"):
        """تهيئة قاعدة المعرفة"""
        self.collection_name = collection_name
        self.knowledge_dir = "knowledge_data"
        self.embeddings_dir = "embeddings"
        self.create_directories()
        
        # محتوى تداول تجريبي
        self.trading_knowledge = self.load_trading_knowledge()
        
    def create_directories(self):
        """إنشاء مجلدات قاعدة المعرفة"""
        directories = [self.knowledge_dir, self.embeddings_dir, "queries", "responses"]
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"📁 Created directory: {directory}")
    
    def load_trading_knowledge(self) -> List[Dict]:
        """تحميل المعرفة التداولية"""
        knowledge_base = [
            {
                "id": "rsi_basics",
                "title": "مؤشر القوة النسبية RSI",
                "content": """
                مؤشر القوة النسبية (RSI) هو مؤشر زخم يقيس سرعة وتغيير حركات الأسعار.
                يتراوح RSI من 0 إلى 100 ويستخدم عادة لتحديد ظروف التشبع الشرائي والبيعي.
                
                القواعد الأساسية:
                - RSI أقل من 30: منطقة التشبع البيعي (إشارة شراء محتملة)
                - RSI أكبر من 70: منطقة التشبع الشرائي (إشارة بيع محتملة)
                - RSI بين 30-70: منطقة محايدة
                
                الاستراتيجيات:
                1. شراء عند RSI < 30 وبداية الارتفاع
                2. بيع عند RSI > 70 وبداية الانخفاض
                3. البحث عن التباعد بين RSI والسعر
                """,
                "category": "technical_indicators",
                "author": "John Murphy",
                "difficulty": "beginner"
            },
            {
                "id": "macd_strategy",
                "title": "استراتيجية MACD",
                "content": """
                MACD (Moving Average Convergence Divergence) هو مؤشر اتجاه يتبع الزخم.
                يتكون من خط MACD وخط الإشارة والهيستوجرام.
                
                مكونات MACD:
                - خط MACD: الفرق بين EMA 12 و EMA 26
                - خط الإشارة: EMA 9 لخط MACD
                - الهيستوجرام: الفرق بين خط MACD وخط الإشارة
                
                إشارات التداول:
                1. عبور صاعد: خط MACD يعبر فوق خط الإشارة (شراء)
                2. عبور هابط: خط MACD يعبر تحت خط الإشارة (بيع)
                3. عبور خط الصفر: تأكيد الاتجاه
                4. التباعد: اختلاف اتجاه MACD عن السعر
                """,
                "category": "technical_indicators",
                "author": "Gerald Appel",
                "difficulty": "intermediate"
            },
            {
                "id": "support_resistance",
                "title": "الدعم والمقاومة",
                "content": """
                الدعم والمقاومة هما من أهم المفاهيم في التحليل الفني.
                
                الدعم (Support):
                - مستوى سعري يميل السعر للارتداد منه صعوداً
                - يمثل منطقة طلب قوية
                - كلما اختُبر أكثر، كلما أصبح أقوى
                
                المقاومة (Resistance):
                - مستوى سعري يميل السعر للارتداد منه هبوطاً
                - يمثل منطقة عرض قوية
                - تصبح دعماً عند كسرها
                
                استراتيجيات التداول:
                1. الشراء عند الدعم مع وقف خسارة تحته
                2. البيع عند المقاومة مع وقف خسارة فوقها
                3. كسر الدعم = استمرار الهبوط
                4. كسر المقاومة = استمرار الصعود
                """,
                "category": "price_action",
                "author": "Charles Dow",
                "difficulty": "beginner"
            },
            {
                "id": "risk_management",
                "title": "إدارة المخاطر",
                "content": """
                إدارة المخاطر هي أهم جانب في التداول الناجح.
                
                قواعد إدارة المخاطر:
                1. لا تخاطر بأكثر من 1-2% من رأس المال في صفقة واحدة
                2. استخدم وقف الخسارة دائماً
                3. نسبة المخاطرة للمكافأة يجب أن تكون 1:2 على الأقل
                4. تنويع المحفظة عبر أصول مختلفة
                
                حساب حجم المركز:
                حجم المركز = (رأس المال × نسبة المخاطرة) ÷ (سعر الدخول - وقف الخسارة)
                
                أنواع وقف الخسارة:
                - وقف ثابت: مستوى سعري محدد
                - وقف متحرك: يتحرك مع السعر
                - وقف زمني: إغلاق بعد فترة محددة
                """,
                "category": "risk_management",
                "author": "Van Tharp",
                "difficulty": "advanced"
            },
            {
                "id": "gold_trading",
                "title": "تداول الذهب",
                "content": """
                الذهب هو أحد أهم الأصول الآمنة في الأسواق المالية.
                
                خصائص تداول الذهب:
                - يرتفع في أوقات عدم اليقين الاقتصادي
                - يتأثر بقوة الدولار الأمريكي عكسياً
                - يتأثر بأسعار الفائدة والتضخم
                - ساعات التداول: 24 ساعة تقريباً
                
                العوامل المؤثرة:
                1. السياسة النقدية للبنوك المركزية
                2. التضخم ومؤشرات الاقتصاد الكلي
                3. الأحداث الجيوسياسية
                4. قوة الدولار الأمريكي
                5. الطلب الصناعي والاستثماري
                
                استراتيجيات التداول:
                - شراء في أوقات الأزمات
                - بيع عند تحسن المعنويات
                - متابعة مستويات الدعم والمقاومة النفسية
                """,
                "category": "commodities",
                "author": "Jim Rogers",
                "difficulty": "intermediate"
            },
            {
                "id": "market_psychology",
                "title": "علم نفس السوق",
                "content": """
                علم نفس التداول يلعب دوراً حاسماً في نجاح المتداول.
                
                المشاعر الشائعة:
                1. الخوف (Fear): يؤدي للبيع في القاع
                2. الطمع (Greed): يؤدي للشراء في القمة
                3. الأمل (Hope): التمسك بالصفقات الخاسرة
                4. الندم (Regret): اتخاذ قرارات متسرعة
                
                دورة المشاعر:
                تفاؤل → إثارة → نشوة → قلق → إنكار → خوف → يأس → ذعر → استسلام
                
                كيفية التحكم:
                - وضع خطة تداول واضحة
                - الالتزام بقواعد إدارة المخاطر
                - تجنب التداول العاطفي
                - أخذ فترات راحة عند الخسائر
                - التعلم المستمر والتطوير
                """,
                "category": "psychology",
                "author": "Mark Douglas",
                "difficulty": "advanced"
            }
        ]
        
        return knowledge_base
    
    def create_simple_embeddings(self, text: str) -> List[float]:
        """إنشاء embeddings مبسطة (محاكاة)"""
        # في التطبيق الحقيقي، نستخدم sentence-transformers أو OpenAI
        # هنا سنستخدم hash بسيط للمحاكاة
        
        words = text.lower().split()
        
        # إنشاء vector بسيط بناءً على الكلمات المفتاحية
        keywords = {
            'rsi': 0.1, 'macd': 0.2, 'support': 0.3, 'resistance': 0.4,
            'buy': 0.5, 'sell': 0.6, 'gold': 0.7, 'risk': 0.8,
            'strategy': 0.9, 'trading': 1.0, 'price': 0.15, 'market': 0.25
        }
        
        embedding = [0.0] * 100  # vector بحجم 100
        
        for i, word in enumerate(words[:100]):
            if word in keywords:
                embedding[i] = keywords[word]
            else:
                # استخدام hash للكلمات الأخرى
                hash_val = hash(word) % 100
                embedding[hash_val % len(embedding)] = (hash_val % 100) / 100.0
        
        return embedding
    
    def search_knowledge(self, query: str, top_k: int = 3) -> List[Dict]:
        """البحث في قاعدة المعرفة"""
        logger.info(f"🔍 Searching for: {query}")
        
        query_lower = query.lower()
        results = []
        
        # بحث بسيط بناءً على الكلمات المفتاحية
        for item in self.trading_knowledge:
            score = 0.0
            content_lower = item['content'].lower()
            title_lower = item['title'].lower()
            
            # حساب النقاط بناءً على وجود كلمات الاستعلام
            query_words = query_lower.split()
            for word in query_words:
                if word in title_lower:
                    score += 3.0  # وزن أعلى للعنوان
                if word in content_lower:
                    score += 1.0
                if word in item['category']:
                    score += 2.0
            
            if score > 0:
                results.append({
                    'item': item,
                    'score': score,
                    'relevance': min(score / len(query_words), 1.0)
                })
        
        # ترتيب النتائج حسب النقاط
        results.sort(key=lambda x: x['score'], reverse=True)
        
        return results[:top_k]
    
    def generate_answer(self, query: str, context: List[Dict]) -> str:
        """توليد إجابة بناءً على السياق"""
        if not context:
            return "عذراً، لم أجد معلومات ذات صلة بسؤالك في قاعدة المعرفة."
        
        # دمج المعلومات من أفضل النتائج
        answer_parts = []
        
        for result in context:
            item = result['item']
            relevance = result['relevance']
            
            if relevance > 0.3:  # عتبة الصلة
                answer_parts.append(f"**{item['title']}** (صلة: {relevance:.1%})")
                
                # استخراج الجزء الأكثر صلة
                content_lines = item['content'].strip().split('\n')
                relevant_lines = []
                
                query_words = query.lower().split()
                for line in content_lines:
                    line = line.strip()
                    if line and any(word in line.lower() for word in query_words):
                        relevant_lines.append(line)
                
                if relevant_lines:
                    answer_parts.append('\n'.join(relevant_lines[:3]))  # أول 3 أسطر ذات صلة
                else:
                    # إذا لم نجد أسطر محددة، نأخذ بداية المحتوى
                    answer_parts.append('\n'.join(content_lines[:2]))
                
                answer_parts.append("")  # سطر فارغ
        
        if answer_parts:
            final_answer = '\n'.join(answer_parts)
            return final_answer
        else:
            return "وجدت معلومات ذات صلة ولكنها قد لا تجيب على سؤالك بشكل مباشر."
    
    def ask_question(self, question: str) -> Dict:
        """طرح سؤال على قاعدة المعرفة"""
        logger.info(f"❓ Question: {question}")
        
        # البحث في قاعدة المعرفة
        search_results = self.search_knowledge(question)
        
        # توليد الإجابة
        answer = self.generate_answer(question, search_results)
        
        # إنشاء الاستجابة
        response = {
            'question': question,
            'answer': answer,
            'sources': [
                {
                    'title': result['item']['title'],
                    'category': result['item']['category'],
                    'author': result['item']['author'],
                    'relevance': result['relevance']
                }
                for result in search_results
            ],
            'timestamp': datetime.now().isoformat(),
            'confidence': max([r['relevance'] for r in search_results]) if search_results else 0.0
        }
        
        # حفظ السؤال والإجابة
        self.save_qa_pair(response)
        
        return response
    
    def save_qa_pair(self, qa_pair: Dict):
        """حفظ زوج السؤال والإجابة"""
        try:
            filename = f"queries/qa_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(qa_pair, f, ensure_ascii=False, indent=2)
            logger.info(f"💾 Q&A saved to {filename}")
        except Exception as e:
            logger.error(f"❌ Error saving Q&A: {str(e)}")
    
    def get_trading_recommendations(self, symbol: str) -> Dict:
        """الحصول على توصيات تداول من قاعدة المعرفة"""
        if symbol.upper() == 'GOLD':
            question = "ما هي أفضل استراتيجيات تداول الذهب؟"
        else:
            question = f"ما هي أفضل المؤشرات الفنية لتداول {symbol}؟"
        
        return self.ask_question(question)
    
    def display_answer(self, response: Dict):
        """عرض الإجابة"""
        print(f"\n{'='*60}")
        print(f"❓ السؤال: {response['question']}")
        print(f"{'='*60}")
        print(f"🤖 الإجابة:")
        print(response['answer'])
        
        if response['sources']:
            print(f"\n📚 المصادر:")
            for source in response['sources']:
                print(f"  • {source['title']} ({source['category']}) - {source['relevance']:.1%}")
        
        print(f"\n🎯 مستوى الثقة: {response['confidence']:.1%}")

def main():
    """الدالة الرئيسية"""
    print("🧠 Trading Knowledge Base Engine")
    print("=" * 60)
    
    # إنشاء قاعدة المعرفة
    kb = TradingKnowledgeBase()
    
    # أسئلة تجريبية
    questions = [
        "ما هو مؤشر RSI وكيف أستخدمه؟",
        "كيف أتداول الذهب بأمان؟",
        "ما هي قواعد إدارة المخاطر؟",
        "متى أشتري ومتى أبيع؟"
    ]
    
    for question in questions:
        try:
            response = kb.ask_question(question)
            kb.display_answer(response)
            print("\n" + "-"*60)
        except Exception as e:
            logger.error(f"❌ Error processing question: {str(e)}")
    
    print(f"\n🎉 Knowledge Base Demo Completed!")

if __name__ == "__main__":
    main()
