version: '3.8'

services:
  # 🐘 PostgreSQL - بيانات المستخدمين والإشارات
  postgres:
    image: postgres:15-alpine
    container_name: ai_trading_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - trading_network
    restart: unless-stopped

  # 🍃 MongoDB - المحادثات والبيانات غير المنظمة
  mongodb:
    image: mongo:7.0
    container_name: ai_trading_mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD}
    ports:
      - "${MONGO_PORT}:27017"
    volumes:
      - mongodb_data:/data/db
      - ./database/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - trading_network
    restart: unless-stopped

  # 🔴 Redis - كاش البيانات الفورية
  redis:
    image: redis:7-alpine
    container_name: ai_trading_redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
    networks:
      - trading_network
    restart: unless-stopped

  # 📊 InfluxDB - بيانات السوق الزمنية
  influxdb:
    image: influxdb:2.7-alpine
    container_name: ai_trading_influxdb
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: ${INFLUXDB_ADMIN_USER}
      DOCKER_INFLUXDB_INIT_PASSWORD: ${INFLUXDB_ADMIN_PASSWORD}
      DOCKER_INFLUXDB_INIT_ORG: ai-trading
      DOCKER_INFLUXDB_INIT_BUCKET: market-data
    ports:
      - "${INFLUXDB_PORT}:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
    networks:
      - trading_network
    restart: unless-stopped

  # 🌐 Adminer - إدارة قواعد البيانات
  adminer:
    image: adminer:latest
    container_name: ai_trading_adminer
    ports:
      - "8080:8080"
    networks:
      - trading_network
    restart: unless-stopped
    depends_on:
      - postgres
      - mongodb

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
  influxdb_data:

networks:
  trading_network:
    driver: bridge
