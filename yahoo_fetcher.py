#!/usr/bin/env python3
"""
📊 Yahoo Finance Data Fetcher
جالب بيانات Yahoo Finance

المرحلة 4: جلب البيانات المالية الحقيقية
- جلب بيانات الأسهم العالمية
- جلب بيانات العملات والسلع
- حفظ البيانات في ملفات CSV
- معالجة الأخطاء والاستثناءات
"""

import yfinance as yf
import pandas as pd
import os
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import time

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('yahoo_fetcher.log'),
        logging.StreamHandler()
    ]
)

class YahooFinanceFetcher:
    """فئة جلب البيانات من Yahoo Finance"""
    
    def __init__(self, data_dir: str = "data"):
        """
        تهيئة جالب البيانات
        
        Args:
            data_dir: مجلد حفظ البيانات
        """
        self.data_dir = data_dir
        self.create_data_directory()
        
        # رموز الأصول المالية المهمة
        self.symbols = {
            'stocks': [
                'AAPL',    # Apple
                'GOOGL',   # Google
                'MSFT',    # Microsoft
                'TSLA',    # Tesla
                'AMZN',    # Amazon
                'NVDA',    # NVIDIA
                'META',    # Meta (Facebook)
                'NFLX',    # Netflix
            ],
            'forex': [
                'EURUSD=X',  # Euro/USD
                'GBPUSD=X',  # GBP/USD
                'USDJPY=X',  # USD/JPY
                'USDCHF=X',  # USD/CHF
                'AUDUSD=X',  # AUD/USD
                'USDCAD=X',  # USD/CAD
            ],
            'commodities': [
                'GC=F',      # Gold
                'SI=F',      # Silver
                'CL=F',      # Crude Oil
                'NG=F',      # Natural Gas
                'HG=F',      # Copper
            ],
            'crypto': [
                'BTC-USD',   # Bitcoin
                'ETH-USD',   # Ethereum
                'BNB-USD',   # Binance Coin
                'ADA-USD',   # Cardano
                'SOL-USD',   # Solana
            ],
            'indices': [
                '^GSPC',     # S&P 500
                '^DJI',      # Dow Jones
                '^IXIC',     # NASDAQ
                '^FTSE',     # FTSE 100
                '^N225',     # Nikkei 225
            ]
        }
    
    def create_data_directory(self):
        """إنشاء مجلد البيانات"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            logging.info(f"✅ تم إنشاء مجلد البيانات: {self.data_dir}")
    
    def fetch_single_symbol(self, symbol: str, period: str = "1y", 
                           interval: str = "1d") -> Optional[pd.DataFrame]:
        """
        جلب بيانات رمز واحد
        
        Args:
            symbol: رمز الأصل المالي
            period: الفترة الزمنية (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            interval: الفاصل الزمني (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
        
        Returns:
            DataFrame مع بيانات السعر أو None في حالة الخطأ
        """
        try:
            logging.info(f"📊 جلب بيانات {symbol}...")
            
            # إنشاء كائن Ticker
            ticker = yf.Ticker(symbol)
            
            # جلب البيانات التاريخية
            data = ticker.history(period=period, interval=interval)
            
            if data.empty:
                logging.warning(f"⚠️ لا توجد بيانات لـ {symbol}")
                return None
            
            # إضافة معلومات إضافية
            data['Symbol'] = symbol
            data['Timestamp'] = data.index
            
            # إعادة ترتيب الأعمدة
            columns_order = ['Symbol', 'Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume']
            available_columns = [col for col in columns_order if col in data.columns]
            data = data[available_columns]
            
            logging.info(f"✅ تم جلب {len(data)} سجل لـ {symbol}")
            return data
            
        except Exception as e:
            logging.error(f"❌ خطأ في جلب بيانات {symbol}: {str(e)}")
            return None
    
    def fetch_multiple_symbols(self, symbols: List[str], period: str = "1y") -> Dict[str, pd.DataFrame]:
        """
        جلب بيانات عدة رموز
        
        Args:
            symbols: قائمة برموز الأصول المالية
            period: الفترة الزمنية
        
        Returns:
            قاموس يحتوي على البيانات لكل رمز
        """
        results = {}
        
        for symbol in symbols:
            data = self.fetch_single_symbol(symbol, period)
            if data is not None:
                results[symbol] = data
            
            # توقف قصير لتجنب تحديد المعدل
            time.sleep(0.1)
        
        return results
    
    def save_to_csv(self, data: pd.DataFrame, symbol: str, category: str = ""):
        """
        حفظ البيانات في ملف CSV
        
        Args:
            data: البيانات المراد حفظها
            symbol: رمز الأصل المالي
            category: فئة الأصل (stocks, forex, etc.)
        """
        try:
            # إنشاء مجلد الفئة إذا لم يكن موجوداً
            if category:
                category_dir = os.path.join(self.data_dir, category)
                if not os.path.exists(category_dir):
                    os.makedirs(category_dir)
                file_path = os.path.join(category_dir, f"{symbol}.csv")
            else:
                file_path = os.path.join(self.data_dir, f"{symbol}.csv")
            
            # حفظ البيانات
            data.to_csv(file_path, index=False)
            logging.info(f"💾 تم حفظ بيانات {symbol} في {file_path}")
            
        except Exception as e:
            logging.error(f"❌ خطأ في حفظ بيانات {symbol}: {str(e)}")
    
    def fetch_all_categories(self, period: str = "1y"):
        """
        جلب بيانات جميع الفئات
        
        Args:
            period: الفترة الزمنية
        """
        logging.info("🚀 بدء جلب البيانات لجميع الفئات...")
        
        total_symbols = 0
        successful_downloads = 0
        
        for category, symbols in self.symbols.items():
            logging.info(f"📂 جلب بيانات فئة: {category}")
            
            results = self.fetch_multiple_symbols(symbols, period)
            
            for symbol, data in results.items():
                self.save_to_csv(data, symbol, category)
                successful_downloads += 1
            
            total_symbols += len(symbols)
            
            logging.info(f"✅ تم الانتهاء من فئة {category}: {len(results)}/{len(symbols)} رمز")
        
        logging.info(f"🎉 تم الانتهاء من جلب البيانات: {successful_downloads}/{total_symbols} رمز")
    
    def get_latest_price(self, symbol: str) -> Optional[Dict]:
        """
        جلب آخر سعر لرمز معين
        
        Args:
            symbol: رمز الأصل المالي
        
        Returns:
            قاموس يحتوي على معلومات السعر الحالي
        """
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            current_price = info.get('currentPrice') or info.get('regularMarketPrice')
            
            if current_price:
                return {
                    'symbol': symbol,
                    'current_price': current_price,
                    'currency': info.get('currency', 'USD'),
                    'market_cap': info.get('marketCap'),
                    'volume': info.get('volume'),
                    'timestamp': datetime.now().isoformat()
                }
            
        except Exception as e:
            logging.error(f"❌ خطأ في جلب السعر الحالي لـ {symbol}: {str(e)}")
        
        return None

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء جلب البيانات المالية من Yahoo Finance")
    
    # إنشاء جالب البيانات
    fetcher = YahooFinanceFetcher()
    
    # جلب بيانات جميع الفئات
    fetcher.fetch_all_categories(period="1y")
    
    # مثال على جلب السعر الحالي
    print("\n📊 أسعار حالية لبعض الأصول:")
    sample_symbols = ['AAPL', 'GC=F', 'BTC-USD', 'EURUSD=X']
    
    for symbol in sample_symbols:
        price_info = fetcher.get_latest_price(symbol)
        if price_info:
            print(f"💰 {symbol}: ${price_info['current_price']}")

if __name__ == "__main__":
    main()
