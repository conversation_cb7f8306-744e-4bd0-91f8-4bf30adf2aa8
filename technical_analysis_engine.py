#!/usr/bin/env python3
"""
📊 Technical Analysis Engine
محرك التحليل الفني

المرحلة 5: بناء نموذج التحليل والتوصية
- تحليل فني شامل باستخدام مؤشرات متعددة
- توليد إشارات التداول الذكية
- تقييم قوة الإشارات
- حفظ التوصيات في قاعدة البيانات
"""

import pandas as pd
import numpy as np
import os
import sys
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
import warnings
warnings.filterwarnings('ignore')

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('technical_analysis.log'),
        logging.StreamHandler()
    ]
)

class TechnicalAnalysisEngine:
    """محرك التحليل الفني الشامل"""
    
    def __init__(self, data_path: str = "../data_ingestion"):
        """
        تهيئة محرك التحليل الفني
        
        Args:
            data_path: مسار مجلد البيانات
        """
        self.data_path = data_path
        self.signals_dir = "signals"
        self.reports_dir = "reports"
        self.create_directories()
        
        # إعدادات المؤشرات
        self.indicators_config = {
            'sma_periods': [10, 20, 50, 200],
            'ema_periods': [12, 26, 50],
            'rsi_period': 14,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'bb_period': 20,
            'bb_std': 2,
            'stoch_k': 14,
            'stoch_d': 3
        }
        
        # عتبات الإشارات
        self.signal_thresholds = {
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'macd_bullish': 0,
            'macd_bearish': 0,
            'bb_oversold': 0.1,
            'bb_overbought': 0.9,
            'volume_spike': 1.5
        }
    
    def create_directories(self):
        """إنشاء مجلدات النتائج"""
        for directory in [self.signals_dir, self.reports_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logging.info(f"✅ تم إنشاء مجلد: {directory}")
    
    def load_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        تحميل بيانات رمز معين
        
        Args:
            symbol: رمز الأصل المالي
        
        Returns:
            DataFrame مع البيانات أو None
        """
        try:
            # البحث عن الملف
            possible_files = [
                f"{self.data_path}/{symbol}.csv",
                f"{self.data_path}/{symbol.replace('=', '_').replace('-', '_')}.csv"
            ]
            
            file_path = None
            for path in possible_files:
                if os.path.exists(path):
                    file_path = path
                    break
            
            if not file_path:
                logging.warning(f"⚠️ لم يتم العثور على ملف البيانات لـ {symbol}")
                return None
            
            # تحميل البيانات
            df = pd.read_csv(file_path)
            
            # تحويل عمود التاريخ
            if 'Date' in df.columns:
                df['Date'] = pd.to_datetime(df['Date'])
                df.set_index('Date', inplace=True)
            
            # التأكد من وجود الأعمدة المطلوبة
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logging.error(f"❌ أعمدة مفقودة في {symbol}: {missing_columns}")
                return None
            
            logging.info(f"✅ تم تحميل بيانات {symbol}: {len(df)} سجل")
            return df
            
        except Exception as e:
            logging.error(f"❌ خطأ في تحميل بيانات {symbol}: {str(e)}")
            return None
    
    def calculate_sma(self, df: pd.DataFrame, periods: List[int]) -> pd.DataFrame:
        """حساب المتوسطات المتحركة البسيطة"""
        for period in periods:
            df[f'SMA_{period}'] = df['Close'].rolling(window=period).mean()
        return df
    
    def calculate_ema(self, df: pd.DataFrame, periods: List[int]) -> pd.DataFrame:
        """حساب المتوسطات المتحركة الأسية"""
        for period in periods:
            df[f'EMA_{period}'] = df['Close'].ewm(span=period).mean()
        return df
    
    def calculate_rsi(self, df: pd.DataFrame, period: int = 14) -> pd.DataFrame:
        """حساب مؤشر القوة النسبية RSI"""
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        return df
    
    def calculate_macd(self, df: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.DataFrame:
        """حساب مؤشر MACD"""
        ema_fast = df['Close'].ewm(span=fast).mean()
        ema_slow = df['Close'].ewm(span=slow).mean()
        df['MACD'] = ema_fast - ema_slow
        df['MACD_Signal'] = df['MACD'].ewm(span=signal).mean()
        df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']
        return df
    
    def calculate_bollinger_bands(self, df: pd.DataFrame, period: int = 20, std: int = 2) -> pd.DataFrame:
        """حساب نطاقات بولينجر"""
        sma = df['Close'].rolling(window=period).mean()
        std_dev = df['Close'].rolling(window=period).std()
        df['BB_Upper'] = sma + (std_dev * std)
        df['BB_Lower'] = sma - (std_dev * std)
        df['BB_Middle'] = sma
        df['BB_Width'] = df['BB_Upper'] - df['BB_Lower']
        df['BB_Position'] = (df['Close'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
        return df
    
    def calculate_stochastic(self, df: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> pd.DataFrame:
        """حساب مؤشر Stochastic"""
        low_min = df['Low'].rolling(window=k_period).min()
        high_max = df['High'].rolling(window=k_period).max()
        df['Stoch_K'] = 100 * (df['Close'] - low_min) / (high_max - low_min)
        df['Stoch_D'] = df['Stoch_K'].rolling(window=d_period).mean()
        return df
    
    def calculate_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """حساب مؤشرات الحجم"""
        df['Volume_SMA'] = df['Volume'].rolling(window=20).mean()
        df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA']
        df['Price_Volume'] = df['Close'] * df['Volume']
        return df
    
    def calculate_all_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """حساب جميع المؤشرات الفنية"""
        logging.info("📊 حساب المؤشرات الفنية...")
        
        # المتوسطات المتحركة
        df = self.calculate_sma(df, self.indicators_config['sma_periods'])
        df = self.calculate_ema(df, self.indicators_config['ema_periods'])
        
        # مؤشرات الزخم
        df = self.calculate_rsi(df, self.indicators_config['rsi_period'])
        df = self.calculate_macd(df, 
                               self.indicators_config['macd_fast'],
                               self.indicators_config['macd_slow'],
                               self.indicators_config['macd_signal'])
        
        # نطاقات بولينجر
        df = self.calculate_bollinger_bands(df,
                                          self.indicators_config['bb_period'],
                                          self.indicators_config['bb_std'])
        
        # Stochastic
        df = self.calculate_stochastic(df,
                                     self.indicators_config['stoch_k'],
                                     self.indicators_config['stoch_d'])
        
        # مؤشرات الحجم
        df = self.calculate_volume_indicators(df)
        
        logging.info("✅ تم حساب جميع المؤشرات الفنية")
        return df
    
    def generate_signals(self, df: pd.DataFrame, symbol: str) -> Dict:
        """
        توليد إشارات التداول
        
        Args:
            df: البيانات مع المؤشرات
            symbol: رمز الأصل المالي
        
        Returns:
            قاموس يحتوي على الإشارات والتوصيات
        """
        if df.empty or len(df) < 50:
            return {'error': 'بيانات غير كافية للتحليل'}
        
        latest = df.iloc[-1]
        previous = df.iloc[-2]
        
        signals = {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'current_price': float(latest['Close']),
            'signals': {},
            'overall_signal': 'HOLD',
            'confidence': 0,
            'recommendations': []
        }
        
        # إشارات RSI
        if latest['RSI'] < self.signal_thresholds['rsi_oversold']:
            signals['signals']['RSI'] = 'BUY'
            signals['recommendations'].append(f"RSI منخفض ({latest['RSI']:.1f}) - إشارة شراء")
        elif latest['RSI'] > self.signal_thresholds['rsi_overbought']:
            signals['signals']['RSI'] = 'SELL'
            signals['recommendations'].append(f"RSI مرتفع ({latest['RSI']:.1f}) - إشارة بيع")
        else:
            signals['signals']['RSI'] = 'NEUTRAL'
        
        # إشارات MACD
        if latest['MACD'] > latest['MACD_Signal'] and previous['MACD'] <= previous['MACD_Signal']:
            signals['signals']['MACD'] = 'BUY'
            signals['recommendations'].append("MACD عبر خط الإشارة للأعلى - إشارة شراء")
        elif latest['MACD'] < latest['MACD_Signal'] and previous['MACD'] >= previous['MACD_Signal']:
            signals['signals']['MACD'] = 'SELL'
            signals['recommendations'].append("MACD عبر خط الإشارة للأسفل - إشارة بيع")
        else:
            signals['signals']['MACD'] = 'NEUTRAL'
        
        # إشارات نطاقات بولينجر
        if latest['BB_Position'] < self.signal_thresholds['bb_oversold']:
            signals['signals']['BB'] = 'BUY'
            signals['recommendations'].append("السعر قرب النطاق السفلي - إشارة شراء محتملة")
        elif latest['BB_Position'] > self.signal_thresholds['bb_overbought']:
            signals['signals']['BB'] = 'SELL'
            signals['recommendations'].append("السعر قرب النطاق العلوي - إشارة بيع محتملة")
        else:
            signals['signals']['BB'] = 'NEUTRAL'
        
        # إشارات المتوسطات المتحركة
        if latest['Close'] > latest['SMA_20'] > latest['SMA_50']:
            signals['signals']['MA'] = 'BUY'
            signals['recommendations'].append("السعر فوق المتوسطات المتحركة - اتجاه صاعد")
        elif latest['Close'] < latest['SMA_20'] < latest['SMA_50']:
            signals['signals']['MA'] = 'SELL'
            signals['recommendations'].append("السعر تحت المتوسطات المتحركة - اتجاه هابط")
        else:
            signals['signals']['MA'] = 'NEUTRAL'
        
        # إشارات الحجم
        if latest['Volume_Ratio'] > self.signal_thresholds['volume_spike']:
            signals['signals']['Volume'] = 'HIGH'
            signals['recommendations'].append(f"حجم تداول مرتفع ({latest['Volume_Ratio']:.1f}x) - نشاط قوي")
        else:
            signals['signals']['Volume'] = 'NORMAL'
        
        # حساب الإشارة الإجمالية والثقة
        buy_signals = sum(1 for s in signals['signals'].values() if s == 'BUY')
        sell_signals = sum(1 for s in signals['signals'].values() if s == 'SELL')
        total_signals = len([s for s in signals['signals'].values() if s in ['BUY', 'SELL']])
        
        if buy_signals > sell_signals and buy_signals >= 2:
            signals['overall_signal'] = 'BUY'
            signals['confidence'] = (buy_signals / total_signals) * 100 if total_signals > 0 else 0
        elif sell_signals > buy_signals and sell_signals >= 2:
            signals['overall_signal'] = 'SELL'
            signals['confidence'] = (sell_signals / total_signals) * 100 if total_signals > 0 else 0
        else:
            signals['overall_signal'] = 'HOLD'
            signals['confidence'] = 50
        
        return signals
    
    def save_signals(self, signals: Dict, symbol: str):
        """حفظ الإشارات في ملف"""
        try:
            filename = f"{self.signals_dir}/{symbol}_signals_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(signals, f, ensure_ascii=False, indent=2)
            logging.info(f"💾 تم حفظ الإشارات: {filename}")
        except Exception as e:
            logging.error(f"❌ خطأ في حفظ الإشارات: {str(e)}")
    
    def analyze_symbol(self, symbol: str) -> Optional[Dict]:
        """
        تحليل رمز واحد وتوليد التوصيات
        
        Args:
            symbol: رمز الأصل المالي
        
        Returns:
            قاموس يحتوي على التحليل والتوصيات
        """
        logging.info(f"🔍 بدء تحليل {symbol}...")
        
        # تحميل البيانات
        df = self.load_data(symbol)
        if df is None:
            return None
        
        # حساب المؤشرات
        df = self.calculate_all_indicators(df)
        
        # توليد الإشارات
        signals = self.generate_signals(df, symbol)
        
        # حفظ الإشارات
        self.save_signals(signals, symbol)
        
        logging.info(f"✅ تم الانتهاء من تحليل {symbol}")
        return signals

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء محرك التحليل الفني")
    print("=" * 60)
    
    # إنشاء محرك التحليل
    engine = TechnicalAnalysisEngine()
    
    # رموز للتحليل
    symbols = ['AAPL', 'GOLD']
    
    results = {}
    
    for symbol in symbols:
        print(f"\n📊 تحليل {symbol}...")
        result = engine.analyze_symbol(symbol)
        
        if result:
            results[symbol] = result
            
            # عرض النتائج
            print(f"💰 السعر الحالي: ${result['current_price']:.2f}")
            print(f"📈 الإشارة الإجمالية: {result['overall_signal']}")
            print(f"🎯 مستوى الثقة: {result['confidence']:.1f}%")
            print(f"📋 التوصيات:")
            for rec in result['recommendations']:
                print(f"  • {rec}")
        else:
            print(f"❌ فشل في تحليل {symbol}")
    
    print(f"\n🎉 تم الانتهاء من التحليل!")
    print(f"📁 تحقق من مجلد 'signals' للنتائج التفصيلية")

if __name__ == "__main__":
    main()
