/**
 * Advanced Trading Chart Component
 * مكون الرسم البياني المتقدم للتداول
 */

import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  IconButton, 
  Tooltip, 
  Menu, 
  MenuItem,
  FormControl,
  Select,
  Chip,
  Switch,
  FormControlLabel,
  Slider,
  Divider
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  TrendingUp as TrendingUpIcon,
  ShowChart as ShowChartIcon,
  Timeline as TimelineIcon,
  Insights as InsightsIcon
} from '@mui/icons-material';
import { createChart, ColorType, CrosshairMode, LineStyle } from 'lightweight-charts';
import { useTheme } from '@mui/material/styles';

// Custom hooks
import { useWebSocket } from '../../hooks/useWebSocket';
import { useMarketData } from '../../hooks/useMarketData';

const AdvancedChart = ({ 
  symbol = 'EURUSD',
  timeframe = '1h',
  height = 500,
  showVolume = true,
  showIndicators = true,
  onSymbolChange,
  onTimeframeChange 
}) => {
  const theme = useTheme();
  const chartContainerRef = useRef(null);
  const chartRef = useRef(null);
  const candlestickSeriesRef = useRef(null);
  const volumeSeriesRef = useRef(null);
  const indicatorSeriesRef = useRef({});
  
  // State
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [settingsAnchor, setSettingsAnchor] = useState(null);
  const [chartSettings, setChartSettings] = useState({
    chartType: 'candlestick', // 'candlestick', 'line', 'area'
    showGrid: true,
    showCrosshair: true,
    showVolume: true,
    indicators: {
      sma: { enabled: false, period: 20, color: '#ff6b35' },
      ema: { enabled: false, period: 20, color: '#4ecdc4' },
      rsi: { enabled: false, period: 14 },
      macd: { enabled: false },
      bollinger: { enabled: false, period: 20, stdDev: 2 }
    },
    colors: {
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderUpColor: '#26a69a',
      borderDownColor: '#ef5350',
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350'
    }
  });

  // Custom hooks
  const { marketData, isLoading } = useMarketData(symbol, timeframe);
  const { subscribe: subscribeWS } = useWebSocket();

  // Chart configuration
  const chartConfig = useMemo(() => ({
    layout: {
      background: { 
        type: ColorType.Solid, 
        color: theme.palette.background.paper 
      },
      textColor: theme.palette.text.primary,
      fontSize: 12,
      fontFamily: theme.typography.fontFamily,
    },
    grid: {
      vertLines: { 
        color: theme.palette.divider,
        style: LineStyle.Dotted,
        visible: chartSettings.showGrid
      },
      horzLines: { 
        color: theme.palette.divider,
        style: LineStyle.Dotted,
        visible: chartSettings.showGrid
      },
    },
    crosshair: {
      mode: chartSettings.showCrosshair ? CrosshairMode.Normal : CrosshairMode.Hidden,
      vertLine: {
        color: theme.palette.primary.main,
        width: 1,
        style: LineStyle.Dashed,
      },
      horzLine: {
        color: theme.palette.primary.main,
        width: 1,
        style: LineStyle.Dashed,
      },
    },
    rightPriceScale: {
      borderColor: theme.palette.divider,
      textColor: theme.palette.text.secondary,
    },
    timeScale: {
      borderColor: theme.palette.divider,
      textColor: theme.palette.text.secondary,
      timeVisible: true,
      secondsVisible: false,
    },
    handleScroll: {
      mouseWheel: true,
      pressedMouseMove: true,
      horzTouchDrag: true,
      vertTouchDrag: true,
    },
    handleScale: {
      axisPressedMouseMove: true,
      mouseWheel: true,
      pinch: true,
    },
  }), [theme, chartSettings]);

  // Initialize chart
  useEffect(() => {
    if (!chartContainerRef.current) return;

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      ...chartConfig,
      width: chartContainerRef.current.clientWidth,
      height: height,
    });

    chartRef.current = chart;

    // Create candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: chartSettings.colors.upColor,
      downColor: chartSettings.colors.downColor,
      borderUpColor: chartSettings.colors.borderUpColor,
      borderDownColor: chartSettings.colors.borderDownColor,
      wickUpColor: chartSettings.colors.wickUpColor,
      wickDownColor: chartSettings.colors.wickDownColor,
    });

    candlestickSeriesRef.current = candlestickSeries;

    // Create volume series if enabled
    if (chartSettings.showVolume) {
      const volumeSeries = chart.addHistogramSeries({
        color: theme.palette.primary.main,
        priceFormat: {
          type: 'volume',
        },
        priceScaleId: 'volume',
      });

      chart.priceScale('volume').applyOptions({
        scaleMargins: {
          top: 0.8,
          bottom: 0,
        },
      });

      volumeSeriesRef.current = volumeSeries;
    }

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chart) {
        chart.applyOptions({
          width: chartContainerRef.current.clientWidth,
          height: isFullscreen ? window.innerHeight - 100 : height,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chart) {
        chart.remove();
      }
    };
  }, [chartConfig, height, isFullscreen, chartSettings]);

  // Update chart data
  useEffect(() => {
    if (!marketData || !candlestickSeriesRef.current) return;

    try {
      // Format data for chart
      const formattedData = marketData.map(item => ({
        time: Math.floor(new Date(item.timestamp).getTime() / 1000),
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
      }));

      const volumeData = marketData.map(item => ({
        time: Math.floor(new Date(item.timestamp).getTime() / 1000),
        value: item.volume,
        color: item.close >= item.open ? 
          chartSettings.colors.upColor + '80' : 
          chartSettings.colors.downColor + '80',
      }));

      // Set data
      candlestickSeriesRef.current.setData(formattedData);
      
      if (volumeSeriesRef.current) {
        volumeSeriesRef.current.setData(volumeData);
      }

      // Fit content
      chartRef.current?.timeScale().fitContent();

    } catch (error) {
      console.error('Error updating chart data:', error);
    }
  }, [marketData, chartSettings.colors]);

  // Subscribe to real-time updates
  useEffect(() => {
    const unsubscribe = subscribeWS('market_data', (data) => {
      if (data.symbol === symbol && candlestickSeriesRef.current) {
        const newPoint = {
          time: Math.floor(new Date(data.timestamp).getTime() / 1000),
          open: data.open,
          high: data.high,
          low: data.low,
          close: data.close,
        };

        candlestickSeriesRef.current.update(newPoint);

        if (volumeSeriesRef.current) {
          volumeSeriesRef.current.update({
            time: newPoint.time,
            value: data.volume,
            color: data.close >= data.open ? 
              chartSettings.colors.upColor + '80' : 
              chartSettings.colors.downColor + '80',
          });
        }
      }
    });

    return unsubscribe;
  }, [symbol, subscribeWS, chartSettings.colors]);

  // Add technical indicators
  useEffect(() => {
    if (!chartRef.current || !marketData) return;

    // Clear existing indicators
    Object.values(indicatorSeriesRef.current).forEach(series => {
      chartRef.current.removeSeries(series);
    });
    indicatorSeriesRef.current = {};

    // Add enabled indicators
    Object.entries(chartSettings.indicators).forEach(([name, config]) => {
      if (config.enabled) {
        addIndicator(name, config);
      }
    });
  }, [chartSettings.indicators, marketData]);

  // Add indicator function
  const addIndicator = useCallback((name, config) => {
    if (!chartRef.current || !marketData) return;

    try {
      switch (name) {
        case 'sma':
          const smaData = calculateSMA(marketData, config.period);
          const smaSeries = chartRef.current.addLineSeries({
            color: config.color,
            lineWidth: 2,
            title: `SMA(${config.period})`,
          });
          smaSeries.setData(smaData);
          indicatorSeriesRef.current[name] = smaSeries;
          break;

        case 'ema':
          const emaData = calculateEMA(marketData, config.period);
          const emaSeries = chartRef.current.addLineSeries({
            color: config.color,
            lineWidth: 2,
            title: `EMA(${config.period})`,
          });
          emaSeries.setData(emaData);
          indicatorSeriesRef.current[name] = emaSeries;
          break;

        case 'bollinger':
          const bollingerData = calculateBollingerBands(marketData, config.period, config.stdDev);
          
          const upperBandSeries = chartRef.current.addLineSeries({
            color: '#2196f3',
            lineWidth: 1,
            title: 'BB Upper',
          });
          
          const lowerBandSeries = chartRef.current.addLineSeries({
            color: '#2196f3',
            lineWidth: 1,
            title: 'BB Lower',
          });

          upperBandSeries.setData(bollingerData.upper);
          lowerBandSeries.setData(bollingerData.lower);
          
          indicatorSeriesRef.current[`${name}_upper`] = upperBandSeries;
          indicatorSeriesRef.current[`${name}_lower`] = lowerBandSeries;
          break;

        default:
          break;
      }
    } catch (error) {
      console.error(`Error adding ${name} indicator:`, error);
    }
  }, [marketData]);

  // Technical indicator calculations
  const calculateSMA = (data, period) => {
    const result = [];
    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1)
        .reduce((acc, item) => acc + item.close, 0);
      result.push({
        time: Math.floor(new Date(data[i].timestamp).getTime() / 1000),
        value: sum / period,
      });
    }
    return result;
  };

  const calculateEMA = (data, period) => {
    const result = [];
    const multiplier = 2 / (period + 1);
    let ema = data[0].close;

    for (let i = 0; i < data.length; i++) {
      if (i === 0) {
        ema = data[i].close;
      } else {
        ema = (data[i].close * multiplier) + (ema * (1 - multiplier));
      }
      
      result.push({
        time: Math.floor(new Date(data[i].timestamp).getTime() / 1000),
        value: ema,
      });
    }
    return result;
  };

  const calculateBollingerBands = (data, period, stdDev) => {
    const smaData = calculateSMA(data, period);
    const upper = [];
    const lower = [];

    for (let i = 0; i < smaData.length; i++) {
      const dataIndex = i + period - 1;
      const slice = data.slice(dataIndex - period + 1, dataIndex + 1);
      const mean = smaData[i].value;
      
      const variance = slice.reduce((acc, item) => 
        acc + Math.pow(item.close - mean, 2), 0) / period;
      const standardDeviation = Math.sqrt(variance);

      upper.push({
        time: smaData[i].time,
        value: mean + (standardDeviation * stdDev),
      });

      lower.push({
        time: smaData[i].time,
        value: mean - (standardDeviation * stdDev),
      });
    }

    return { upper, lower };
  };

  // Event handlers
  const handleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleSettingsClick = (event) => {
    setSettingsAnchor(event.currentTarget);
  };

  const handleSettingsClose = () => {
    setSettingsAnchor(null);
  };

  const handleIndicatorToggle = (indicator) => {
    setChartSettings(prev => ({
      ...prev,
      indicators: {
        ...prev.indicators,
        [indicator]: {
          ...prev.indicators[indicator],
          enabled: !prev.indicators[indicator].enabled,
        },
      },
    }));
  };

  return (
    <Paper 
      elevation={3}
      sx={{ 
        position: 'relative',
        height: isFullscreen ? '100vh' : height + 100,
        width: '100%',
        ...(isFullscreen && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999,
        }),
      }}
    >
      {/* Chart Header */}
      <Box 
        sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="h6" component="h2">
            {symbol}
          </Typography>
          
          <FormControl size="small" sx={{ minWidth: 80 }}>
            <Select
              value={timeframe}
              onChange={(e) => onTimeframeChange?.(e.target.value)}
            >
              <MenuItem value="1m">1m</MenuItem>
              <MenuItem value="5m">5m</MenuItem>
              <MenuItem value="15m">15m</MenuItem>
              <MenuItem value="1h">1h</MenuItem>
              <MenuItem value="4h">4h</MenuItem>
              <MenuItem value="1d">1d</MenuItem>
            </Select>
          </FormControl>

          {/* Active Indicators */}
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {Object.entries(chartSettings.indicators)
              .filter(([_, config]) => config.enabled)
              .map(([name, config]) => (
                <Chip
                  key={name}
                  label={name.toUpperCase()}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              ))
            }
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="Chart Settings">
            <IconButton onClick={handleSettingsClick}>
              <SettingsIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
            <IconButton onClick={handleFullscreen}>
              {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Chart Container */}
      <Box
        ref={chartContainerRef}
        sx={{ 
          width: '100%', 
          height: isFullscreen ? 'calc(100vh - 80px)' : height,
          position: 'relative',
        }}
      />

      {/* Settings Menu */}
      <Menu
        anchorEl={settingsAnchor}
        open={Boolean(settingsAnchor)}
        onClose={handleSettingsClose}
        PaperProps={{
          sx: { width: 300, maxHeight: 500 }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Chart Settings
          </Typography>
          
          <Divider sx={{ my: 1 }} />
          
          <FormControlLabel
            control={
              <Switch
                checked={chartSettings.showGrid}
                onChange={(e) => setChartSettings(prev => ({
                  ...prev,
                  showGrid: e.target.checked
                }))}
              />
            }
            label="Show Grid"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={chartSettings.showCrosshair}
                onChange={(e) => setChartSettings(prev => ({
                  ...prev,
                  showCrosshair: e.target.checked
                }))}
              />
            }
            label="Show Crosshair"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={chartSettings.showVolume}
                onChange={(e) => setChartSettings(prev => ({
                  ...prev,
                  showVolume: e.target.checked
                }))}
              />
            }
            label="Show Volume"
          />

          <Divider sx={{ my: 2 }} />
          
          <Typography variant="subtitle2" gutterBottom>
            Technical Indicators
          </Typography>
          
          {Object.entries(chartSettings.indicators).map(([name, config]) => (
            <FormControlLabel
              key={name}
              control={
                <Switch
                  checked={config.enabled}
                  onChange={() => handleIndicatorToggle(name)}
                />
              }
              label={name.toUpperCase()}
            />
          ))}
        </Box>
      </Menu>

      {/* Loading Overlay */}
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1000,
          }}
        >
          <Typography color="white">Loading chart data...</Typography>
        </Box>
      )}
    </Paper>
  );
};

export default AdvancedChart;
