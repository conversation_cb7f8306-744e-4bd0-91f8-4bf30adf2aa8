/**
 * Advanced WebSocket Service for Real-time Data
 * خدمة WebSocket متقدمة للبيانات الفورية
 */

class WebSocketService {
  constructor() {
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 1000;
    this.subscribers = new Map();
    this.messageQueue = [];
    this.isConnected = false;
    this.isConnecting = false;
    this.heartbeatInterval = null;
    this.lastHeartbeat = null;
    
    // Configuration
    this.config = {
      url: process.env.REACT_APP_WS_URL || 'ws://localhost:8000/ws',
      heartbeatInterval: 30000, // 30 seconds
      reconnectDelay: 1000,
      maxReconnectDelay: 30000,
      reconnectDecay: 1.5,
      timeoutInterval: 2000,
    };

    // Bind methods
    this.connect = this.connect.bind(this);
    this.disconnect = this.disconnect.bind(this);
    this.send = this.send.bind(this);
    this.subscribe = this.subscribe.bind(this);
    this.unsubscribe = this.unsubscribe.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.handleOpen = this.handleOpen.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleError = this.handleError.bind(this);
  }

  /**
   * Initialize WebSocket connection
   */
  initialize() {
    console.log('🔌 Initializing WebSocket service...');
    this.connect();
  }

  /**
   * Connect to WebSocket server
   */
  connect() {
    if (this.isConnecting || this.isConnected) {
      return;
    }

    this.isConnecting = true;
    console.log(`🔗 Connecting to WebSocket: ${this.config.url}`);

    try {
      this.ws = new WebSocket(this.config.url);
      
      this.ws.onopen = this.handleOpen;
      this.ws.onmessage = this.handleMessage;
      this.ws.onclose = this.handleClose;
      this.ws.onerror = this.handleError;

      // Set connection timeout
      this.connectionTimeout = setTimeout(() => {
        if (this.ws.readyState === WebSocket.CONNECTING) {
          console.warn('⚠️ WebSocket connection timeout');
          this.ws.close();
        }
      }, this.config.timeoutInterval);

    } catch (error) {
      console.error('❌ WebSocket connection error:', error);
      this.isConnecting = false;
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket open event
   */
  handleOpen(event) {
    console.log('✅ WebSocket connected successfully');
    
    this.isConnected = true;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    
    // Clear connection timeout
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }

    // Start heartbeat
    this.startHeartbeat();

    // Send queued messages
    this.flushMessageQueue();

    // Notify subscribers about connection
    this.notifySubscribers('connection', { status: 'connected' });

    // Re-subscribe to all topics
    this.resubscribeAll();
  }

  /**
   * Handle WebSocket message event
   */
  handleMessage(event) {
    try {
      const data = JSON.parse(event.data);
      
      // Handle heartbeat response
      if (data.type === 'pong') {
        this.lastHeartbeat = Date.now();
        return;
      }

      // Handle subscription confirmation
      if (data.type === 'subscription_confirmed') {
        console.log(`📡 Subscription confirmed for: ${data.topic}`);
        return;
      }

      // Route message to appropriate subscribers
      this.routeMessage(data);

    } catch (error) {
      console.error('❌ Error parsing WebSocket message:', error);
    }
  }

  /**
   * Handle WebSocket close event
   */
  handleClose(event) {
    console.log(`🔌 WebSocket disconnected: ${event.code} - ${event.reason}`);
    
    this.isConnected = false;
    this.isConnecting = false;

    // Stop heartbeat
    this.stopHeartbeat();

    // Clear connection timeout
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }

    // Notify subscribers about disconnection
    this.notifySubscribers('connection', { 
      status: 'disconnected', 
      code: event.code, 
      reason: event.reason 
    });

    // Schedule reconnection if not intentional
    if (event.code !== 1000) { // 1000 = normal closure
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket error event
   */
  handleError(error) {
    console.error('❌ WebSocket error:', error);
    
    this.notifySubscribers('connection', { 
      status: 'error', 
      error: error.message || 'Unknown WebSocket error' 
    });
  }

  /**
   * Disconnect WebSocket
   */
  disconnect() {
    console.log('🔌 Disconnecting WebSocket...');
    
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.isConnected = false;
    this.isConnecting = false;
    this.subscribers.clear();
    this.messageQueue = [];
  }

  /**
   * Send message to WebSocket server
   */
  send(message) {
    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('❌ Error sending WebSocket message:', error);
        return false;
      }
    } else {
      // Queue message for later sending
      this.messageQueue.push(message);
      console.log('📤 Message queued (WebSocket not connected)');
      return false;
    }
  }

  /**
   * Subscribe to a topic
   */
  subscribe(topic, callback) {
    if (!this.subscribers.has(topic)) {
      this.subscribers.set(topic, new Set());
    }
    
    this.subscribers.get(topic).add(callback);
    
    // Send subscription request if connected
    if (this.isConnected) {
      this.send({
        type: 'subscribe',
        topic: topic
      });
    }

    console.log(`📡 Subscribed to topic: ${topic}`);

    // Return unsubscribe function
    return () => this.unsubscribe(topic, callback);
  }

  /**
   * Unsubscribe from a topic
   */
  unsubscribe(topic, callback) {
    if (this.subscribers.has(topic)) {
      this.subscribers.get(topic).delete(callback);
      
      // Remove topic if no more subscribers
      if (this.subscribers.get(topic).size === 0) {
        this.subscribers.delete(topic);
        
        // Send unsubscribe request if connected
        if (this.isConnected) {
          this.send({
            type: 'unsubscribe',
            topic: topic
          });
        }
      }
    }

    console.log(`📡 Unsubscribed from topic: ${topic}`);
  }

  /**
   * Route incoming message to subscribers
   */
  routeMessage(data) {
    const { type, topic, ...payload } = data;
    
    // Route by type
    if (this.subscribers.has(type)) {
      this.notifySubscribers(type, payload);
    }
    
    // Route by topic if specified
    if (topic && this.subscribers.has(topic)) {
      this.notifySubscribers(topic, payload);
    }

    // Route to general message subscribers
    if (this.subscribers.has('message')) {
      this.notifySubscribers('message', data);
    }
  }

  /**
   * Notify subscribers of a topic
   */
  notifySubscribers(topic, data) {
    if (this.subscribers.has(topic)) {
      this.subscribers.get(topic).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`❌ Error in subscriber callback for ${topic}:`, error);
        }
      });
    }
  }

  /**
   * Start heartbeat mechanism
   */
  startHeartbeat() {
    this.stopHeartbeat(); // Clear any existing heartbeat
    
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping', timestamp: Date.now() });
        
        // Check if we received a recent heartbeat response
        if (this.lastHeartbeat && Date.now() - this.lastHeartbeat > this.config.heartbeatInterval * 2) {
          console.warn('⚠️ Heartbeat timeout - connection may be stale');
          this.ws.close(1006, 'Heartbeat timeout');
        }
      }
    }, this.config.heartbeatInterval);
  }

  /**
   * Stop heartbeat mechanism
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Flush queued messages
   */
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }

  /**
   * Re-subscribe to all topics after reconnection
   */
  resubscribeAll() {
    for (const topic of this.subscribers.keys()) {
      if (topic !== 'connection' && topic !== 'message') {
        this.send({
          type: 'subscribe',
          topic: topic
        });
      }
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached');
      this.notifySubscribers('connection', { 
        status: 'failed', 
        reason: 'Max reconnection attempts reached' 
      });
      return;
    }

    const delay = Math.min(
      this.config.reconnectDelay * Math.pow(this.config.reconnectDecay, this.reconnectAttempts),
      this.config.maxReconnectDelay
    );

    console.log(`🔄 Scheduling reconnection attempt ${this.reconnectAttempts + 1} in ${delay}ms`);

    setTimeout(() => {
      this.reconnectAttempts++;
      this.connect();
    }, delay);
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      subscriberCount: this.subscribers.size,
      queuedMessages: this.messageQueue.length,
      lastHeartbeat: this.lastHeartbeat,
    };
  }

  /**
   * Subscribe to market data updates
   */
  subscribeToMarketData(callback) {
    return this.subscribe('market_data', callback);
  }

  /**
   * Subscribe to trading signals
   */
  subscribeToTradingSignals(callback) {
    return this.subscribe('trading_signals', callback);
  }

  /**
   * Subscribe to portfolio updates
   */
  subscribeToPortfolioUpdates(callback) {
    return this.subscribe('portfolio_updates', callback);
  }

  /**
   * Subscribe to AI insights
   */
  subscribeToAIInsights(callback) {
    return this.subscribe('ai_insights', callback);
  }

  /**
   * Subscribe to system notifications
   */
  subscribeToNotifications(callback) {
    return this.subscribe('notifications', callback);
  }

  /**
   * Subscribe to connection status changes
   */
  subscribeToConnection(callback) {
    return this.subscribe('connection', callback);
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

export { websocketService };
export default websocketService;
