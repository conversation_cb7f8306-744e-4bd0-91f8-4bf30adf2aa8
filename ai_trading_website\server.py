#!/usr/bin/env python3
import http.server
import socketserver
import os
import sys

# تغيير المجلد الحالي إلى مجلد الموقع
os.chdir(os.path.dirname(os.path.abspath(__file__)))

PORT = 8080

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

    def guess_type(self, path):
        mimetype = super().guess_type(path)
        if path.endswith('.css'):
            return 'text/css'
        elif path.endswith('.js'):
            return 'application/javascript'
        elif path.endswith('.html'):
            return 'text/html; charset=utf-8'
        return mimetype

try:
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"خادم الويب يعمل على http://localhost:{PORT}")
        print("اضغط Ctrl+C لإيقاف الخادم")
        httpd.serve_forever()
except KeyboardInterrupt:
    print("\nتم إيقاف الخادم")
    sys.exit(0)
except Exception as e:
    print(f"خطأ في بدء الخادم: {e}")
    sys.exit(1)
