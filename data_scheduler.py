#!/usr/bin/env python3
"""
⏰ Data Scheduler - جدولة جلب البيانات
مجدول جلب البيانات المالية

المرحلة 4: جدولة جلب البيانات التلقائي
- جلب البيانات في أوقات محددة
- مراقبة حالة الجلب
- إعادة المحاولة في حالة الفشل
- تسجيل العمليات
"""

import schedule
import time
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List
import json
import threading
from yahoo_fetcher import YahooFinanceFetcher
from alpha_vantage_fetcher import AlphaVantageFetcher

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_scheduler.log'),
        logging.StreamHandler()
    ]
)

class DataScheduler:
    """فئة جدولة جلب البيانات"""
    
    def __init__(self, config_file: str = "scheduler_config.json"):
        """
        تهيئة المجدول
        
        Args:
            config_file: ملف التكوين
        """
        self.config_file = config_file
        self.config = self.load_config()
        self.running = False
        
        # إنشاء جالبات البيانات
        self.yahoo_fetcher = YahooFinanceFetcher("scheduled_data/yahoo")
        
        # Alpha Vantage (يحتاج مفتاح API)
        alpha_vantage_key = os.getenv('ALPHA_VANTAGE_API_KEY', 'demo')
        self.alpha_vantage_fetcher = AlphaVantageFetcher(
            alpha_vantage_key, 
            "scheduled_data/alpha_vantage"
        )
        
        # إحصائيات الجلب
        self.stats = {
            'total_runs': 0,
            'successful_runs': 0,
            'failed_runs': 0,
            'last_run': None,
            'last_success': None,
            'last_error': None
        }
        
        self.setup_schedules()
    
    def load_config(self) -> Dict:
        """تحميل ملف التكوين"""
        default_config = {
            "schedules": {
                "market_hours": {
                    "enabled": True,
                    "times": ["09:30", "12:00", "15:30"],
                    "timezone": "US/Eastern",
                    "symbols": ["AAPL", "GOOGL", "MSFT", "TSLA"]
                },
                "daily_update": {
                    "enabled": True,
                    "time": "18:00",
                    "full_update": True
                },
                "weekly_full": {
                    "enabled": True,
                    "day": "sunday",
                    "time": "02:00",
                    "historical_data": True
                }
            },
            "retry_settings": {
                "max_retries": 3,
                "retry_delay": 300,  # 5 minutes
                "exponential_backoff": True
            },
            "data_sources": {
                "yahoo_finance": True,
                "alpha_vantage": False  # يحتاج مفتاح API
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logging.info(f"✅ تم تحميل التكوين من {self.config_file}")
                return config
            else:
                # إنشاء ملف التكوين الافتراضي
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, ensure_ascii=False, indent=2)
                logging.info(f"✅ تم إنشاء ملف التكوين الافتراضي: {self.config_file}")
                return default_config
                
        except Exception as e:
            logging.error(f"❌ خطأ في تحميل التكوين: {str(e)}")
            return default_config
    
    def setup_schedules(self):
        """إعداد جداول الجلب"""
        schedules_config = self.config.get('schedules', {})
        
        # جلب أثناء ساعات السوق
        market_hours = schedules_config.get('market_hours', {})
        if market_hours.get('enabled', False):
            for time_str in market_hours.get('times', []):
                schedule.every().monday.at(time_str).do(self.market_hours_update)
                schedule.every().tuesday.at(time_str).do(self.market_hours_update)
                schedule.every().wednesday.at(time_str).do(self.market_hours_update)
                schedule.every().thursday.at(time_str).do(self.market_hours_update)
                schedule.every().friday.at(time_str).do(self.market_hours_update)
                logging.info(f"📅 تم جدولة التحديث في ساعات السوق: {time_str}")
        
        # التحديث اليومي
        daily_update = schedules_config.get('daily_update', {})
        if daily_update.get('enabled', False):
            time_str = daily_update.get('time', '18:00')
            schedule.every().day.at(time_str).do(self.daily_update)
            logging.info(f"📅 تم جدولة التحديث اليومي: {time_str}")
        
        # التحديث الأسبوعي الكامل
        weekly_full = schedules_config.get('weekly_full', {})
        if weekly_full.get('enabled', False):
            day = weekly_full.get('day', 'sunday')
            time_str = weekly_full.get('time', '02:00')
            getattr(schedule.every(), day).at(time_str).do(self.weekly_full_update)
            logging.info(f"📅 تم جدولة التحديث الأسبوعي: {day} {time_str}")
    
    def market_hours_update(self):
        """تحديث أثناء ساعات السوق"""
        logging.info("🕐 بدء التحديث في ساعات السوق...")
        
        try:
            self.stats['total_runs'] += 1
            self.stats['last_run'] = datetime.now().isoformat()
            
            # جلب الأسعار الحالية للرموز المهمة
            symbols = self.config['schedules']['market_hours'].get('symbols', [])
            
            if self.config['data_sources'].get('yahoo_finance', True):
                for symbol in symbols:
                    price_info = self.yahoo_fetcher.get_latest_price(symbol)
                    if price_info:
                        logging.info(f"💰 {symbol}: ${price_info['current_price']}")
            
            self.stats['successful_runs'] += 1
            self.stats['last_success'] = datetime.now().isoformat()
            logging.info("✅ تم الانتهاء من التحديث في ساعات السوق")
            
        except Exception as e:
            self.stats['failed_runs'] += 1
            self.stats['last_error'] = str(e)
            logging.error(f"❌ خطأ في التحديث في ساعات السوق: {str(e)}")
    
    def daily_update(self):
        """التحديث اليومي"""
        logging.info("📊 بدء التحديث اليومي...")
        
        try:
            self.stats['total_runs'] += 1
            self.stats['last_run'] = datetime.now().isoformat()
            
            if self.config['data_sources'].get('yahoo_finance', True):
                # جلب بيانات يوم واحد للرموز الرئيسية
                self.yahoo_fetcher.fetch_all_categories(period="5d")
            
            if self.config['data_sources'].get('alpha_vantage', False):
                # جلب بيانات محدودة من Alpha Vantage
                logging.info("📈 جلب بيانات Alpha Vantage...")
                # يمكن إضافة جلب محدود هنا
            
            self.stats['successful_runs'] += 1
            self.stats['last_success'] = datetime.now().isoformat()
            logging.info("✅ تم الانتهاء من التحديث اليومي")
            
        except Exception as e:
            self.stats['failed_runs'] += 1
            self.stats['last_error'] = str(e)
            logging.error(f"❌ خطأ في التحديث اليومي: {str(e)}")
    
    def weekly_full_update(self):
        """التحديث الأسبوعي الكامل"""
        logging.info("🗓️ بدء التحديث الأسبوعي الكامل...")
        
        try:
            self.stats['total_runs'] += 1
            self.stats['last_run'] = datetime.now().isoformat()
            
            if self.config['data_sources'].get('yahoo_finance', True):
                # جلب بيانات سنة كاملة
                self.yahoo_fetcher.fetch_all_categories(period="1y")
            
            if self.config['data_sources'].get('alpha_vantage', False):
                # جلب بيانات كاملة من Alpha Vantage
                self.alpha_vantage_fetcher.fetch_all_data()
            
            self.stats['successful_runs'] += 1
            self.stats['last_success'] = datetime.now().isoformat()
            logging.info("✅ تم الانتهاء من التحديث الأسبوعي الكامل")
            
        except Exception as e:
            self.stats['failed_runs'] += 1
            self.stats['last_error'] = str(e)
            logging.error(f"❌ خطأ في التحديث الأسبوعي: {str(e)}")
    
    def save_stats(self):
        """حفظ إحصائيات الجلب"""
        try:
            stats_file = "scheduler_stats.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logging.error(f"❌ خطأ في حفظ الإحصائيات: {str(e)}")
    
    def get_status(self) -> Dict:
        """الحصول على حالة المجدول"""
        return {
            'running': self.running,
            'stats': self.stats,
            'next_runs': [str(job.next_run) for job in schedule.jobs],
            'config': self.config
        }
    
    def start(self):
        """بدء المجدول"""
        self.running = True
        logging.info("🚀 بدء مجدول جلب البيانات...")
        
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
                
                # حفظ الإحصائيات كل ساعة
                if datetime.now().minute == 0:
                    self.save_stats()
                    
            except KeyboardInterrupt:
                logging.info("⏹️ تم إيقاف المجدول بواسطة المستخدم")
                break
            except Exception as e:
                logging.error(f"❌ خطأ في المجدول: {str(e)}")
                time.sleep(60)
        
        self.running = False
        self.save_stats()
        logging.info("🛑 تم إيقاف المجدول")
    
    def stop(self):
        """إيقاف المجدول"""
        self.running = False

def main():
    """الدالة الرئيسية"""
    print("⏰ مجدول جلب البيانات المالية")
    print("=" * 50)
    
    # إنشاء المجدول
    scheduler = DataScheduler()
    
    # عرض الحالة
    status = scheduler.get_status()
    print(f"📊 إجمالي التشغيلات: {status['stats']['total_runs']}")
    print(f"✅ التشغيلات الناجحة: {status['stats']['successful_runs']}")
    print(f"❌ التشغيلات الفاشلة: {status['stats']['failed_runs']}")
    
    if status['stats']['last_success']:
        print(f"🕐 آخر تشغيل ناجح: {status['stats']['last_success']}")
    
    print("\n📅 المهام المجدولة:")
    for i, next_run in enumerate(status['next_runs'], 1):
        print(f"  {i}. {next_run}")
    
    print("\n🚀 بدء المجدول... (اضغط Ctrl+C للإيقاف)")
    
    try:
        scheduler.start()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف المجدول")
        scheduler.stop()

if __name__ == "__main__":
    main()
