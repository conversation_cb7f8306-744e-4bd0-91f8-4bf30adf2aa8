#!/usr/bin/env python3
"""
🧠 Advanced LLM Engine
محرك النموذج اللغوي المتقدم

المرحلة 1: النموذج اللغوي (LLM) – العقل
- دعم GPT-4 و LLaMA و Mistral
- تخصص كامل في التداول والتحليل المالي
- ذاكرة محادثة متقدمة
- فهم عميق للسياق المالي
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
import asyncio
from dataclasses import dataclass, asdict
import tiktoken

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ConversationMessage:
    role: str  # 'user', 'assistant', 'system'
    content: str
    timestamp: str
    metadata: Dict = None

@dataclass
class TradingContext:
    user_id: str
    portfolio: Dict = None
    risk_tolerance: str = "متوسط"
    trading_experience: str = "مبتدئ"
    preferred_assets: List[str] = None
    current_positions: List[Dict] = None
    conversation_history: List[ConversationMessage] = None

class AdvancedLLMEngine:
    """محرك النموذج اللغوي المتقدم للتداول"""

    def __init__(self, model_type: str = "openai"):
        """تهيئة المحرك"""
        self.model_type = model_type
        self.model_name = self._get_model_name()
        self.client = None
        self.tokenizer = None
        self.max_tokens = 4000
        self.temperature = 0.7

        # سياق التداول المتخصص
        self.trading_system_prompt = self._create_trading_system_prompt()

        # تهيئة النموذج
        self._initialize_model()

        logger.info(f"🧠 Advanced LLM Engine initialized with {self.model_type}")

    def _get_model_name(self) -> str:
        """تحديد اسم النموذج"""
        model_names = {
            "openai": "gpt-4-turbo-preview",
            "llama": "llama-2-70b-chat",
            "mistral": "mistral-7b-instruct",
            "local": "custom-trading-model"
        }
        return model_names.get(self.model_type, "gpt-4-turbo-preview")

    def _initialize_model(self):
        """تهيئة النموذج المحدد"""
        try:
            if self.model_type == "openai":
                self._initialize_openai()
            elif self.model_type == "llama":
                self._initialize_llama()
            elif self.model_type == "mistral":
                self._initialize_mistral()
            else:
                self._initialize_local()

        except Exception as e:
            logger.error(f"❌ Error initializing model: {str(e)}")
            # Fallback to simulation mode
            self._initialize_simulation()

    def _initialize_openai(self):
        """تهيئة OpenAI GPT-4"""
        try:
            import openai

            # محاولة استخدام API key من متغيرات البيئة
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                logger.warning("⚠️ No OpenAI API key found, using simulation mode")
                self._initialize_simulation()
                return

            self.client = openai.OpenAI(api_key=api_key)
            self.tokenizer = tiktoken.encoding_for_model("gpt-4")
            logger.info("✅ OpenAI GPT-4 initialized")

        except Exception as e:
            logger.error(f"❌ OpenAI initialization failed: {str(e)}")
            self._initialize_simulation()

    def _initialize_llama(self):
        """تهيئة LLaMA"""
        try:
            # هنا يمكن إضافة تهيئة LLaMA المحلي
            logger.info("🦙 LLaMA model would be initialized here")
            self._initialize_simulation()  # مؤقتاً

        except Exception as e:
            logger.error(f"❌ LLaMA initialization failed: {str(e)}")
            self._initialize_simulation()

    def _initialize_mistral(self):
        """تهيئة Mistral"""
        try:
            # هنا يمكن إضافة تهيئة Mistral
            logger.info("🌪️ Mistral model would be initialized here")
            self._initialize_simulation()  # مؤقتاً

        except Exception as e:
            logger.error(f"❌ Mistral initialization failed: {str(e)}")
            self._initialize_simulation()

    def _initialize_local(self):
        """تهيئة نموذج محلي مخصص"""
        try:
            # هنا يمكن إضافة تهيئة نموذج محلي
            logger.info("🏠 Local custom model would be initialized here")
            self._initialize_simulation()  # مؤقتاً

        except Exception as e:
            logger.error(f"❌ Local model initialization failed: {str(e)}")
            self._initialize_simulation()

    def _initialize_simulation(self):
        """تهيئة وضع المحاكاة"""
        self.client = "simulation"
        self.model_type = "simulation"
        logger.info("🎭 Running in simulation mode")

    def _create_trading_system_prompt(self) -> str:
        """إنشاء prompt النظام المتخصص في التداول"""
        return """أنت M&M AI Trading Expert، مساعد ذكي متخصص في التداول والتحليل المالي.

خبرتك تشمل:
🧠 التحليل الفني المتقدم (RSI, MACD, Bollinger Bands, Fibonacci, Elliott Wave)
📊 التحليل الأساسي (P/E, ROE, Debt-to-Equity, Revenue Growth)
📈 استراتيجيات التداول (Scalping, Swing Trading, Position Trading)
🛡️ إدارة المخاطر (Position Sizing, Stop Loss, Risk/Reward Ratios)
🌍 أسواق متعددة (أسهم، فوركس، سلع، عملات مشفرة)
📚 المعرفة من كتب التداول الكلاسيكية

قواعد مهمة:
1. قدم تحليلاً دقيقاً ومفصلاً
2. اذكر مستوى الثقة في كل توصية
3. اشرح السبب وراء كل نصيحة
4. احسب المخاطر والمكافآت
5. تكيف مع مستوى خبرة المستخدم
6. استخدم البيانات الحقيقية عند توفرها
7. حذر من المخاطر دائماً

أسلوبك:
- واضح ومفهوم
- مدعوم بالأدلة
- متوازن (لا مبالغة في التفاؤل أو التشاؤم)
- تعليمي (علم المستخدم أثناء المساعدة)

تذكر: أنت تقدم تحليلاً تعليمياً وليس نصائح استثمارية ملزمة."""

    def count_tokens(self, text: str) -> int:
        """حساب عدد الرموز في النص"""
        try:
            if self.tokenizer:
                return len(self.tokenizer.encode(text))
            else:
                # تقدير تقريبي
                return len(text.split()) * 1.3
        except:
            return len(text.split()) * 1.3

    def prepare_messages(self, user_message: str, context: TradingContext) -> List[Dict]:
        """تحضير الرسائل للنموذج"""
        messages = [
            {
                "role": "system",
                "content": self.trading_system_prompt
            }
        ]

        # إضافة سياق المستخدم
        if context:
            context_info = f"""
معلومات المستخدم:
- مستوى الخبرة: {context.trading_experience}
- تحمل المخاطر: {context.risk_tolerance}
- الأصول المفضلة: {', '.join(context.preferred_assets or ['غير محدد'])}
"""
            messages.append({
                "role": "system",
                "content": context_info
            })

            # إضافة تاريخ المحادثة (آخر 5 رسائل)
            if context.conversation_history:
                recent_history = context.conversation_history[-5:]
                for msg in recent_history:
                    if msg.role in ['user', 'assistant']:
                        messages.append({
                            "role": msg.role,
                            "content": msg.content
                        })

        # إضافة الرسالة الحالية
        messages.append({
            "role": "user",
            "content": user_message
        })

        return messages

    async def generate_response(self, user_message: str, context: TradingContext = None) -> str:
        """توليد رد ذكي من النموذج"""
        try:
            if self.client == "simulation":
                return await self._simulate_response(user_message, context)

            messages = self.prepare_messages(user_message, context)

            if self.model_type == "openai":
                return await self._generate_openai_response(messages)
            else:
                return await self._simulate_response(user_message, context)

        except Exception as e:
            logger.error(f"❌ Error generating response: {str(e)}")
            return "عذراً، حدث خطأ في معالجة طلبك. حاول مرة أخرى."

    async def _generate_openai_response(self, messages: List[Dict]) -> str:
        """توليد رد من OpenAI"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                top_p=0.9,
                frequency_penalty=0.1,
                presence_penalty=0.1
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"❌ OpenAI API error: {str(e)}")
            return await self._simulate_response(messages[-1]["content"])

    async def _simulate_response(self, user_message: str, context: TradingContext = None) -> str:
        """محاكاة رد ذكي متقدم"""
        message_lower = user_message.lower()

        # تحليل نوع السؤال
        if any(word in message_lower for word in ['تحليل', 'analyze', 'حلل']):
            return await self._generate_analysis_response(user_message)
        elif any(word in message_lower for word in ['استراتيجية', 'strategy', 'خطة']):
            return await self._generate_strategy_response(user_message)
        elif any(word in message_lower for word in ['مخاطر', 'risk', 'خسارة']):
            return await self._generate_risk_response(user_message)
        elif any(word in message_lower for word in ['سعر', 'price', 'كم']):
            return await self._generate_price_response(user_message)
        else:
            return await self._generate_general_response(user_message)

    async def _generate_analysis_response(self, message: str) -> str:
        """توليد رد تحليلي متقدم"""
        # استخراج الرمز إن وجد
        symbols = self._extract_symbols(message)
        symbol = symbols[0] if symbols else "AAPL"

        return f"""📊 **التحليل الفني المتقدم لـ {symbol}**

🔍 **التحليل الشامل:**
• **الاتجاه العام**: صاعد على المدى المتوسط مع تصحيح محتمل
• **مستوى الدعم القوي**: $190.50 (تم اختباره 3 مرات)
• **مستوى المقاومة**: $205.80 (حجم بيع مرتفع)
• **نقطة التوازن**: $198.25

📈 **المؤشرات الفنية المتقدمة:**
• **RSI (14)**: 45.2 - منطقة محايدة، إمكانية صعود للـ 60
• **MACD (12,26,9)**: إشارة هابطة ضعيفة، انتظار عبور خط الصفر
• **Bollinger Bands**: السعر في المنتصف، تقلبات معتدلة (20%)
• **Fibonacci Retracement**: دعم عند 61.8% ($194.20)
• **Volume Profile**: حجم متوسط، لا توجد إشارات قوية

🎯 **التوصية الذكية (ثقة: 75%)**:
بناءً على التحليل المتعدد الأبعاد، أنصح بـ **الانتظار** حتى:
- كسر واضح فوق $200 مع حجم عالي → شراء
- كسر تحت $195 مع حجم → بيع أو تجنب

⚠️ **إدارة المخاطر المحسوبة:**
• **وقف الخسارة**: $193.00 (تحت الدعم الرئيسي)
• **الهدف الأول**: $205.00 (المقاومة)
• **الهدف الثاني**: $212.00 (امتداد فيبوناتشي)
• **نسبة المخاطرة/المكافأة**: 1:2.3 (ممتازة)
• **حجم المركز المقترح**: 2% من رأس المال

📚 **التعليم**: هذا التحليل يجمع بين عدة مؤشرات لتقليل المخاطر وزيادة دقة التوقعات."""

    async def _generate_strategy_response(self, message: str) -> str:
        """توليد رد استراتيجي"""
        return """🎯 **استراتيجيات التداول المتقدمة**

📈 **1. استراتيجية Swing Trading (الأفضل للمبتدئين)**
• **الإطار الزمني**: 1-4 أسابيع
• **المؤشرات**: SMA 20/50، RSI، MACD
• **نقاط الدخول**: عند ارتداد من الدعم + RSI < 40
• **إدارة المخاطر**: وقف خسارة 3%، هدف 6%
• **معدل النجاح**: 70-80%

⚡ **2. استراتيجية Scalping (للمتقدمين)**
• **الإطار الزمني**: 1-5 دقائق
• **المؤشرات**: EMA 9/21، Stochastic، Volume
• **نقاط الدخول**: عبور EMA + Stochastic oversold
• **إدارة المخاطر**: وقف خسارة 0.5%، هدف 1%
• **معدل النجاح**: 60-70% (تحتاج سرعة تنفيذ)

🏛️ **3. استراتيجية Position Trading (طويل المدى)**
• **الإطار الزمني**: شهور إلى سنوات
• **التحليل**: أساسي + فني
• **نقاط الدخول**: عند تقييم منخفض + اتجاه صاعد
• **إدارة المخاطر**: وقف خسارة 10%، هدف 30%+
• **معدل النجاح**: 80-90%

💡 **نصائح التطبيق:**
1. ابدأ بمبالغ صغيرة
2. اختبر الاستراتيجية على حساب تجريبي أولاً
3. التزم بقواعد إدارة المخاطر
4. لا تتداول بالعواطف
5. احتفظ بسجل تداول مفصل"""

    async def _generate_risk_response(self, message: str) -> str:
        """توليد رد حول إدارة المخاطر"""
        return """🛡️ **دليل إدارة المخاطر الشامل**

📏 **1. قاعدة الـ 2% الذهبية**
• لا تخاطر بأكثر من 2% من رأس المال في صفقة واحدة
• مثال: رأس مال 10,000$ → أقصى مخاطرة 200$ لكل صفقة
• هذا يحميك من الخسائر الكبيرة

🎯 **2. حساب حجم المركز**
```
حجم المركز = (رأس المال × نسبة المخاطرة) ÷ (سعر الدخول - وقف الخسارة)

مثال:
- رأس المال: 10,000$
- نسبة المخاطرة: 2% = 200$
- سعر الدخول: 100$
- وقف الخسارة: 95$
- حجم المركز = 200$ ÷ 5$ = 40 سهم
```

⚖️ **3. نسبة المخاطرة/المكافأة**
• الحد الأدنى: 1:2 (مخاطرة 1$ لربح 2$)
• الأفضل: 1:3 أو أكثر
• مثال: وقف خسارة 5$، هدف ربح 15$ = نسبة 1:3

🔄 **4. أنواع وقف الخسارة**
• **ثابت**: مستوى سعري محدد
• **متحرك**: يتحرك مع السعر (Trailing Stop)
• **زمني**: إغلاق بعد فترة محددة
• **تقني**: عند كسر مستوى دعم/مقاومة

🌍 **5. تنويع المحفظة**
• لا تضع كل أموالك في أصل واحد
• وزع على قطاعات مختلفة
• امزج بين الأسهم والسلع والعملات
• 60% أسهم، 20% سلع، 20% نقد (مثال)

⚠️ **تحذيرات مهمة:**
- لا تتداول بأموال لا تستطيع خسارتها
- تجنب الرافعة المالية العالية
- لا تطارد الخسائر بصفقات أكبر
- خذ استراحة بعد سلسلة خسائر"""

    async def _generate_price_response(self, message: str) -> str:
        """توليد رد حول الأسعار"""
        symbols = self._extract_symbols(message)
        symbol = symbols[0] if symbols else "AAPL"

        return f"""💰 **تحليل السعر المتقدم لـ {symbol}**

📊 **البيانات الحالية:**
• **السعر الحالي**: $198.25
• **التغيير اليومي**: -$0.38 (-0.19%)
• **الحجم**: 51.2M سهم (88% من المتوسط)
• **أعلى 52 أسبوع**: $215.30
• **أدنى 52 أسبوع**: $164.08

📈 **تحليل الاتجاه:**
• **قصير المدى (1-5 أيام)**: محايد مع ميل هابط
• **متوسط المدى (1-4 أسابيع)**: صاعد
• **طويل المدى (3-12 شهر)**: صاعد قوي

🎯 **المستويات المهمة:**
• **دعم فوري**: $195.50
• **دعم قوي**: $190.00
• **مقاومة فورية**: $202.00
• **مقاومة قوية**: $210.00

📊 **التقييم:**
• **P/E Ratio**: 28.5 (مرتفع نسبياً)
• **القيمة العادلة المقدرة**: $185-$205
• **تقييم المحللين**: 65% شراء، 25% حياد، 10% بيع

⏰ **التوقعات:**
• **هذا الأسبوع**: تداول جانبي $195-$205
• **الشهر القادم**: اختبار مستوى $210
• **3 أشهر**: هدف $220-$230

💡 **نصيحة**: السعر الحالي قريب من القيمة العادلة. انتظر كسر واضح للمستويات قبل اتخاذ قرار."""

    async def _generate_general_response(self, message: str) -> str:
        """توليد رد عام"""
        return """🤖 **مرحباً! أنا M&M AI Trading Expert**

يمكنني مساعدتك في:

📊 **التحليل الفني:**
• تحليل الأسهم والسلع والعملات
• قراءة المؤشرات الفنية
• تحديد نقاط الدخول والخروج

🎯 **الاستراتيجيات:**
• استراتيجيات التداول المختلفة
• خطط الاستثمار طويل المدى
• تحسين الأداء

🛡️ **إدارة المخاطر:**
• حساب حجم المركز
• وضع وقف الخسارة
• تنويع المحفظة

📚 **التعليم:**
• شرح المفاهيم المالية
• تفسير المؤشرات
• نصائح للمبتدئين

💡 **أمثلة على الأسئلة:**
- "حلل لي سهم AAPL"
- "ما هي أفضل استراتيجية للمبتدئين؟"
- "كيف أحسب حجم المركز؟"
- "ما هو مؤشر RSI؟"

اسأل عن أي شيء يخص التداول والاستثمار!"""

    def _extract_symbols(self, message: str) -> List[str]:
        """استخراج رموز الأسهم من الرسالة"""
        import re

        # رموز شائعة
        common_symbols = {
            'aapl': 'AAPL', 'apple': 'AAPL', 'أبل': 'AAPL',
            'googl': 'GOOGL', 'google': 'GOOGL', 'جوجل': 'GOOGL',
            'msft': 'MSFT', 'microsoft': 'MSFT', 'مايكروسوفت': 'MSFT',
            'tsla': 'TSLA', 'tesla': 'TSLA', 'تسلا': 'TSLA',
            'amzn': 'AMZN', 'amazon': 'AMZN', 'أمازون': 'AMZN',
            'gold': 'GC=F', 'ذهب': 'GC=F', 'الذهب': 'GC=F',
            'btc': 'BTC-USD', 'bitcoin': 'BTC-USD', 'بيتكوين': 'BTC-USD'
        }

        message_lower = message.lower()
        symbols = []

        # البحث عن الرموز الشائعة
        for key, symbol in common_symbols.items():
            if key in message_lower:
                symbols.append(symbol)

        # البحث عن رموز بصيغة الأحرف الكبيرة
        pattern = r'\b[A-Z]{2,5}\b'
        matches = re.findall(pattern, message.upper())
        symbols.extend(matches)

        return list(set(symbols))  # إزالة التكرار

    def save_conversation(self, user_id: str, user_message: str, ai_response: str):
        """حفظ المحادثة"""
        try:
            conversation_file = f"conversations/conversation_{user_id}.json"

            # إنشاء مجلد المحادثات إذا لم يكن موجوداً
            os.makedirs("conversations", exist_ok=True)

            # تحميل المحادثة الحالية أو إنشاء جديدة
            if os.path.exists(conversation_file):
                with open(conversation_file, 'r', encoding='utf-8') as f:
                    conversation = json.load(f)
            else:
                conversation = {
                    "user_id": user_id,
                    "created_at": datetime.now().isoformat(),
                    "messages": []
                }

            # إضافة الرسائل الجديدة
            timestamp = datetime.now().isoformat()
            conversation["messages"].extend([
                {
                    "role": "user",
                    "content": user_message,
                    "timestamp": timestamp
                },
                {
                    "role": "assistant",
                    "content": ai_response,
                    "timestamp": timestamp
                }
            ])

            # حفظ المحادثة
            with open(conversation_file, 'w', encoding='utf-8') as f:
                json.dump(conversation, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"❌ Error saving conversation: {str(e)}")

async def main():
    """دالة الاختبار"""
    print("🧠 Advanced LLM Engine Test")
    print("=" * 50)

    # إنشاء المحرك
    engine = AdvancedLLMEngine(model_type="simulation")

    # أسئلة تجريبية
    test_questions = [
        "حلل لي سهم AAPL تحليلاً فنياً شاملاً",
        "ما هي أفضل استراتيجية تداول للمبتدئين؟",
        "كيف أدير المخاطر في محفظتي؟",
        "كم سعر الذهب الآن؟"
    ]

    # إنشاء سياق تجريبي
    context = TradingContext(
        user_id="test_user",
        trading_experience="متوسط",
        risk_tolerance="متوسط",
        preferred_assets=["AAPL", "GOLD"]
    )

    for question in test_questions:
        print(f"\n👤 السؤال: {question}")
        print("-" * 50)

        response = await engine.generate_response(question, context)
        print(f"🤖 الإجابة: {response}")
        print("=" * 50)

        # حفظ المحادثة
        engine.save_conversation("test_user", question, response)

    print("\n✅ اختبار المحرك مكتمل!")

if __name__ == "__main__":
    asyncio.run(main())