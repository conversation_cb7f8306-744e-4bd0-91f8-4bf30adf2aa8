/**
 * AI Trading Website - Advanced JavaScript
 * موقع التداول الذكي - جافا سكريبت متطور
 */

// ===== إعدادات عامة =====
const CONFIG = {
    TYPING_SPEED: 50,
    THINKING_TIME: 2000,
    PARTICLE_COUNT: 100,
    ROBOT_RESPONSES: [
        "مرحباً! كيف يمكنني مساعدتك في التداول اليوم؟",
        "أنا هنا لأساعدك في تحليل الأسواق المالية.",
        "هل تريد معرفة آخر التحديثات في السوق؟",
        "يمكنني تقديم نصائح تداول مخصصة لك.",
        "دعني أحلل البيانات وأقدم لك أفضل الفرص.",
        "السوق اليوم يظهر إشارات مثيرة للاهتمام!",
        "هل تريد مني تحليل عملة أو سهم معين؟"
    ]
};

// ===== فئة إدارة الجسيمات =====
class ParticleSystem {
    constructor() {
        this.particles = [];
        this.container = document.getElementById('particles-container');
        this.init();
    }

    init() {
        this.createParticles();
        this.animate();
    }

    createParticles() {
        for (let i = 0; i < CONFIG.PARTICLE_COUNT; i++) {
            const particle = {
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                size: Math.random() * 3 + 1,
                opacity: Math.random() * 0.5 + 0.2,
                element: this.createParticleElement()
            };
            
            this.particles.push(particle);
            this.container.appendChild(particle.element);
        }
    }

    createParticleElement() {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            background: radial-gradient(circle, #00d4ff, transparent);
            border-radius: 50%;
            pointer-events: none;
            filter: blur(1px);
        `;
        return particle;
    }

    animate() {
        this.particles.forEach(particle => {
            // تحديث الموقع
            particle.x += particle.vx;
            particle.y += particle.vy;

            // إعادة تدوير الجسيمات
            if (particle.x < 0) particle.x = window.innerWidth;
            if (particle.x > window.innerWidth) particle.x = 0;
            if (particle.y < 0) particle.y = window.innerHeight;
            if (particle.y > window.innerHeight) particle.y = 0;

            // تطبيق التغييرات
            particle.element.style.left = particle.x + 'px';
            particle.element.style.top = particle.y + 'px';
            particle.element.style.width = particle.size + 'px';
            particle.element.style.height = particle.size + 'px';
            particle.element.style.opacity = particle.opacity;
        });

        requestAnimationFrame(() => this.animate());
    }

    resize() {
        this.particles.forEach(particle => {
            if (particle.x > window.innerWidth) particle.x = window.innerWidth;
            if (particle.y > window.innerHeight) particle.y = window.innerHeight;
        });
    }
}

// ===== فئة الروبوت التفاعلي =====
class InteractiveRobot {
    constructor() {
        this.robot = document.querySelector('.robot');
        this.chatMessages = document.getElementById('chatMessages');
        this.userInput = document.getElementById('userInput');
        this.sendButton = document.getElementById('sendButton');
        this.isThinking = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startIdleAnimation();
    }

    setupEventListeners() {
        this.sendButton.addEventListener('click', () => this.handleUserMessage());
        this.userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.handleUserMessage();
        });

        // تفاعل الروبوت مع حركة الماوس
        this.robot.addEventListener('mouseenter', () => this.onRobotHover());
        this.robot.addEventListener('mouseleave', () => this.onRobotLeave());
    }

    async handleUserMessage() {
        const message = this.userInput.value.trim();
        if (!message) return;

        // إضافة رسالة المستخدم
        this.addMessage(message, 'user');
        this.userInput.value = '';

        // بدء التفكير
        this.startThinking();

        // انتظار ثم الرد
        await this.delay(CONFIG.THINKING_TIME);
        this.stopThinking();

        // إضافة رد الروبوت
        const response = this.generateResponse(message);
        await this.addTypingMessage(response, 'robot');
    }

    addMessage(content, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = sender === 'user' ? '👤' : '🤖';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.textContent = content;
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    async addTypingMessage(content, sender) {
        // إضافة مؤشر الكتابة
        const typingDiv = this.createTypingIndicator();
        this.chatMessages.appendChild(typingDiv);
        this.scrollToBottom();

        await this.delay(1000);

        // إزالة مؤشر الكتابة
        this.chatMessages.removeChild(typingDiv);

        // إضافة الرسالة بتأثير الكتابة
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = sender === 'user' ? '👤' : '🤖';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        this.chatMessages.appendChild(messageDiv);

        // تأثير الكتابة
        await this.typeText(messageContent, content);
        this.scrollToBottom();
    }

    createTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message robot-message';
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = '🤖';
        
        const typingContent = document.createElement('div');
        typingContent.className = 'message-content';
        
        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';
        
        for (let i = 0; i < 3; i++) {
            const dot = document.createElement('div');
            dot.className = 'typing-dot';
            typingIndicator.appendChild(dot);
        }
        
        typingContent.appendChild(typingIndicator);
        typingDiv.appendChild(avatar);
        typingDiv.appendChild(typingContent);
        
        return typingDiv;
    }

    async typeText(element, text) {
        element.textContent = '';
        for (let i = 0; i < text.length; i++) {
            element.textContent += text[i];
            await this.delay(CONFIG.TYPING_SPEED);
        }
    }

    generateResponse(userMessage) {
        const message = userMessage.toLowerCase();
        
        // ردود ذكية بناءً على الكلمات المفتاحية
        if (message.includes('سعر') || message.includes('price')) {
            return "أسعار الذهب اليوم تظهر اتجاهاً صاعداً. هل تريد تحليلاً مفصلاً؟";
        } else if (message.includes('تداول') || message.includes('trade')) {
            return "التداول يتطلب استراتيجية محكمة. دعني أساعدك في وضع خطة مناسبة.";
        } else if (message.includes('ذهب') || message.includes('gold')) {
            return "الذهب يعتبر ملاذاً آمناً. السوق يظهر إشارات إيجابية حالياً.";
        } else if (message.includes('نصيحة') || message.includes('advice')) {
            return "نصيحتي: ادرس السوق جيداً، ولا تستثمر أكثر مما تستطيع خسارته.";
        } else if (message.includes('مساعدة') || message.includes('help')) {
            return "بالطبع! أنا هنا لمساعدتك في كل ما يتعلق بالتداول والأسواق المالية.";
        } else {
            // رد عشوائي من القائمة
            return CONFIG.ROBOT_RESPONSES[Math.floor(Math.random() * CONFIG.ROBOT_RESPONSES.length)];
        }
    }

    startThinking() {
        this.isThinking = true;
        this.robot.classList.add('thinking');
        
        // تحريك العيون أثناء التفكير
        const eyes = this.robot.querySelectorAll('.pupil');
        eyes.forEach(eye => {
            eye.style.animation = 'eyeThinking 0.5s ease-in-out infinite';
        });
    }

    stopThinking() {
        this.isThinking = false;
        this.robot.classList.remove('thinking');
        
        // إعادة تحريك العيون الطبيعي
        const eyes = this.robot.querySelectorAll('.pupil');
        eyes.forEach(eye => {
            eye.style.animation = 'eyeMove 3s ease-in-out infinite';
        });
    }

    onRobotHover() {
        if (!this.isThinking) {
            this.robot.style.transform = 'scale(1.05)';
            this.robot.style.filter = 'brightness(1.2)';
        }
    }

    onRobotLeave() {
        if (!this.isThinking) {
            this.robot.style.transform = 'scale(1)';
            this.robot.style.filter = 'brightness(1)';
        }
    }

    startIdleAnimation() {
        setInterval(() => {
            if (!this.isThinking) {
                // حركات عشوائية للروبوت
                const randomAction = Math.random();
                
                if (randomAction < 0.3) {
                    this.blinkEyes();
                } else if (randomAction < 0.6) {
                    this.moveAntenna();
                } else {
                    this.flashPanelLights();
                }
            }
        }, 3000);
    }

    blinkEyes() {
        const eyes = this.robot.querySelectorAll('.eye');
        eyes.forEach(eye => {
            eye.style.animation = 'eyeBlink 0.3s ease-in-out';
            setTimeout(() => {
                eye.style.animation = 'eyeBlink 4s ease-in-out infinite';
            }, 300);
        });
    }

    moveAntenna() {
        const antenna = this.robot.querySelector('.antenna-tip');
        antenna.style.animation = 'antennaPulse 0.5s ease-in-out 3';
        setTimeout(() => {
            antenna.style.animation = 'antennaPulse 1.5s ease-in-out infinite';
        }, 1500);
    }

    flashPanelLights() {
        const lights = this.robot.querySelectorAll('.button');
        lights.forEach((light, index) => {
            setTimeout(() => {
                light.style.animation = 'buttonBlink 0.5s ease-in-out';
                setTimeout(() => {
                    light.style.animation = 'buttonBlink 3s ease-in-out infinite';
                    light.style.animationDelay = `${index}s`;
                }, 500);
            }, index * 200);
        });
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// ===== فئة التنقل =====
class Navigation {
    constructor() {
        this.navbar = document.querySelector('.navbar');
        this.hamburger = document.querySelector('.hamburger');
        this.navMenu = document.querySelector('.nav-menu');
        this.navLinks = document.querySelectorAll('.nav-link');
        this.init();
    }

    init() {
        this.setupScrollEffect();
        this.setupSmoothScrolling();
        this.setupMobileMenu();
    }

    setupScrollEffect() {
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                this.navbar.style.background = 'rgba(10, 14, 26, 0.98)';
                this.navbar.style.boxShadow = '0 5px 20px rgba(0, 212, 255, 0.2)';
            } else {
                this.navbar.style.background = 'rgba(10, 14, 26, 0.95)';
                this.navbar.style.boxShadow = 'none';
            }
        });
    }

    setupSmoothScrolling() {
        this.navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetSection = document.querySelector(targetId);
                
                if (targetSection) {
                    targetSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    setupMobileMenu() {
        if (this.hamburger) {
            this.hamburger.addEventListener('click', () => {
                this.navMenu.classList.toggle('active');
                this.hamburger.classList.toggle('active');
            });
        }
    }
}

// ===== تأثيرات إضافية =====
class VisualEffects {
    constructor() {
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupParallaxEffect();
        this.setupHoverEffects();
    }

    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });

        // مراقبة العناصر
        document.querySelectorAll('.feature-card, .section-title').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(50px)';
            el.style.transition = 'all 0.6s ease';
            observer.observe(el);
        });
    }

    setupParallaxEffect() {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.main-logo, .water-waves');
            
            parallaxElements.forEach(element => {
                const speed = 0.5;
                element.style.transform = `translateY(${scrolled * speed}px)`;
            });
        });
    }

    setupHoverEffects() {
        // تأثيرات hover للأزرار
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.05)';
            });
            
            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // تأثيرات hover للبطاقات
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-15px) rotateY(5deg)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) rotateY(0deg)';
            });
        });
    }
}

// ===== فئة دعم الصور =====
class ImageSupport {
    constructor() {
        this.currentImage = null;
        this.mainCurrentImage = null;
        this.init();
    }

    init() {
        this.setupMainChat();
        this.setupSecondaryChat();
    }

    setupMainChat() {
        const mainImageBtn = document.getElementById('mainImageBtn');
        const mainImageInput = document.getElementById('mainImageInput');
        const mainImagePreview = document.getElementById('mainImagePreview');
        const mainPreviewImg = document.getElementById('mainPreviewImg');
        const mainRemoveImage = document.getElementById('mainRemoveImage');
        const mainUserInput = document.getElementById('mainUserInput');
        const mainSendButton = document.getElementById('mainSendButton');
        const mainChatMessages = document.getElementById('mainChatMessages');

        if (mainImageBtn && mainImageInput) {
            mainImageBtn.addEventListener('click', () => mainImageInput.click());
            this.handleImageUpload(mainImageInput, mainImagePreview, mainPreviewImg, mainRemoveImage, true);

            if (mainSendButton) {
                mainSendButton.addEventListener('click', () => this.sendMessage(mainUserInput, mainChatMessages, true));
            }

            if (mainUserInput) {
                mainUserInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.sendMessage(mainUserInput, mainChatMessages, true);
                    }
                });
            }
        }
    }

    setupSecondaryChat() {
        const imageBtn = document.getElementById('imageBtn');
        const imageInput = document.getElementById('imageInput');
        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        const removeImage = document.getElementById('removeImage');

        if (imageBtn && imageInput) {
            imageBtn.addEventListener('click', () => imageInput.click());
            this.handleImageUpload(imageInput, imagePreview, previewImg, removeImage, false);
        }
    }

    handleImageUpload(input, preview, previewImg, removeBtn, isMain = false) {
        input.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    previewImg.src = e.target.result;
                    preview.style.display = 'block';
                    if (isMain) {
                        this.mainCurrentImage = e.target.result;
                    } else {
                        this.currentImage = e.target.result;
                    }
                };
                reader.readAsDataURL(file);
            }
        });

        if (removeBtn) {
            removeBtn.addEventListener('click', () => {
                preview.style.display = 'none';
                input.value = '';
                if (isMain) {
                    this.mainCurrentImage = null;
                } else {
                    this.currentImage = null;
                }
            });
        }
    }

    sendMessage(input, container, isMain = false) {
        const message = input.value.trim();
        const imageData = isMain ? this.mainCurrentImage : this.currentImage;

        if (message || imageData) {
            this.addMessage(message, true, imageData, container);
            input.value = '';

            // إخفاء معاينة الصورة
            if (isMain && this.mainCurrentImage) {
                document.getElementById('mainImagePreview').style.display = 'none';
                document.getElementById('mainImageInput').value = '';
                this.mainCurrentImage = null;
            } else if (!isMain && this.currentImage) {
                document.getElementById('imagePreview').style.display = 'none';
                document.getElementById('imageInput').value = '';
                this.currentImage = null;
            }

            // محاكاة رد الروبوت
            setTimeout(() => {
                let response;
                if (imageData) {
                    response = 'شكراً لك على الصورة! يمكنني رؤيتها وتحليلها. ما الذي تريد معرفته عنها؟';
                } else {
                    const responses = [
                        'شكراً لك على سؤالك! كيف يمكنني مساعدتك أكثر؟',
                        'هذا سؤال رائع! دعني أفكر في أفضل إجابة لك.',
                        'أفهم ما تقصده. هل تريد المزيد من التفاصيل؟',
                        'ممتاز! هل لديك أسئلة أخرى؟',
                        'يمكنني مساعدتك في هذا الموضوع. ما رأيك؟'
                    ];
                    response = responses[Math.floor(Math.random() * responses.length)];
                }
                this.addMessage(response, false, null, container);
            }, 1000);
        }
    }

    addMessage(content, isUser = false, imageData = null, container) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user-message' : 'robot-message'}`;

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = isUser ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        if (imageData) {
            const img = document.createElement('img');
            img.src = imageData;
            img.style.maxWidth = '200px';
            img.style.maxHeight = '150px';
            img.style.borderRadius = '10px';
            img.style.marginBottom = '0.5rem';
            img.style.display = 'block';
            messageContent.appendChild(img);
        }

        if (content) {
            const textDiv = document.createElement('div');
            textDiv.textContent = content;
            messageContent.appendChild(textDiv);
        }

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);

        container.appendChild(messageDiv);
        container.scrollTop = container.scrollHeight;
    }
}

// ===== تهيئة التطبيق =====
document.addEventListener('DOMContentLoaded', () => {
    // تهيئة جميع الفئات
    const particleSystem = new ParticleSystem();
    const robot = new InteractiveRobot();
    const navigation = new Navigation();
    const effects = new VisualEffects();
    const imageSupport = new ImageSupport();

    // معالجة تغيير حجم النافذة
    window.addEventListener('resize', () => {
        particleSystem.resize();
    });

    // رسالة ترحيب في وحدة التحكم
    console.log(`
    🧠 AI Trading System Loaded Successfully!
    ✨ نظام التداول الذكي تم تحميله بنجاح!

    Features:
    - Interactive Robot 🤖
    - Image Support 📷
    - Particle System ✨
    - Smooth Animations 🎭
    - Responsive Design 📱
    `);
});

// إضافة CSS للحركات الإضافية
const additionalStyles = `
    @keyframes eyeThinking {
        0%, 100% { transform: translate(-50%, -50%) scale(1); }
        50% { transform: translate(-30%, -30%) scale(1.2); }
    }
    
    .nav-menu.active {
        display: flex !important;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: rgba(10, 14, 26, 0.98);
        padding: 2rem;
        border-top: 1px solid rgba(0, 212, 255, 0.3);
    }
    
    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
`;

// إضافة الأنماط الإضافية
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
