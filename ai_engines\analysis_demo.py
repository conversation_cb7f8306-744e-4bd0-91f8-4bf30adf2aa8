#!/usr/bin/env python3
"""
📊 Technical Analysis Demo
عرض توضيحي للتحليل الفني
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime

def main():
    print("🚀 عرض توضيحي للتحليل الفني")
    print("=" * 50)
    
    # تحليل AAPL
    print("\n📊 تحليل AAPL...")
    try:
        df = pd.read_csv("../data_ingestion/AAPL.csv")
        print(f"✅ تم تحميل {len(df)} سجل")
        
        # البيانات الحالية
        current_price = df['Close'].iloc[-1]
        prev_price = df['Close'].iloc[-2]
        change = current_price - prev_price
        change_pct = (change / prev_price) * 100
        
        print(f"💰 السعر الحالي: ${current_price:.2f}")
        print(f"📈 التغيير: ${change:+.2f} ({change_pct:+.2f}%)")
        
        # المتوسطات المتحركة
        sma_5 = df['Close'].tail(5).mean()
        sma_10 = df['Close'].tail(10).mean()
        sma_20 = df['Close'].tail(20).mean()
        
        print(f"📊 المتوسط 5: ${sma_5:.2f}")
        print(f"📊 المتوسط 10: ${sma_10:.2f}")
        print(f"📊 المتوسط 20: ${sma_20:.2f}")
        
        # RSI مبسط
        changes = df['Close'].diff().dropna()
        gains = changes[changes > 0].tail(14)
        losses = abs(changes[changes < 0]).tail(14)
        
        avg_gain = gains.mean() if len(gains) > 0 else 0
        avg_loss = losses.mean() if len(losses) > 0 else 0.01
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        print(f"📊 RSI: {rsi:.1f}")
        
        # الإشارات
        signals = []
        
        if current_price > sma_5 > sma_10:
            signals.append("🟢 BUY - اتجاه صاعد")
        elif current_price < sma_5 < sma_10:
            signals.append("🔴 SELL - اتجاه هابط")
        else:
            signals.append("🟡 HOLD - اتجاه جانبي")
        
        if rsi < 30:
            signals.append("🟢 BUY - RSI مبالغ في البيع")
        elif rsi > 70:
            signals.append("🔴 SELL - RSI مبالغ في الشراء")
        
        print(f"\n🚨 الإشارات:")
        for signal in signals:
            print(f"  • {signal}")
        
        # التوصية النهائية
        buy_count = len([s for s in signals if "BUY" in s])
        sell_count = len([s for s in signals if "SELL" in s])
        
        if buy_count > sell_count:
            recommendation = "BUY"
            confidence = min(buy_count * 50, 100)
        elif sell_count > buy_count:
            recommendation = "SELL"
            confidence = min(sell_count * 50, 100)
        else:
            recommendation = "HOLD"
            confidence = 50
        
        print(f"\n🎯 التوصية النهائية: {recommendation}")
        print(f"🎯 مستوى الثقة: {confidence}%")
        
        # حفظ النتائج
        result = {
            "symbol": "AAPL",
            "timestamp": datetime.now().isoformat(),
            "current_price": round(current_price, 2),
            "change_percent": round(change_pct, 2),
            "sma_5": round(sma_5, 2),
            "sma_10": round(sma_10, 2),
            "sma_20": round(sma_20, 2),
            "rsi": round(rsi, 1),
            "signals": signals,
            "recommendation": recommendation,
            "confidence": confidence
        }
        
        # إنشاء مجلد النتائج
        if not os.path.exists("analysis_results"):
            os.makedirs("analysis_results")
        
        # حفظ النتائج
        with open("analysis_results/AAPL_analysis.json", "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ النتائج في analysis_results/AAPL_analysis.json")
        
    except Exception as e:
        print(f"❌ خطأ في تحليل AAPL: {str(e)}")
    
    # تحليل الذهب
    print("\n🥇 تحليل الذهب...")
    try:
        df = pd.read_csv("../data_ingestion/GOLD.csv")
        print(f"✅ تم تحميل {len(df)} سجل")
        
        current_price = df['Close'].iloc[-1]
        prev_price = df['Close'].iloc[-2]
        change_pct = ((current_price / prev_price) - 1) * 100
        
        print(f"💰 سعر الذهب: ${current_price:.2f}")
        print(f"📈 التغيير: {change_pct:+.2f}%")
        
        # متوسط متحرك
        sma_10 = df['Close'].tail(10).mean()
        print(f"📊 المتوسط 10: ${sma_10:.2f}")
        
        # إشارة بسيطة
        if current_price > sma_10:
            gold_signal = "BUY - فوق المتوسط"
        else:
            gold_signal = "SELL - تحت المتوسط"
        
        print(f"🚨 إشارة الذهب: {gold_signal}")
        
        # حفظ نتائج الذهب
        gold_result = {
            "symbol": "GOLD",
            "timestamp": datetime.now().isoformat(),
            "current_price": round(current_price, 2),
            "change_percent": round(change_pct, 2),
            "sma_10": round(sma_10, 2),
            "signal": gold_signal
        }
        
        with open("analysis_results/GOLD_analysis.json", "w", encoding="utf-8") as f:
            json.dump(gold_result, f, ensure_ascii=False, indent=2)
        
        print(f"💾 تم حفظ نتائج الذهب في analysis_results/GOLD_analysis.json")
        
    except Exception as e:
        print(f"❌ خطأ في تحليل الذهب: {str(e)}")
    
    print(f"\n🎉 تم الانتهاء من التحليل الفني!")
    print(f"📁 تحقق من مجلد analysis_results للنتائج التفصيلية")

if __name__ == "__main__":
    main()
