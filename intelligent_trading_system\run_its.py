#!/usr/bin/env python3
"""
🚀 سكريبت التشغيل السريع لنظام التداول الذكي المتكامل
Quick Start Script for Intelligent Trading System (ITS)

يوفر طريقة سريعة وسهلة لتشغيل النظام مع إعدادات افتراضية محسنة
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path
import logging
from datetime import datetime
import json
import webbrowser
import warnings
warnings.filterwarnings('ignore')

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ITS_QuickStart")

class ITSQuickStart:
    """مشغل سريع لنظام التداول الذكي المتكامل"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.processes = []
        self.running = False
        
        # إعداد معالج الإشارات
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("🧠 مرحباً بك في نظام التداول الذكي المتكامل")
    
    def _signal_handler(self, signum, frame):
        """معالج إشارات الإغلاق"""
        logger.info("🛑 تم استلام إشارة الإغلاق...")
        self.shutdown()
    
    def check_requirements(self):
        """فحص المتطلبات الأساسية"""
        logger.info("🔍 فحص المتطلبات الأساسية...")
        
        # فحص Python
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            logger.error("❌ يتطلب Python 3.8 أو أحدث")
            return False
        
        logger.info(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # فحص pip
        try:
            import pip
            logger.info("✅ pip متوفر")
        except ImportError:
            logger.error("❌ pip غير متوفر")
            return False
        
        return True
    
    def install_dependencies(self, force=False):
        """تثبيت المتطلبات"""
        requirements_file = self.project_root / "requirements.txt"
        
        if not requirements_file.exists():
            logger.error("❌ ملف requirements.txt غير موجود")
            return False
        
        if not force:
            # فحص إذا كانت المكتبات الأساسية مثبتة
            try:
                import fastapi
                import pandas
                import numpy
                logger.info("✅ المكتبات الأساسية مثبتة")
                return True
            except ImportError:
                pass
        
        logger.info("📦 تثبيت المتطلبات...")
        
        try:
            # تثبيت المكتبات الأساسية أولاً
            basic_packages = [
                "fastapi==0.104.1",
                "uvicorn[standard]==0.24.0",
                "pandas==2.1.4",
                "numpy==1.25.2",
                "requests==2.31.0",
                "pydantic==2.5.0"
            ]
            
            for package in basic_packages:
                logger.info(f"تثبيت {package}...")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", package
                ], capture_output=True, text=True)
                
                if result.returncode != 0:
                    logger.warning(f"⚠️ فشل في تثبيت {package}")
            
            logger.info("✅ تم تثبيت المكتبات الأساسية")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تثبيت المتطلبات: {e}")
            return False
    
    def create_demo_data(self):
        """إنشاء بيانات تجريبية"""
        logger.info("📊 إنشاء بيانات تجريبية...")
        
        try:
            import pandas as pd
            import numpy as np
            
            # إنشاء بيانات السوق التجريبية
            np.random.seed(42)
            dates = pd.date_range('2020-01-01', periods=1000, freq='D')
            
            symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN']
            
            for symbol in symbols:
                price = 100 if symbol != 'GOOGL' else 2000
                prices = [price]
                
                for _ in range(999):
                    change = np.random.normal(0, 0.02)
                    price *= (1 + change)
                    prices.append(price)
                
                data = pd.DataFrame({
                    'date': dates,
                    'open': prices,
                    'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
                    'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
                    'close': prices,
                    'volume': np.random.randint(1000000, 10000000, 1000)
                })
                
                # حفظ البيانات
                data_dir = self.project_root / "data" / "demo"
                data_dir.mkdir(parents=True, exist_ok=True)
                
                data.to_csv(data_dir / f"{symbol}_demo.csv", index=False)
            
            logger.info("✅ تم إنشاء البيانات التجريبية")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
            return False
    
    def create_config(self):
        """إنشاء ملف التكوين"""
        logger.info("⚙️ إنشاء ملف التكوين...")
        
        config = {
            "system": {
                "name": "Intelligent Trading System",
                "version": "1.0.0",
                "environment": "demo",
                "debug": True
            },
            "api": {
                "host": "0.0.0.0",
                "port": 8000,
                "reload": True
            },
            "database": {
                "type": "sqlite",
                "path": "data/its_demo.db"
            },
            "trading": {
                "paper_trading": True,
                "demo_mode": True,
                "initial_balance": 100000
            },
            "data": {
                "demo_mode": True,
                "update_interval": 60
            }
        }
        
        try:
            config_file = self.project_root / "config_demo.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info("✅ تم إنشاء ملف التكوين")
            return str(config_file)
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء ملف التكوين: {e}")
            return None
    
    def start_api_server(self):
        """بدء خادم API"""
        logger.info("🌐 بدء خادم API...")
        
        try:
            # إنشاء سكريبت API مبسط
            api_script = self.project_root / "simple_api.py"
            
            api_code = '''
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
import json
from datetime import datetime
import numpy as np

app = FastAPI(title="نظام التداول الذكي المتكامل - العرض التوضيحي")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return HTMLResponse("""
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام التداول الذكي المتكامل</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                color: white;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 20px;
                padding: 30px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            }
            .header {
                text-align: center;
                margin-bottom: 40px;
            }
            .title {
                font-size: 3em;
                margin-bottom: 10px;
                background: linear-gradient(45deg, #FFD700, #FFA500);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .subtitle {
                font-size: 1.2em;
                opacity: 0.9;
            }
            .features {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 40px;
            }
            .feature {
                background: rgba(255, 255, 255, 0.1);
                padding: 20px;
                border-radius: 15px;
                text-align: center;
                transition: transform 0.3s ease;
            }
            .feature:hover {
                transform: translateY(-5px);
            }
            .feature-icon {
                font-size: 3em;
                margin-bottom: 15px;
            }
            .api-links {
                margin-top: 40px;
                text-align: center;
            }
            .api-link {
                display: inline-block;
                margin: 10px;
                padding: 15px 30px;
                background: rgba(255, 255, 255, 0.2);
                color: white;
                text-decoration: none;
                border-radius: 25px;
                transition: all 0.3s ease;
            }
            .api-link:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: scale(1.05);
            }
            .status {
                background: rgba(0, 255, 0, 0.2);
                padding: 10px;
                border-radius: 10px;
                text-align: center;
                margin-bottom: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="status">
                🟢 النظام يعمل بنجاح - ''' + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '''
            </div>
            
            <div class="header">
                <h1 class="title">🧠 نظام التداول الذكي المتكامل</h1>
                <p class="subtitle">Intelligent Trading System (ITS) - نظام تداول متطور بالذكاء الاصطناعي</p>
            </div>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🤖</div>
                    <h3>الذكاء الاصطناعي المتقدم</h3>
                    <p>نماذج التعلم العميق والتعلم المعزز للتنبؤ بالأسعار واتخاذ قرارات التداول الذكية</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">📊</div>
                    <h3>تحليل البيانات الشامل</h3>
                    <p>تحليل فني وأساسي متقدم مع معالجة البيانات في الوقت الفعلي</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <h3>أداء عالي</h3>
                    <p>محرك C++ عالي السرعة مع تسريع CUDA للحوسبة المتوازية</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">🛡️</div>
                    <h3>إدارة المخاطر</h3>
                    <p>نظام متقدم لإدارة المخاطر مع نماذج VaR ومحاكاة مونت كارلو</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">🌐</div>
                    <h3>واجهات متقدمة</h3>
                    <p>REST API و GraphQL مع WebSocket للبيانات الفورية</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">📈</div>
                    <h3>تحليل الأداء</h3>
                    <p>مقاييس شاملة للأداء مع تقارير تفصيلية وتصور تفاعلي</p>
                </div>
            </div>
            
            <div class="api-links">
                <h3>🔗 واجهات برمجة التطبيقات</h3>
                <a href="/docs" class="api-link">📚 توثيق API</a>
                <a href="/health" class="api-link">🏥 فحص الصحة</a>
                <a href="/api/v1/demo-data" class="api-link">📊 بيانات تجريبية</a>
                <a href="/api/v1/demo-signals" class="api-link">🎯 إشارات تجريبية</a>
            </div>
        </div>
    </body>
    </html>
    """)

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "mode": "demo"
    }

@app.get("/api/v1/demo-data")
async def demo_data():
    symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"]
    data = {}
    
    for symbol in symbols:
        price = np.random.uniform(100, 300)
        change = np.random.uniform(-5, 5)
        
        data[symbol] = {
            "price": round(price, 2),
            "change": round(change, 2),
            "change_percent": round(change/price * 100, 2),
            "volume": np.random.randint(1000000, 10000000),
            "timestamp": datetime.now().isoformat()
        }
    
    return {"status": "success", "data": data}

@app.get("/api/v1/demo-signals")
async def demo_signals():
    symbols = ["AAPL", "GOOGL", "MSFT"]
    signals = []
    
    for symbol in symbols:
        action = np.random.choice(["BUY", "SELL", "HOLD"])
        confidence = np.random.uniform(0.6, 0.95)
        
        signals.append({
            "symbol": symbol,
            "action": action,
            "confidence": round(confidence, 2),
            "strategy": "AI_Demo",
            "timestamp": datetime.now().isoformat()
        })
    
    return {"status": "success", "signals": signals}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
            
            with open(api_script, 'w', encoding='utf-8') as f:
                f.write(api_code)
            
            # تشغيل الخادم
            process = subprocess.Popen([
                sys.executable, str(api_script)
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            self.processes.append(process)
            
            # انتظار قصير للتأكد من بدء الخادم
            time.sleep(3)
            
            logger.info("✅ تم بدء خادم API على http://localhost:8000")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء خادم API: {e}")
            return False
    
    def open_browser(self):
        """فتح المتصفح"""
        try:
            time.sleep(2)  # انتظار حتى يصبح الخادم جاهزاً
            webbrowser.open("http://localhost:8000")
            logger.info("🌐 تم فتح المتصفح")
        except Exception as e:
            logger.error(f"❌ خطأ في فتح المتصفح: {e}")
    
    def monitor_system(self):
        """مراقبة النظام"""
        logger.info("📊 بدء مراقبة النظام...")
        
        while self.running:
            try:
                # فحص العمليات
                for i, process in enumerate(self.processes):
                    if process.poll() is not None:
                        logger.warning(f"⚠️ العملية {i} توقفت")
                
                time.sleep(10)
                
            except Exception as e:
                logger.error(f"❌ خطأ في مراقبة النظام: {e}")
                break
    
    def run(self):
        """تشغيل النظام الكامل"""
        logger.info("🚀 بدء تشغيل نظام التداول الذكي المتكامل...")
        
        try:
            # فحص المتطلبات
            if not self.check_requirements():
                return False
            
            # تثبيت المتطلبات الأساسية
            if not self.install_dependencies():
                return False
            
            # إنشاء البيانات التجريبية
            self.create_demo_data()
            
            # إنشاء ملف التكوين
            config_file = self.create_config()
            
            # بدء خادم API
            if not self.start_api_server():
                return False
            
            self.running = True
            
            # فتح المتصفح في خيط منفصل
            browser_thread = threading.Thread(target=self.open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            # بدء مراقبة النظام
            monitor_thread = threading.Thread(target=self.monitor_system)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            logger.info("✅ تم تشغيل النظام بنجاح!")
            logger.info("🌐 الوصول للنظام: http://localhost:8000")
            logger.info("📚 توثيق API: http://localhost:8000/docs")
            logger.info("🛑 اضغط Ctrl+C للإيقاف")
            
            # انتظار الإيقاف
            while self.running:
                time.sleep(1)
            
            return True
            
        except KeyboardInterrupt:
            logger.info("🛑 تم إيقاف النظام بواسطة المستخدم")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل النظام: {e}")
            return False
        finally:
            self.shutdown()
    
    def shutdown(self):
        """إغلاق النظام"""
        logger.info("🔄 إغلاق النظام...")
        
        self.running = False
        
        # إنهاء العمليات
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass
        
        logger.info("✅ تم إغلاق النظام بنجاح")

def main():
    """الدالة الرئيسية"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🧠 نظام التداول الذكي المتكامل 🧠                    ║
    ║              Intelligent Trading System                      ║
    ║                                                              ║
    ║  نظام تداول متطور يدمج الذكاء الاصطناعي والتعلم الآلي      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # إنشاء وتشغيل النظام
    its = ITSQuickStart()
    
    try:
        success = its.run()
        if success:
            logger.info("🎉 تم تشغيل النظام بنجاح!")
        else:
            logger.error("❌ فشل في تشغيل النظام")
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ خطأ غير متوقع: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
