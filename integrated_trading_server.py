#!/usr/bin/env python3
"""
🌐 Integrated Trading Server
خادم التداول المتكامل

- خادم ويب متكامل
- بيانات حقيقية مباشرة
- روبوت ذكي متقدم
- واجهة مستخدم تفاعلية
"""

from flask import Flask, render_template, request, jsonify
from flask_socketio import SocketIO, emit
import asyncio
import threading
import json
import logging
from datetime import datetime
import os

# استيراد المحركات المطورة
from real_time_data_engine import RealTimeDataEngine
from advanced_trading_ai import AdvancedTradingAI

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'trading_secret_key_2024'
socketio = SocketIO(app, cors_allowed_origins="*")

# المحركات العالمية
data_engine = None
ai_assistant = None

class IntegratedTradingServer:
    """خادم التداول المتكامل"""
    
    def __init__(self):
        """تهيئة الخادم"""
        global data_engine, ai_assistant
        
        # تهيئة محرك البيانات
        data_engine = RealTimeDataEngine()
        
        # تهيئة المساعد الذكي
        ai_assistant = AdvancedTradingAI()
        
        # بدء محرك البيانات في خيط منفصل
        self.start_data_engine()
        
        logger.info("🚀 Integrated Trading Server initialized")
    
    def start_data_engine(self):
        """بدء محرك البيانات في خيط منفصل"""
        def run_data_engine():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            async def data_loop():
                data_engine.running = True
                await data_engine.data_update_loop()
            
            loop.run_until_complete(data_loop())
        
        data_thread = threading.Thread(target=run_data_engine, daemon=True)
        data_thread.start()
        logger.info("📊 Data engine started in background")

# إنشاء الخادم المتكامل
server = IntegratedTradingServer()

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/api/market-data')
def get_market_data():
    """API للحصول على بيانات السوق"""
    try:
        all_data = data_engine.get_all_data()
        
        # تحويل البيانات للتنسيق المطلوب
        market_data = {}
        for symbol, data in all_data.items():
            market_data[symbol] = {
                'symbol': data.symbol,
                'price': data.price,
                'change': data.change,
                'change_percent': data.change_percent,
                'volume': data.volume,
                'timestamp': data.timestamp,
                'high': data.high,
                'low': data.low,
                'open': data.open,
                'bid': data.bid,
                'ask': data.ask
            }
        
        return jsonify({
            'success': True,
            'data': market_data,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"❌ Error getting market data: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/symbol/<symbol>')
def get_symbol_data(symbol):
    """API للحصول على بيانات رمز محدد"""
    try:
        data = data_engine.get_symbol_data(symbol.upper())
        
        if data:
            return jsonify({
                'success': True,
                'data': {
                    'symbol': data.symbol,
                    'price': data.price,
                    'change': data.change,
                    'change_percent': data.change_percent,
                    'volume': data.volume,
                    'timestamp': data.timestamp,
                    'high': data.high,
                    'low': data.low,
                    'open': data.open,
                    'bid': data.bid,
                    'ask': data.ask
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Symbol not found'
            }), 404
            
    except Exception as e:
        logger.error(f"❌ Error getting symbol data: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@socketio.on('connect')
def handle_connect():
    """معالجة اتصال العميل"""
    logger.info(f"🔗 Client connected: {request.sid}")
    
    # إرسال البيانات الحالية للعميل الجديد
    try:
        all_data = data_engine.get_all_data()
        for symbol, data in all_data.items():
            emit('market_update', {
                'symbol': data.symbol,
                'price': data.price,
                'change': data.change,
                'change_percent': data.change_percent,
                'volume': data.volume,
                'timestamp': data.timestamp,
                'high': data.high,
                'low': data.low,
                'open': data.open,
                'bid': data.bid,
                'ask': data.ask
            })
    except Exception as e:
        logger.error(f"❌ Error sending initial data: {str(e)}")

@socketio.on('disconnect')
def handle_disconnect():
    """معالجة قطع الاتصال"""
    logger.info(f"🔌 Client disconnected: {request.sid}")

@socketio.on('chat_message')
def handle_chat_message(data):
    """معالجة رسائل الدردشة"""
    try:
        message = data.get('message', '')
        user_id = data.get('user_id', request.sid)
        
        logger.info(f"💬 Chat message from {user_id}: {message}")
        
        # معالجة الرسالة مع المساعد الذكي
        async def process_message():
            response = await ai_assistant.generate_response(message, user_id)
            
            # إرسال الرد للعميل
            socketio.emit('chat_response', {
                'message': response,
                'timestamp': datetime.now().isoformat(),
                'user_id': user_id
            }, room=request.sid)
        
        # تشغيل المعالجة في خيط منفصل
        def run_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(process_message())
        
        threading.Thread(target=run_async, daemon=True).start()
        
    except Exception as e:
        logger.error(f"❌ Error processing chat message: {str(e)}")
        emit('chat_error', {
            'error': 'حدث خطأ في معالجة رسالتك. حاول مرة أخرى.',
            'timestamp': datetime.now().isoformat()
        })

@socketio.on('subscribe_symbol')
def handle_subscribe_symbol(data):
    """الاشتراك في تحديثات رمز محدد"""
    try:
        symbol = data.get('symbol', '').upper()
        logger.info(f"📊 Client {request.sid} subscribed to {symbol}")
        
        # إرسال البيانات الحالية للرمز
        symbol_data = data_engine.get_symbol_data(symbol)
        if symbol_data:
            emit('market_update', {
                'symbol': symbol_data.symbol,
                'price': symbol_data.price,
                'change': symbol_data.change,
                'change_percent': symbol_data.change_percent,
                'volume': symbol_data.volume,
                'timestamp': symbol_data.timestamp,
                'high': symbol_data.high,
                'low': symbol_data.low,
                'open': symbol_data.open,
                'bid': symbol_data.bid,
                'ask': symbol_data.ask
            })
        
    except Exception as e:
        logger.error(f"❌ Error subscribing to symbol: {str(e)}")

# دالة لبث التحديثات للعملاء المتصلين
def broadcast_market_updates():
    """بث تحديثات السوق لجميع العملاء"""
    def update_loop():
        while True:
            try:
                all_data = data_engine.get_all_data()
                for symbol, data in all_data.items():
                    socketio.emit('market_update', {
                        'symbol': data.symbol,
                        'price': data.price,
                        'change': data.change,
                        'change_percent': data.change_percent,
                        'volume': data.volume,
                        'timestamp': data.timestamp,
                        'high': data.high,
                        'low': data.low,
                        'open': data.open,
                        'bid': data.bid,
                        'ask': data.ask
                    })
                
                # انتظار ثانية واحدة
                import time
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"❌ Error broadcasting updates: {str(e)}")
                import time
                time.sleep(5)
    
    # بدء حلقة التحديث في خيط منفصل
    update_thread = threading.Thread(target=update_loop, daemon=True)
    update_thread.start()

# بدء بث التحديثات
broadcast_market_updates()

@app.errorhandler(404)
def not_found(error):
    """معالجة الصفحات غير الموجودة"""
    return jsonify({
        'success': False,
        'error': 'Page not found'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """معالجة الأخطاء الداخلية"""
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500

if __name__ == '__main__':
    print("🌐 Integrated Trading Server")
    print("=" * 50)
    print("🚀 Starting integrated trading platform...")
    print("📊 Real-time data engine: ACTIVE")
    print("🤖 Advanced AI assistant: READY")
    print("🌐 Web server: http://localhost:5000")
    print("⚡ WebSocket: ws://localhost:5000")
    print("=" * 50)
    
    try:
        # تشغيل الخادم
        socketio.run(
            app, 
            host='0.0.0.0', 
            port=5000, 
            debug=False,
            allow_unsafe_werkzeug=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Shutting down server...")
        if data_engine:
            data_engine.stop_server()
        print("👋 Goodbye!")
