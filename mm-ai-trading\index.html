<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M&M AI - منصة التداول الذكية</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1e2a38 50%, #2d3e50 100%);
            color: #ffffff;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(10, 14, 26, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(0, 178, 255, 0.3);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: #00B2FF;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: #ffffff;
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #00B2FF;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 20%, rgba(0, 178, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 30%, rgba(0, 255, 136, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 40% 70%, rgba(0, 178, 255, 0.05) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #00B2FF, #00FF88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            color: #8B9DC3;
            line-height: 1.6;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(45deg, #00B2FF, #00FF88);
            color: #ffffff;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: transform 0.3s, box-shadow 0.3s;
            margin: 0.5rem;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 178, 255, 0.3);
        }

        /* Stats Section */
        .stats {
            padding: 4rem 2rem;
            background: rgba(30, 42, 56, 0.5);
        }

        .stats-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(0, 178, 255, 0.2);
            transition: transform 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #00B2FF;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #8B9DC3;
            font-size: 1.1rem;
        }

        /* Features Section */
        .features {
            padding: 4rem 2rem;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #ffffff;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 15px;
            border: 1px solid rgba(0, 178, 255, 0.2);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 178, 255, 0.2);
        }

        .feature-icon {
            font-size: 2.5rem;
            color: #00B2FF;
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .feature-description {
            color: #8B9DC3;
            line-height: 1.6;
        }

        /* Live Data Section */
        .live-data {
            padding: 4rem 2rem;
            background: rgba(30, 42, 56, 0.5);
        }

        .data-grid {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .data-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 15px;
            border: 1px solid rgba(0, 178, 255, 0.2);
        }

        .asset-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .asset-price {
            font-size: 2rem;
            font-weight: 700;
            color: #00FF88;
            margin-bottom: 0.5rem;
        }

        .asset-change {
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .positive { color: #00FF88; }
        .negative { color: #FF4757; }

        .signal {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .signal.buy {
            background: rgba(0, 255, 136, 0.2);
            color: #00FF88;
            border: 1px solid #00FF88;
        }

        .signal.sell {
            background: rgba(255, 71, 87, 0.2);
            color: #FF4757;
            border: 1px solid #FF4757;
        }

        .signal.hold {
            background: rgba(255, 193, 7, 0.2);
            color: #FFC107;
            border: 1px solid #FFC107;
        }

        /* Chat Section */
        .chat-demo {
            padding: 4rem 2rem;
        }

        .chat-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(0, 178, 255, 0.2);
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            margin-bottom: 1rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }

        .message {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 10px;
            max-width: 80%;
        }

        .user-message {
            background: rgba(0, 178, 255, 0.2);
            margin-left: auto;
            text-align: right;
        }

        .bot-message {
            background: rgba(0, 255, 136, 0.2);
            margin-right: auto;
        }

        .chat-input {
            display: flex;
            gap: 1rem;
        }

        .chat-input input {
            flex: 1;
            padding: 1rem;
            border: 1px solid rgba(0, 178, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            font-family: 'Cairo', sans-serif;
        }

        .chat-input button {
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #00B2FF, #00FF88);
            border: none;
            border-radius: 25px;
            color: #ffffff;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .chat-input button:hover {
            transform: scale(1.05);
        }

        /* Footer */
        .footer {
            background: rgba(10, 14, 26, 0.9);
            padding: 2rem;
            text-align: center;
            border-top: 1px solid rgba(0, 178, 255, 0.3);
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 178, 255, 0.3);
            border-radius: 50%;
            border-top-color: #00B2FF;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .stats-container,
            .features-grid,
            .data-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
