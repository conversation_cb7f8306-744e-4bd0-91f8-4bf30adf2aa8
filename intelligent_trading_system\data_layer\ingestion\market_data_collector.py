"""
📊 جامع البيانات المالية المتقدم
Advanced Market Data Collector

يدعم جمع البيانات من مصادر متعددة:
- Yahoo Finance
- Alpha Vantage  
- Binance (للعملات الرقمية)
- FRED (البيانات الاقتصادية)
- Quandl
- IEX Cloud
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
import yfinance as yf
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, timedelta
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from dataclasses import dataclass
import json
import websocket
import threading
from queue import Queue
import warnings
warnings.filterwarnings('ignore')

@dataclass
class MarketDataConfig:
    """تكوين جامع البيانات المالية"""
    
    # مفاتيح API
    alpha_vantage_key: Optional[str] = None
    iex_cloud_key: Optional[str] = None
    quandl_key: Optional[str] = None
    fred_key: Optional[str] = None
    
    # إعدادات التحديث
    update_interval: int = 60  # ثانية
    batch_size: int = 100
    max_retries: int = 3
    timeout: int = 30
    
    # إعدادات التخزين المؤقت
    cache_enabled: bool = True
    cache_duration: int = 300  # ثانية
    
    # إعدادات الجودة
    data_validation: bool = True
    outlier_detection: bool = True
    missing_data_handling: str = 'interpolate'  # 'drop', 'interpolate', 'forward_fill'

class AdvancedMarketDataCollector:
    """جامع البيانات المالية المتقدم"""
    
    def __init__(self, config: MarketDataConfig = None):
        self.config = config or MarketDataConfig()
        self.logger = self._setup_logger()
        self.cache = {}
        self.session = None
        self.websocket_connections = {}
        self.data_queue = Queue()
        
        # إعداد جلسة HTTP غير متزامنة
        self._setup_session()
        
    def _setup_logger(self):
        """إعداد نظام السجلات"""
        logger = logging.getLogger('MarketDataCollector')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _setup_session(self):
        """إعداد جلسة HTTP"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'M&M AI Trading System/1.0'
        })
    
    async def collect_yahoo_finance_data(self, 
                                       symbols: List[str], 
                                       period: str = '1d',
                                       interval: str = '1m') -> Dict[str, pd.DataFrame]:
        """جمع البيانات من Yahoo Finance"""
        
        self.logger.info(f"جمع بيانات Yahoo Finance لـ {len(symbols)} رمز")
        
        data_dict = {}
        
        # معالجة الرموز في مجموعات
        for i in range(0, len(symbols), self.config.batch_size):
            batch = symbols[i:i + self.config.batch_size]
            
            try:
                # جمع البيانات للمجموعة
                tickers = yf.Tickers(' '.join(batch))
                
                for symbol in batch:
                    try:
                        ticker = yf.Ticker(symbol)
                        data = ticker.history(period=period, interval=interval)
                        
                        if not data.empty:
                            # تنظيف البيانات
                            data = self._clean_data(data, symbol)
                            data_dict[symbol] = data
                            
                            self.logger.debug(f"تم جمع {len(data)} نقطة بيانات لـ {symbol}")
                        else:
                            self.logger.warning(f"لا توجد بيانات لـ {symbol}")
                            
                    except Exception as e:
                        self.logger.error(f"خطأ في جمع بيانات {symbol}: {e}")
                
                # تأخير بين المجموعات لتجنب حدود المعدل
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"خطأ في معالجة المجموعة {batch}: {e}")
        
        return data_dict
    
    async def collect_alpha_vantage_data(self, 
                                       symbols: List[str],
                                       function: str = 'TIME_SERIES_INTRADAY',
                                       interval: str = '1min') -> Dict[str, pd.DataFrame]:
        """جمع البيانات من Alpha Vantage"""
        
        if not self.config.alpha_vantage_key:
            self.logger.warning("مفتاح Alpha Vantage غير متوفر")
            return {}
        
        self.logger.info(f"جمع بيانات Alpha Vantage لـ {len(symbols)} رمز")
        
        data_dict = {}
        base_url = "https://www.alphavantage.co/query"
        
        async with aiohttp.ClientSession() as session:
            for symbol in symbols:
                try:
                    params = {
                        'function': function,
                        'symbol': symbol,
                        'interval': interval,
                        'apikey': self.config.alpha_vantage_key,
                        'outputsize': 'full'
                    }
                    
                    async with session.get(base_url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            # استخراج البيانات الزمنية
                            time_series_key = f'Time Series ({interval})'
                            if time_series_key in data:
                                df = self._parse_alpha_vantage_data(data[time_series_key])
                                df = self._clean_data(df, symbol)
                                data_dict[symbol] = df
                                
                                self.logger.debug(f"تم جمع {len(df)} نقطة بيانات لـ {symbol}")
                            else:
                                self.logger.warning(f"لا توجد بيانات زمنية لـ {symbol}")
                        else:
                            self.logger.error(f"فشل في جمع بيانات {symbol}: {response.status}")
                    
                    # تأخير لتجنب حدود المعدل
                    await asyncio.sleep(12)  # Alpha Vantage: 5 calls per minute
                    
                except Exception as e:
                    self.logger.error(f"خطأ في جمع بيانات Alpha Vantage لـ {symbol}: {e}")
        
        return data_dict
    
    def _parse_alpha_vantage_data(self, time_series_data: Dict) -> pd.DataFrame:
        """تحليل بيانات Alpha Vantage"""
        
        data_list = []
        
        for timestamp, values in time_series_data.items():
            data_list.append({
                'timestamp': pd.to_datetime(timestamp),
                'open': float(values['1. open']),
                'high': float(values['2. high']),
                'low': float(values['3. low']),
                'close': float(values['4. close']),
                'volume': int(values['5. volume'])
            })
        
        df = pd.DataFrame(data_list)
        df.set_index('timestamp', inplace=True)
        df.sort_index(inplace=True)
        
        return df
    
    async def collect_binance_data(self, 
                                 symbols: List[str],
                                 interval: str = '1m',
                                 limit: int = 1000) -> Dict[str, pd.DataFrame]:
        """جمع بيانات العملات الرقمية من Binance"""
        
        self.logger.info(f"جمع بيانات Binance لـ {len(symbols)} رمز")
        
        data_dict = {}
        base_url = "https://api.binance.com/api/v3/klines"
        
        async with aiohttp.ClientSession() as session:
            for symbol in symbols:
                try:
                    params = {
                        'symbol': symbol.replace('-', ''),  # تحويل BTC-USDT إلى BTCUSDT
                        'interval': interval,
                        'limit': limit
                    }
                    
                    async with session.get(base_url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            df = self._parse_binance_data(data)
                            df = self._clean_data(df, symbol)
                            data_dict[symbol] = df
                            
                            self.logger.debug(f"تم جمع {len(df)} نقطة بيانات لـ {symbol}")
                        else:
                            self.logger.error(f"فشل في جمع بيانات {symbol}: {response.status}")
                    
                    await asyncio.sleep(0.1)  # تأخير قصير
                    
                except Exception as e:
                    self.logger.error(f"خطأ في جمع بيانات Binance لـ {symbol}: {e}")
        
        return data_dict
    
    def _parse_binance_data(self, klines_data: List) -> pd.DataFrame:
        """تحليل بيانات Binance"""
        
        data_list = []
        
        for kline in klines_data:
            data_list.append({
                'timestamp': pd.to_datetime(kline[0], unit='ms'),
                'open': float(kline[1]),
                'high': float(kline[2]),
                'low': float(kline[3]),
                'close': float(kline[4]),
                'volume': float(kline[5])
            })
        
        df = pd.DataFrame(data_list)
        df.set_index('timestamp', inplace=True)
        df.sort_index(inplace=True)
        
        return df
    
    def _clean_data(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """تنظيف وتحسين جودة البيانات"""
        
        if not self.config.data_validation:
            return data
        
        original_length = len(data)
        
        # إزالة القيم المفقودة
        if self.config.missing_data_handling == 'drop':
            data = data.dropna()
        elif self.config.missing_data_handling == 'interpolate':
            data = data.interpolate(method='linear')
        elif self.config.missing_data_handling == 'forward_fill':
            data = data.fillna(method='ffill')
        
        # كشف القيم الشاذة
        if self.config.outlier_detection:
            data = self._remove_outliers(data)
        
        # التحقق من صحة البيانات
        data = self._validate_ohlc_data(data)
        
        cleaned_length = len(data)
        if cleaned_length < original_length:
            self.logger.info(f"تم تنظيف {original_length - cleaned_length} نقطة بيانات لـ {symbol}")
        
        return data
    
    def _remove_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """إزالة القيم الشاذة باستخدام IQR"""
        
        for column in ['open', 'high', 'low', 'close']:
            if column in data.columns:
                Q1 = data[column].quantile(0.25)
                Q3 = data[column].quantile(0.75)
                IQR = Q3 - Q1
                
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                # تحديد القيم الشاذة
                outliers = (data[column] < lower_bound) | (data[column] > upper_bound)
                
                # استبدال القيم الشاذة بالقيم المتوسطة
                data.loc[outliers, column] = data[column].median()
        
        return data
    
    def _validate_ohlc_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """التحقق من صحة بيانات OHLC"""
        
        if all(col in data.columns for col in ['open', 'high', 'low', 'close']):
            # التحقق من أن High >= max(Open, Close) و Low <= min(Open, Close)
            invalid_high = data['high'] < data[['open', 'close']].max(axis=1)
            invalid_low = data['low'] > data[['open', 'close']].min(axis=1)
            
            # تصحيح البيانات غير الصحيحة
            data.loc[invalid_high, 'high'] = data.loc[invalid_high, ['open', 'close']].max(axis=1)
            data.loc[invalid_low, 'low'] = data.loc[invalid_low, ['open', 'close']].min(axis=1)
        
        return data
    
    def setup_real_time_stream(self, symbols: List[str], callback_function):
        """إعداد تدفق البيانات في الوقت الفعلي"""
        
        self.logger.info(f"إعداد تدفق البيانات الفورية لـ {len(symbols)} رمز")
        
        # إعداد WebSocket لـ Binance
        def on_message(ws, message):
            try:
                data = json.loads(message)
                processed_data = self._process_websocket_data(data)
                callback_function(processed_data)
            except Exception as e:
                self.logger.error(f"خطأ في معالجة رسالة WebSocket: {e}")
        
        def on_error(ws, error):
            self.logger.error(f"خطأ WebSocket: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            self.logger.info("تم إغلاق اتصال WebSocket")
        
        # إنشاء اتصالات WebSocket للرموز
        for symbol in symbols:
            if symbol.endswith('-USDT') or symbol.endswith('-USD'):
                # Binance WebSocket
                binance_symbol = symbol.replace('-', '').lower()
                ws_url = f"wss://stream.binance.com:9443/ws/{binance_symbol}@ticker"
                
                ws = websocket.WebSocketApp(
                    ws_url,
                    on_message=on_message,
                    on_error=on_error,
                    on_close=on_close
                )
                
                # تشغيل WebSocket في خيط منفصل
                ws_thread = threading.Thread(target=ws.run_forever)
                ws_thread.daemon = True
                ws_thread.start()
                
                self.websocket_connections[symbol] = ws
    
    def _process_websocket_data(self, data: Dict) -> Dict:
        """معالجة بيانات WebSocket"""
        
        return {
            'symbol': data.get('s', ''),
            'price': float(data.get('c', 0)),
            'volume': float(data.get('v', 0)),
            'change': float(data.get('P', 0)),
            'timestamp': datetime.now()
        }
    
    async def collect_economic_data(self, indicators: List[str]) -> Dict[str, pd.DataFrame]:
        """جمع البيانات الاقتصادية من FRED"""
        
        if not self.config.fred_key:
            self.logger.warning("مفتاح FRED غير متوفر")
            return {}
        
        self.logger.info(f"جمع البيانات الاقتصادية لـ {len(indicators)} مؤشر")
        
        data_dict = {}
        base_url = "https://api.stlouisfed.org/fred/series/observations"
        
        async with aiohttp.ClientSession() as session:
            for indicator in indicators:
                try:
                    params = {
                        'series_id': indicator,
                        'api_key': self.config.fred_key,
                        'file_type': 'json',
                        'limit': 1000
                    }
                    
                    async with session.get(base_url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            df = self._parse_fred_data(data)
                            data_dict[indicator] = df
                            
                            self.logger.debug(f"تم جمع {len(df)} نقطة بيانات لـ {indicator}")
                        else:
                            self.logger.error(f"فشل في جمع بيانات {indicator}: {response.status}")
                    
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    self.logger.error(f"خطأ في جمع بيانات FRED لـ {indicator}: {e}")
        
        return data_dict
    
    def _parse_fred_data(self, fred_data: Dict) -> pd.DataFrame:
        """تحليل بيانات FRED"""
        
        observations = fred_data.get('observations', [])
        data_list = []
        
        for obs in observations:
            if obs['value'] != '.':  # تجاهل القيم المفقودة
                data_list.append({
                    'date': pd.to_datetime(obs['date']),
                    'value': float(obs['value'])
                })
        
        df = pd.DataFrame(data_list)
        if not df.empty:
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)
        
        return df
    
    async def collect_all_data(self, 
                             symbols: List[str],
                             data_sources: List[str] = None) -> Dict[str, Dict[str, pd.DataFrame]]:
        """جمع البيانات من جميع المصادر"""
        
        data_sources = data_sources or ['yahoo', 'binance']
        all_data = {}
        
        tasks = []
        
        if 'yahoo' in data_sources:
            tasks.append(('yahoo', self.collect_yahoo_finance_data(symbols)))
        
        if 'alpha_vantage' in data_sources:
            tasks.append(('alpha_vantage', self.collect_alpha_vantage_data(symbols)))
        
        if 'binance' in data_sources:
            crypto_symbols = [s for s in symbols if '-' in s]
            if crypto_symbols:
                tasks.append(('binance', self.collect_binance_data(crypto_symbols)))
        
        # تنفيذ جميع المهام بشكل متوازي
        for source_name, task in tasks:
            try:
                data = await task
                all_data[source_name] = data
                self.logger.info(f"تم جمع البيانات من {source_name}: {len(data)} رمز")
            except Exception as e:
                self.logger.error(f"خطأ في جمع البيانات من {source_name}: {e}")
        
        return all_data
    
    def close(self):
        """إغلاق جميع الاتصالات"""
        
        # إغلاق WebSocket connections
        for symbol, ws in self.websocket_connections.items():
            try:
                ws.close()
                self.logger.info(f"تم إغلاق اتصال WebSocket لـ {symbol}")
            except Exception as e:
                self.logger.error(f"خطأ في إغلاق WebSocket لـ {symbol}: {e}")
        
        # إغلاق جلسة HTTP
        if self.session:
            self.session.close()

# مثال على الاستخدام
if __name__ == "__main__":
    async def main():
        # إعداد التكوين
        config = MarketDataConfig(
            alpha_vantage_key="YOUR_API_KEY",  # استبدل بمفتاحك
            update_interval=60,
            data_validation=True
        )
        
        # إنشاء جامع البيانات
        collector = AdvancedMarketDataCollector(config)
        
        # قائمة الرموز
        symbols = ['AAPL', 'GOOGL', 'MSFT', 'BTC-USDT', 'ETH-USDT']
        
        # جمع البيانات
        all_data = await collector.collect_all_data(symbols)
        
        # عرض النتائج
        for source, data in all_data.items():
            print(f"\n📊 بيانات {source}:")
            for symbol, df in data.items():
                print(f"   {symbol}: {len(df)} نقطة بيانات")
                if not df.empty:
                    print(f"      آخر سعر: {df['close'].iloc[-1]:.2f}")
        
        # إغلاق الاتصالات
        collector.close()
    
    # تشغيل المثال
    asyncio.run(main())
