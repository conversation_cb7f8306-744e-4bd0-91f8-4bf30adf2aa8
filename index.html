<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M&M AI - منصة التداول الذكية</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1e2a38 50%, #2d3e50 100%);
            color: #ffffff;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(10, 14, 26, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(0, 178, 255, 0.3);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: #00B2FF;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: #ffffff;
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #00B2FF;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a"><stop offset="0" stop-color="%2300B2FF" stop-opacity="0.1"/><stop offset="1" stop-color="%2300B2FF" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #00B2FF, #00FF88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            color: #8B9DC3;
            line-height: 1.6;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(45deg, #00B2FF, #00FF88);
            color: #ffffff;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: transform 0.3s, box-shadow 0.3s;
            margin: 0.5rem;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 178, 255, 0.3);
        }

        /* Stats Section */
        .stats {
            padding: 4rem 2rem;
            background: rgba(30, 42, 56, 0.5);
        }

        .stats-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(0, 178, 255, 0.2);
            transition: transform 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #00B2FF;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #8B9DC3;
            font-size: 1.1rem;
        }

        /* Features Section */
        .features {
            padding: 4rem 2rem;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #ffffff;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 15px;
            border: 1px solid rgba(0, 178, 255, 0.2);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 178, 255, 0.2);
        }

        .feature-icon {
            font-size: 2.5rem;
            color: #00B2FF;
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .feature-description {
            color: #8B9DC3;
            line-height: 1.6;
        }

        /* Live Data Section */
        .live-data {
            padding: 4rem 2rem;
            background: rgba(30, 42, 56, 0.5);
        }

        .data-grid {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .data-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 15px;
            border: 1px solid rgba(0, 178, 255, 0.2);
        }

        .asset-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .asset-price {
            font-size: 2rem;
            font-weight: 700;
            color: #00FF88;
            margin-bottom: 0.5rem;
        }

        .asset-change {
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .positive { color: #00FF88; }
        .negative { color: #FF4757; }

        .signal {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .signal.buy {
            background: rgba(0, 255, 136, 0.2);
            color: #00FF88;
            border: 1px solid #00FF88;
        }

        .signal.sell {
            background: rgba(255, 71, 87, 0.2);
            color: #FF4757;
            border: 1px solid #FF4757;
        }

        .signal.hold {
            background: rgba(255, 193, 7, 0.2);
            color: #FFC107;
            border: 1px solid #FFC107;
        }

        /* Chat Section */
        .chat-demo {
            padding: 4rem 2rem;
        }

        .chat-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(0, 178, 255, 0.2);
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            margin-bottom: 1rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }

        .message {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 10px;
            max-width: 80%;
        }

        .user-message {
            background: rgba(0, 178, 255, 0.2);
            margin-left: auto;
            text-align: right;
        }

        .bot-message {
            background: rgba(0, 255, 136, 0.2);
            margin-right: auto;
        }

        .chat-input {
            display: flex;
            gap: 1rem;
        }

        .chat-input input {
            flex: 1;
            padding: 1rem;
            border: 1px solid rgba(0, 178, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            font-family: 'Cairo', sans-serif;
        }

        .chat-input button {
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #00B2FF, #00FF88);
            border: none;
            border-radius: 25px;
            color: #ffffff;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .chat-input button:hover {
            transform: scale(1.05);
        }

        /* Footer */
        .footer {
            background: rgba(10, 14, 26, 0.9);
            padding: 2rem;
            text-align: center;
            border-top: 1px solid rgba(0, 178, 255, 0.3);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .stats-container,
            .features-grid,
            .data-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 178, 255, 0.3);
            border-radius: 50%;
            border-top-color: #00B2FF;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <a href="#" class="logo">
                <i class="fas fa-chart-line"></i> M&M AI
            </a>
            <ul class="nav-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#features">المميزات</a></li>
                <li><a href="#data">البيانات المباشرة</a></li>
                <li><a href="#chat">الروبوت الذكي</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1>منصة التداول الذكية</h1>
            <p>
                أول منصة تداول عربية تدمج الذكاء الاصطناعي والتحليل الفني المتقدم
                <br>
                لتقديم توصيات تداول دقيقة وموثوقة
            </p>
            <a href="#features" class="cta-button">
                <i class="fas fa-rocket"></i> اكتشف المميزات
            </a>
            <a href="#chat" class="cta-button">
                <i class="fas fa-robot"></i> جرب الروبوت الذكي
            </a>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div class="stat-label">مراحل مكتملة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">15+</div>
                <div class="stat-label">مؤشر فني</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">80%</div>
                <div class="stat-label">دقة الإشارات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">24/7</div>
                <div class="stat-label">مراقبة مستمرة</div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="features-container">
            <h2 class="section-title">المميزات المتقدمة</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="feature-title">ذكاء اصطناعي متقدم</h3>
                    <p class="feature-description">
                        محركات ذكاء اصطناعي متطورة تحلل الأسواق وتقدم توصيات دقيقة بناءً على 15+ مؤشر فني
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">تحليل فني شامل</h3>
                    <p class="feature-description">
                        تحليل فني متقدم يشمل RSI، MACD، المتوسطات المتحركة، ونطاقات بولينجر
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3 class="feature-title">بيانات حقيقية</h3>
                    <p class="feature-description">
                        بيانات مالية حقيقية ومحدثة من Yahoo Finance وأسواق عالمية موثوقة
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="feature-title">روبوت محادثة ذكي</h3>
                    <p class="feature-description">
                        مساعد ذكي يفهم اللغة العربية ويجيب على أسئلة التداول ويقدم النصائح
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">إدارة المخاطر</h3>
                    <p class="feature-description">
                        نظام متقدم لإدارة المخاطر مع حساب نسب المخاطرة والمكافأة تلقائياً
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="feature-title">أداء عالي</h3>
                    <p class="feature-description">
                        محرك C++ عالي الأداء للمعالجة السريعة والتحليل في الوقت الفعلي
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Live Data Section -->
    <section id="data" class="live-data">
        <div class="features-container">
            <h2 class="section-title">البيانات المباشرة والتوصيات</h2>
            <div class="data-grid">
                <div class="data-card">
                    <div class="asset-name">
                        <i class="fab fa-apple"></i> Apple Inc. (AAPL)
                    </div>
                    <div class="asset-price">$198.25</div>
                    <div class="asset-change negative">
                        <i class="fas fa-arrow-down"></i> -0.19%
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <strong>RSI:</strong> 45.2 (محايد)<br>
                        <strong>MACD:</strong> هابط<br>
                        <strong>الثقة:</strong> 65%
                    </div>
                    <span class="signal hold">
                        <i class="fas fa-pause"></i> HOLD
                    </span>
                </div>

                <div class="data-card">
                    <div class="asset-name">
                        <i class="fas fa-coins"></i> الذهب (GOLD)
                    </div>
                    <div class="asset-price">$3,296.60</div>
                    <div class="asset-change negative">
                        <i class="fas fa-arrow-down"></i> -2.51%
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <strong>RSI:</strong> 28.5 (تشبع بيعي)<br>
                        <strong>MACD:</strong> هابط قوي<br>
                        <strong>الثقة:</strong> 75%
                    </div>
                    <span class="signal buy">
                        <i class="fas fa-arrow-up"></i> BUY
                    </span>
                </div>

                <div class="data-card">
                    <div class="asset-name">
                        <i class="fab fa-bitcoin"></i> Bitcoin (BTC)
                    </div>
                    <div class="asset-price">$43,250.00</div>
                    <div class="asset-change positive">
                        <i class="fas fa-arrow-up"></i> +1.25%
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <strong>RSI:</strong> 58.3 (محايد)<br>
                        <strong>MACD:</strong> صاعد<br>
                        <strong>الثقة:</strong> 70%
                    </div>
                    <span class="signal buy">
                        <i class="fas fa-arrow-up"></i> BUY
                    </span>
                </div>
            </div>
        </div>
    </section>

    <!-- Chat Demo Section -->
    <section id="chat" class="chat-demo">
        <div class="features-container">
            <h2 class="section-title">الروبوت الذكي - M&M AI Assistant</h2>
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message bot-message">
                        <strong>🤖 M&M AI:</strong> مرحباً! أنا مساعدك الذكي للتداول. كيف يمكنني مساعدتك اليوم؟
                    </div>
                    <div class="message user-message">
                        <strong>👤 أنت:</strong> كم سعر AAPL؟
                    </div>
                    <div class="message bot-message">
                        <strong>🤖 M&M AI:</strong> 💰 سعر AAPL الحالي: $198.25<br>
                        📈 التغيير: -0.19%<br>
                        🚨 الإشارة: HOLD (65% ثقة)<br>
                        💡 السبب: إشارات متضاربة، انتظار كسر واضح للمستويات
                    </div>
                    <div class="message user-message">
                        <strong>👤 أنت:</strong> حلل لي الذهب
                    </div>
                    <div class="message bot-message">
                        <strong>🤖 M&M AI:</strong> 🥇 تحليل الذهب:<br>
                        💰 السعر: $3,296.60 (-2.51%)<br>
                        📊 RSI: 28.5 (تشبع بيعي قوي)<br>
                        🚨 الإشارة: BUY (75% ثقة)<br>
                        💡 فرصة شراء ممتازة عند مستويات التشبع البيعي
                    </div>
                </div>
                <div class="chat-input">
                    <input type="text" id="chatInput" placeholder="اكتب سؤالك هنا..." />
                    <button onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i> إرسال
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div style="max-width: 1200px; margin: 0 auto;">
            <p>&copy; 2024 M&M AI Trading Platform. جميع الحقوق محفوظة.</p>
            <p style="margin-top: 0.5rem; color: #8B9DC3;">
                منصة تداول ذكية مطورة بأحدث تقنيات الذكاء الاصطناعي
            </p>
        </div>
    </footer>

    <script>
        // Chat functionality
        function sendMessage() {
            const input = document.getElementById('chatInput');
            const messages = document.getElementById('chatMessages');
            const message = input.value.trim();

            if (!message) return;

            // Add user message
            const userMsg = document.createElement('div');
            userMsg.className = 'message user-message';
            userMsg.innerHTML = `<strong>👤 أنت:</strong> ${message}`;
            messages.appendChild(userMsg);

            // Clear input
            input.value = '';

            // Show loading
            const loadingMsg = document.createElement('div');
            loadingMsg.className = 'message bot-message';
            loadingMsg.innerHTML = `<strong>🤖 M&M AI:</strong> <span class="loading"></span> جاري التحليل...`;
            messages.appendChild(loadingMsg);

            // Scroll to bottom
            messages.scrollTop = messages.scrollHeight;

            // Simulate AI response
            setTimeout(() => {
                messages.removeChild(loadingMsg);

                const botMsg = document.createElement('div');
                botMsg.className = 'message bot-message';

                let response = getAIResponse(message);
                botMsg.innerHTML = `<strong>🤖 M&M AI:</strong> ${response}`;

                messages.appendChild(botMsg);
                messages.scrollTop = messages.scrollHeight;
            }, 2000);
        }

        function getAIResponse(message) {
            const msg = message.toLowerCase();

            if (msg.includes('aapl') || msg.includes('أبل') || msg.includes('apple')) {
                return `📊 تحليل AAPL:<br>
                💰 السعر: $198.25<br>
                📈 RSI: 45.2 (محايد)<br>
                🚨 التوصية: HOLD<br>
                💡 انتظار إشارة أوضح للدخول`;
            }

            if (msg.includes('ذهب') || msg.includes('gold')) {
                return `🥇 تحليل الذهب:<br>
                💰 السعر: $3,296.60<br>
                📊 RSI: 28.5 (تشبع بيعي)<br>
                🚨 التوصية: BUY<br>
                💡 فرصة شراء ممتازة`;
            }

            if (msg.includes('rsi')) {
                return `📈 مؤشر RSI:<br>
                • أقل من 30: تشبع بيعي (فرصة شراء)<br>
                • أكبر من 70: تشبع شرائي (فرصة بيع)<br>
                • 30-70: منطقة محايدة`;
            }

            if (msg.includes('مخاطر') || msg.includes('risk')) {
                return `🛡️ إدارة المخاطر:<br>
                1️⃣ لا تخاطر بأكثر من 2% من رأس المال<br>
                2️⃣ استخدم وقف الخسارة دائماً<br>
                3️⃣ نسبة المخاطرة/المكافأة: 1:2<br>
                4️⃣ نوع محفظتك`;
            }

            return `🤖 شكراً لسؤالك! يمكنني مساعدتك في:<br>
            📊 تحليل الأسهم والسلع<br>
            💰 معرفة الأسعار الحالية<br>
            📚 تعليم مفاهيم التداول<br>
            🛡️ إدارة المخاطر<br><br>
            جرب أن تسأل: "حلل لي AAPL" أو "ما هو RSI؟"`;
        }

        // Enter key support
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Auto-update prices (simulation)
        function updatePrices() {
            console.log('Updating prices...');
        }

        setInterval(updatePrices, 30000);

        // Welcome animation
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 1s';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>
