/**
 * @file order_matching_engine.cpp
 * @brief Ultra-fast order matching engine implementation
 * <AUTHOR> AI Trading System
 * @version 1.0.0
 */

#include "../include/order_matching_engine.h"
#include <algorithm>
#include <iomanip>

namespace TradingEngine {

OrderMatchingEngine::OrderMatchingEngine() 
    : running_(false), total_orders_processed_(0), total_trades_executed_(0) {
    
    Logger::info("OrderMatchingEngine initialized");
    
    // Start processing thread
    processing_thread_ = std::thread(&OrderMatchingEngine::processingLoop, this);
}

OrderMatchingEngine::~OrderMatchingEngine() {
    stop();
    
    if (processing_thread_.joinable()) {
        processing_thread_.join();
    }
    
    Logger::info("OrderMatchingEngine destroyed");
}

void OrderMatchingEngine::start() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!running_) {
        running_ = true;
        Logger::info("OrderMatchingEngine started");
    }
}

void OrderMatchingEngine::stop() {
    {
        std::lock_guard<std::mutex> lock(mutex_);
        running_ = false;
    }
    condition_.notify_all();
    Logger::info("OrderMatchingEngine stopped");
}

OrderId OrderMatchingEngine::submitOrder(const Order& order) {
    if (!running_) {
        Logger::warning("Cannot submit order - engine not running");
        return "";
    }
    
    // Validate order
    if (!validateOrder(order)) {
        Logger::warning("Order validation failed for order: " + order.id);
        return "";
    }
    
    // Add to processing queue
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        order_queue_.push(order);
        total_orders_processed_++;
    }
    
    condition_.notify_one();
    
    Logger::debug("Order submitted: " + order.id + " for " + order.symbol);
    return order.id;
}

bool OrderMatchingEngine::cancelOrder(const OrderId& order_id) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Find and remove order from order books
    for (auto& [symbol, book] : order_books_) {
        // Check buy orders
        auto& buy_orders = book.buy_orders;
        for (auto it = buy_orders.begin(); it != buy_orders.end(); ++it) {
            if (it->second.id == order_id) {
                buy_orders.erase(it);
                
                // Update order status
                Order cancelled_order = it->second;
                cancelled_order.status = OrderStatus::CANCELLED;
                cancelled_order.updated_time = Utils::getCurrentTime();
                
                // Store in completed orders
                completed_orders_[order_id] = cancelled_order;
                
                Logger::info("Order cancelled: " + order_id);
                return true;
            }
        }
        
        // Check sell orders
        auto& sell_orders = book.sell_orders;
        for (auto it = sell_orders.begin(); it != sell_orders.end(); ++it) {
            if (it->second.id == order_id) {
                sell_orders.erase(it);
                
                // Update order status
                Order cancelled_order = it->second;
                cancelled_order.status = OrderStatus::CANCELLED;
                cancelled_order.updated_time = Utils::getCurrentTime();
                
                // Store in completed orders
                completed_orders_[order_id] = cancelled_order;
                
                Logger::info("Order cancelled: " + order_id);
                return true;
            }
        }
    }
    
    Logger::warning("Order not found for cancellation: " + order_id);
    return false;
}

Order OrderMatchingEngine::getOrder(const OrderId& order_id) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Check completed orders first
    auto it = completed_orders_.find(order_id);
    if (it != completed_orders_.end()) {
        return it->second;
    }
    
    // Check active orders in order books
    for (const auto& [symbol, book] : order_books_) {
        // Check buy orders
        for (const auto& [price, order] : book.buy_orders) {
            if (order.id == order_id) {
                return order;
            }
        }
        
        // Check sell orders
        for (const auto& [price, order] : book.sell_orders) {
            if (order.id == order_id) {
                return order;
            }
        }
    }
    
    return Order{}; // Return empty order if not found
}

std::vector<Order> OrderMatchingEngine::getActiveOrders(const Symbol& symbol) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::vector<Order> active_orders;
    
    auto it = order_books_.find(symbol);
    if (it != order_books_.end()) {
        const auto& book = it->second;
        
        // Add buy orders
        for (const auto& [price, order] : book.buy_orders) {
            active_orders.push_back(order);
        }
        
        // Add sell orders
        for (const auto& [price, order] : book.sell_orders) {
            active_orders.push_back(order);
        }
    }
    
    return active_orders;
}

std::vector<Trade> OrderMatchingEngine::getTrades(const Symbol& symbol, size_t count) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = trade_history_.find(symbol);
    if (it == trade_history_.end()) {
        return {};
    }
    
    const auto& trades = it->second;
    if (trades.empty()) {
        return {};
    }
    
    size_t start_idx = (count >= trades.size()) ? 0 : trades.size() - count;
    return std::vector<Trade>(trades.begin() + start_idx, trades.end());
}

OrderBook OrderMatchingEngine::getOrderBook(const Symbol& symbol) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = order_books_.find(symbol);
    if (it != order_books_.end()) {
        return it->second;
    }
    
    return OrderBook{};
}

MarketDepth OrderMatchingEngine::getMarketDepth(const Symbol& symbol, size_t levels) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    MarketDepth depth;
    depth.symbol = symbol;
    depth.timestamp = Utils::getCurrentTime();
    
    auto it = order_books_.find(symbol);
    if (it == order_books_.end()) {
        return depth;
    }
    
    const auto& book = it->second;
    
    // Get bid levels (buy orders, highest price first)
    auto buy_it = book.buy_orders.rbegin();
    for (size_t i = 0; i < levels && buy_it != book.buy_orders.rend(); ++i, ++buy_it) {
        depth.bids.emplace_back(buy_it->first, buy_it->second.quantity - buy_it->second.filled_quantity);
    }
    
    // Get ask levels (sell orders, lowest price first)
    auto sell_it = book.sell_orders.begin();
    for (size_t i = 0; i < levels && sell_it != book.sell_orders.end(); ++i, ++sell_it) {
        depth.asks.emplace_back(sell_it->first, sell_it->second.quantity - sell_it->second.filled_quantity);
    }
    
    return depth;
}

EngineStatistics OrderMatchingEngine::getStatistics() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    EngineStatistics stats;
    stats.total_orders_processed = total_orders_processed_;
    stats.total_trades_executed = total_trades_executed_;
    stats.active_symbols = order_books_.size();
    
    // Calculate total active orders
    for (const auto& [symbol, book] : order_books_) {
        stats.active_orders += book.buy_orders.size() + book.sell_orders.size();
    }
    
    return stats;
}

void OrderMatchingEngine::processingLoop() {
    Logger::info("Order processing loop started");
    
    while (running_) {
        Order order;
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            condition_.wait(lock, [this] { return !order_queue_.empty() || !running_; });
            
            if (!running_) break;
            
            if (!order_queue_.empty()) {
                order = order_queue_.front();
                order_queue_.pop();
            } else {
                continue;
            }
        }
        
        // Process the order
        processOrder(order);
    }
    
    Logger::info("Order processing loop stopped");
}

void OrderMatchingEngine::processOrder(Order order) {
    Utils::Timer timer;
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Get or create order book for symbol
    auto& book = order_books_[order.symbol];
    
    if (order.type == OrderType::MARKET) {
        processMarketOrder(order, book);
    } else if (order.type == OrderType::LIMIT) {
        processLimitOrder(order, book);
    }
    
    // Log processing time for performance monitoring
    double processing_time = timer.elapsedUs();
    if (processing_time > 1000.0) { // Log if > 1ms
        Logger::warning("Slow order processing: " + std::to_string(processing_time) + "us for order " + order.id);
    }
}

void OrderMatchingEngine::processMarketOrder(Order& order, OrderBook& book) {
    if (order.side == OrderSide::BUY) {
        // Match against sell orders (asks)
        auto& sell_orders = book.sell_orders;
        
        while (order.filled_quantity < order.quantity && !sell_orders.empty()) {
            auto it = sell_orders.begin(); // Lowest price first
            auto& matching_order = it->second;
            
            Volume trade_quantity = std::min(
                order.quantity - order.filled_quantity,
                matching_order.quantity - matching_order.filled_quantity
            );
            
            Price trade_price = matching_order.price;
            
            // Execute trade
            executeTrade(order, matching_order, trade_price, trade_quantity);
            
            // Remove fully filled order
            if (matching_order.filled_quantity >= matching_order.quantity) {
                matching_order.status = OrderStatus::FILLED;
                matching_order.updated_time = Utils::getCurrentTime();
                completed_orders_[matching_order.id] = matching_order;
                sell_orders.erase(it);
            }
        }
    } else { // SELL
        // Match against buy orders (bids)
        auto& buy_orders = book.buy_orders;
        
        while (order.filled_quantity < order.quantity && !buy_orders.empty()) {
            auto it = buy_orders.rbegin(); // Highest price first
            auto& matching_order = it->second;
            
            Volume trade_quantity = std::min(
                order.quantity - order.filled_quantity,
                matching_order.quantity - matching_order.filled_quantity
            );
            
            Price trade_price = matching_order.price;
            
            // Execute trade
            executeTrade(matching_order, order, trade_price, trade_quantity);
            
            // Remove fully filled order
            if (matching_order.filled_quantity >= matching_order.quantity) {
                matching_order.status = OrderStatus::FILLED;
                matching_order.updated_time = Utils::getCurrentTime();
                completed_orders_[matching_order.id] = matching_order;
                buy_orders.erase(std::next(it).base());
            }
        }
    }
    
    // Update order status
    if (order.filled_quantity >= order.quantity) {
        order.status = OrderStatus::FILLED;
    } else if (order.filled_quantity > 0) {
        order.status = OrderStatus::PARTIALLY_FILLED;
    } else {
        order.status = OrderStatus::REJECTED; // No liquidity
    }
    
    order.updated_time = Utils::getCurrentTime();
    completed_orders_[order.id] = order;
}

void OrderMatchingEngine::processLimitOrder(Order& order, OrderBook& book) {
    if (order.side == OrderSide::BUY) {
        // Try to match against sell orders first
        auto& sell_orders = book.sell_orders;
        
        while (order.filled_quantity < order.quantity && !sell_orders.empty()) {
            auto it = sell_orders.begin();
            auto& matching_order = it->second;
            
            // Only match if our bid price >= ask price
            if (order.price < matching_order.price) {
                break;
            }
            
            Volume trade_quantity = std::min(
                order.quantity - order.filled_quantity,
                matching_order.quantity - matching_order.filled_quantity
            );
            
            Price trade_price = matching_order.price; // Take the ask price
            
            // Execute trade
            executeTrade(order, matching_order, trade_price, trade_quantity);
            
            // Remove fully filled order
            if (matching_order.filled_quantity >= matching_order.quantity) {
                matching_order.status = OrderStatus::FILLED;
                matching_order.updated_time = Utils::getCurrentTime();
                completed_orders_[matching_order.id] = matching_order;
                sell_orders.erase(it);
            }
        }
        
        // Add remaining quantity to order book
        if (order.filled_quantity < order.quantity) {
            order.status = (order.filled_quantity > 0) ? OrderStatus::PARTIALLY_FILLED : OrderStatus::PENDING;
            order.updated_time = Utils::getCurrentTime();
            book.buy_orders[order.price] = order;
        } else {
            order.status = OrderStatus::FILLED;
            order.updated_time = Utils::getCurrentTime();
            completed_orders_[order.id] = order;
        }
        
    } else { // SELL
        // Try to match against buy orders first
        auto& buy_orders = book.buy_orders;
        
        while (order.filled_quantity < order.quantity && !buy_orders.empty()) {
            auto it = buy_orders.rbegin();
            auto& matching_order = it->second;
            
            // Only match if our ask price <= bid price
            if (order.price > matching_order.price) {
                break;
            }
            
            Volume trade_quantity = std::min(
                order.quantity - order.filled_quantity,
                matching_order.quantity - matching_order.filled_quantity
            );
            
            Price trade_price = matching_order.price; // Take the bid price
            
            // Execute trade
            executeTrade(matching_order, order, trade_price, trade_quantity);
            
            // Remove fully filled order
            if (matching_order.filled_quantity >= matching_order.quantity) {
                matching_order.status = OrderStatus::FILLED;
                matching_order.updated_time = Utils::getCurrentTime();
                completed_orders_[matching_order.id] = matching_order;
                buy_orders.erase(std::next(it).base());
            }
        }
        
        // Add remaining quantity to order book
        if (order.filled_quantity < order.quantity) {
            order.status = (order.filled_quantity > 0) ? OrderStatus::PARTIALLY_FILLED : OrderStatus::PENDING;
            order.updated_time = Utils::getCurrentTime();
            book.sell_orders[order.price] = order;
        } else {
            order.status = OrderStatus::FILLED;
            order.updated_time = Utils::getCurrentTime();
            completed_orders_[order.id] = order;
        }
    }
}

void OrderMatchingEngine::executeTrade(Order& buy_order, Order& sell_order, Price price, Volume quantity) {
    // Update order quantities
    buy_order.filled_quantity += quantity;
    sell_order.filled_quantity += quantity;
    
    // Create trade record
    Trade trade(buy_order.id, sell_order.id, buy_order.symbol, price, quantity);
    
    // Store trade
    trade_history_[buy_order.symbol].push_back(trade);
    total_trades_executed_++;
    
    // Notify subscribers
    notifyTradeSubscribers(trade);
    
    Logger::info("Trade executed: " + buy_order.symbol + 
                " Price: " + std::to_string(price) + 
                " Quantity: " + std::to_string(quantity));
}

bool OrderMatchingEngine::validateOrder(const Order& order) const {
    // Basic validation
    if (order.symbol.empty()) {
        Logger::error("Order validation failed: empty symbol");
        return false;
    }
    
    if (order.quantity <= 0) {
        Logger::error("Order validation failed: invalid quantity");
        return false;
    }
    
    if (order.type == OrderType::LIMIT && order.price <= 0) {
        Logger::error("Order validation failed: invalid price for limit order");
        return false;
    }
    
    return true;
}

void OrderMatchingEngine::subscribeToTrades(std::function<void(const Trade&)> callback) {
    std::lock_guard<std::mutex> lock(subscribers_mutex_);
    trade_subscribers_.push_back(std::move(callback));
}

void OrderMatchingEngine::notifyTradeSubscribers(const Trade& trade) {
    std::lock_guard<std::mutex> lock(subscribers_mutex_);
    
    for (auto& callback : trade_subscribers_) {
        try {
            callback(trade);
        } catch (const std::exception& e) {
            Logger::error("Trade subscriber callback failed: " + std::string(e.what()));
        }
    }
}

} // namespace TradingEngine
