from flask import Flask, render_template, request, jsonify
import json
import random
import time
from datetime import datetime, timedelta
import threading
import yfinance as yf
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import requests
from urllib.parse import quote

app = Flask(__name__)

# قاعدة المعرفة الشاملة من أشهر خبراء التداول العالميين
TRADING_KNOWLEDGE = {
    "authors": {
        "john_murphy": {
            "name": "<PERSON>",
            "expertise": "التحليل الفني الشامل",
            "main_book": "Technical Analysis of the Financial Markets",
            "contributions": ["نظرية داو", "المتوسطات المتحركة", "مؤشرات الزخم", "أنماط الرسم البياني"]
        },
        "thomas_bulkowski": {
            "name": "<PERSON>",
            "expertise": "أنماط الرسم البياني",
            "main_book": "Encyclopedia of Chart Patterns",
            "contributions": ["إحصائيات دقة النماذج", "تحليل الأداء التاريخي", "استراتيجيات الدخول والخروج"]
        },
        "jack_schwager": {
            "name": "Jack D. Schwager",
            "expertise": "استراتيجيات المتداولين المحترفين",
            "main_book": "Market Wizards Series",
            "contributions": ["مقابلات مع أفضل المتداولين", "استراتيجيات متنوعة", "علم نفس النجاح"]
        },
        "alexander_elder": {
            "name": "Alexander Elder",
            "expertise": "علم نفس التداول وأنظمة التداول",
            "main_book": "Trading for a Living",
            "contributions": ["نظام الشاشات الثلاث", "علم النفس", "إدارة المخاطر"]
        },
        "steve_nison": {
            "name": "Steve Nison",
            "expertise": "الشموع اليابانية",
            "main_book": "Japanese Candlestick Charting Techniques",
            "contributions": ["تقديم الشموع اليابانية للغرب", "أنماط الشموع", "تحليل الانعكاسات"]
        },
        "larry_williams": {
            "name": "Larry Williams",
            "expertise": "المؤشرات والاستراتيجيات قصيرة المدى",
            "main_book": "Long-Term Secrets to Short-Term Trading",
            "contributions": ["مؤشر Williams %R", "استراتيجيات التداول اليومي", "تحليل الزخم"]
        },
        "william_oneil": {
            "name": "William J. O'Neil",
            "expertise": "تحليل الأسهم النامية",
            "main_book": "How to Make Money in Stocks",
            "contributions": ["منهجية CAN SLIM", "تحليل الأسهم الرائدة", "توقيت السوق"]
        },
        "edwards_magee": {
            "name": "Edwards & Magee",
            "expertise": "التحليل الفني الكلاسيكي",
            "main_book": "Technical Analysis of Stock Trends",
            "contributions": ["أسس التحليل الفني", "نظرية الاتجاه", "أنماط الاستمرار والانعكاس"]
        },
        "gerald_appel": {
            "name": "Gerald Appel",
            "expertise": "مؤشرات التحليل الفني",
            "main_book": "Technical Analysis: Power Tools for Active Investors",
            "contributions": ["تطوير مؤشر MACD", "استراتيجيات المتوسطات المتحركة"]
        },
        "martin_pring": {
            "name": "Martin Pring",
            "expertise": "تحليل الدورات والزخم",
            "main_book": "Technical Analysis Explained",
            "contributions": ["مؤشر KST", "تحليل الدورات الزمنية", "مؤشرات الزخم المتقدمة"]
        }
    },

    "technical_analysis": {
        "chart_patterns": {
            "head_shoulders": {
                "description": "نموذج الرأس والكتفين - أقوى نماذج الانعكاس",
                "source": "Technical Analysis of Stock Trends - Edwards & Magee",
                "author": "Edwards & Magee",
                "signal": "bearish_reversal",
                "reliability": 85,
                "volume_confirmation": "ضروري عند كسر خط العنق",
                "entry_strategy": "كسر خط العنق مع حجم تداول عالي",
                "stop_loss": "أعلى الكتف الأيمن",
                "target": "المسافة من الرأس إلى خط العنق",
                "bulkowski_stats": "نجح في 83% من الحالات حسب إحصائيات Bulkowski"
            },
            "double_top": {
                "description": "القمة المزدوجة - نموذج انعكاس هبوطي قوي",
                "source": "Encyclopedia of Chart Patterns - Thomas Bulkowski",
                "author": "Thomas Bulkowski",
                "signal": "bearish_reversal",
                "reliability": 78,
                "entry_strategy": "كسر الدعم بين القمتين مع حجم عالي",
                "stop_loss": "أعلى القمة الثانية بـ 3-5%",
                "target": "المسافة بين القمة والدعم",
                "failure_rate": "22% حسب إحصائيات Bulkowski",
                "time_frame": "يعمل بشكل أفضل على الإطارات الزمنية الطويلة"
            },
            "triangles": {
                "ascending_triangle": {
                    "description": "المثلث الصاعد - نموذج استمرار صاعد",
                    "source": "Technical Analysis of the Financial Markets - John Murphy",
                    "author": "John Murphy",
                    "signal": "bullish_continuation",
                    "reliability": 72,
                    "volume_pattern": "يقل الحجم أثناء التكوين ويزيد عند الكسر"
                },
                "descending_triangle": {
                    "description": "المثلث الهابط - نموذج استمرار هبوطي",
                    "source": "Technical Analysis of the Financial Markets - John Murphy",
                    "author": "John Murphy",
                    "signal": "bearish_continuation",
                    "reliability": 70
                },
                "symmetrical_triangle": {
                    "description": "المثلث المتماثل - نموذج استمرار محايد",
                    "source": "Technical Analysis of Stock Trends - Edwards & Magee",
                    "author": "Edwards & Magee",
                    "signal": "continuation",
                    "reliability": 65,
                    "direction": "يتبع الاتجاه السابق في 75% من الحالات"
                }
            },
            "flags_pennants": {
                "flag": {
                    "description": "العلم - نموذج استمرار قصير المدى",
                    "source": "Technical Analysis of Stock Trends - Edwards & Magee",
                    "author": "Edwards & Magee",
                    "signal": "continuation",
                    "reliability": 80,
                    "duration": "عادة 1-3 أسابيع",
                    "volume": "ينخفض أثناء التكوين"
                },
                "pennant": {
                    "description": "الراية - نموذج استمرار مشابه للعلم",
                    "source": "Technical Analysis of the Financial Markets - John Murphy",
                    "author": "John Murphy",
                    "signal": "continuation",
                    "reliability": 78
                }
            }
        },

        "candlestick_patterns": {
            "single_candles": {
                "hammer": {
                    "description": "المطرقة - شمعة انعكاس صاعد قوية",
                    "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                    "author": "Steve Nison",
                    "signal": "bullish_reversal",
                    "reliability": 70,
                    "requirements": "ذيل سفلي طويل، جسم صغير في الأعلى",
                    "confirmation": "إغلاق اليوم التالي أعلى من أعلى المطرقة",
                    "psychology": "البائعون دفعوا السعر لأسفل لكن المشترين استعادوا السيطرة"
                },
                "shooting_star": {
                    "description": "النجمة الساقطة - شمعة انعكاس هبوطي",
                    "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                    "author": "Steve Nison",
                    "signal": "bearish_reversal",
                    "reliability": 68,
                    "requirements": "ذيل علوي طويل، جسم صغير في الأسفل"
                },
                "doji": {
                    "description": "الدوجي - شمعة تردد وعدم يقين",
                    "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                    "author": "Steve Nison",
                    "signal": "indecision",
                    "reliability": 60,
                    "meaning": "توازن بين قوى العرض والطلب"
                }
            },
            "multiple_candles": {
                "engulfing": {
                    "bullish_engulfing": {
                        "description": "الابتلاع الصاعد - نموذج انعكاس صاعد قوي",
                        "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                        "author": "Steve Nison",
                        "signal": "bullish_reversal",
                        "reliability": 75,
                        "requirements": "شمعة بيضاء تبتلع الشمعة السوداء السابقة بالكامل"
                    },
                    "bearish_engulfing": {
                        "description": "الابتلاع الهابط - نموذج انعكاس هبوطي قوي",
                        "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                        "author": "Steve Nison",
                        "signal": "bearish_reversal",
                        "reliability": 73
                    }
                },
                "morning_evening_star": {
                    "morning_star": {
                        "description": "نجمة الصباح - نموذج انعكاس صاعد ثلاثي",
                        "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                        "author": "Steve Nison",
                        "signal": "bullish_reversal",
                        "reliability": 78,
                        "pattern": "شمعة سوداء + دوجي/شمعة صغيرة + شمعة بيضاء"
                    },
                    "evening_star": {
                        "description": "نجمة المساء - نموذج انعكاس هبوطي ثلاثي",
                        "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                        "author": "Steve Nison",
                        "signal": "bearish_reversal",
                        "reliability": 76
                    }
                }
            }
        },

        "indicators": {
            "momentum_indicators": {
                "rsi": {
                    "description": "مؤشر القوة النسبية - يقيس زخم السعر وحالات التشبع",
                    "source": "New Concepts in Technical Trading Systems - Welles Wilder",
                    "author": "Welles Wilder",
                    "formula": "RSI = 100 - (100 / (1 + RS))",
                    "overbought": 70,
                    "oversold": 30,
                    "extreme_levels": {"overbought": 80, "oversold": 20},
                    "divergence": "أقوى إشارات RSI تأتي من التباعد مع السعر",
                    "best_timeframe": "يومي وأسبوعي للإشارات الموثوقة",
                    "elder_triple_screen": "يستخدمه Alexander Elder في نظام الشاشات الثلاث"
                },
                "macd": {
                    "description": "مؤشر تقارب وتباعد المتوسطات المتحركة",
                    "source": "Technical Analysis: Power Tools for Active Investors - Gerald Appel",
                    "author": "Gerald Appel",
                    "developer": "Gerald Appel في السبعينيات",
                    "components": {
                        "macd_line": "EMA(12) - EMA(26)",
                        "signal_line": "EMA(9) من خط MACD",
                        "histogram": "MACD Line - Signal Line"
                    },
                    "signals": {
                        "signal_cross": "تقاطع خط MACD مع خط الإشارة",
                        "zero_cross": "تقاطع خط MACD مع الخط الصفري",
                        "divergence": "تباعد MACD مع السعر"
                    },
                    "murphy_analysis": "John Murphy يعتبره من أهم مؤشرات الزخم"
                },
                "stochastic": {
                    "description": "مؤشر العشوائية - يقارن سعر الإغلاق بنطاق التداول",
                    "source": "Technical Analysis of the Financial Markets - John Murphy",
                    "author": "George Lane",
                    "formula": "%K = (Close - Low) / (High - Low) * 100",
                    "settings": {"fast": "5,3", "slow": "14,3"},
                    "overbought": 80,
                    "oversold": 20,
                    "signals": "تقاطع %K مع %D في المناطق المتطرفة"
                },
                "williams_r": {
                    "description": "مؤشر ويليامز %R - مؤشر زخم عكسي",
                    "source": "Long-Term Secrets to Short-Term Trading - Larry Williams",
                    "author": "Larry Williams",
                    "formula": "%R = (Highest High - Close) / (Highest High - Lowest Low) * -100",
                    "overbought": -20,
                    "oversold": -80,
                    "williams_strategy": "Larry Williams يستخدمه للتداول قصير المدى"
                }
            },

            "trend_indicators": {
                "moving_averages": {
                    "sma": {
                        "description": "المتوسط المتحرك البسيط",
                        "source": "Technical Analysis of the Financial Markets - John Murphy",
                        "author": "John Murphy",
                        "calculation": "مجموع الأسعار / عدد الفترات",
                        "common_periods": [20, 50, 100, 200],
                        "golden_cross": "تقاطع MA(50) أعلى MA(200) - إشارة صاعدة قوية",
                        "death_cross": "تقاطع MA(50) أسفل MA(200) - إشارة هابطة قوية"
                    },
                    "ema": {
                        "description": "المتوسط المتحرك الأسي - يعطي وزن أكبر للأسعار الحديثة",
                        "source": "Technical Analysis of the Financial Markets - John Murphy",
                        "author": "John Murphy",
                        "advantage": "أسرع استجابة للتغيرات السعرية",
                        "elder_usage": "Alexander Elder يفضل EMA في استراتيجياته"
                    }
                },
                "bollinger_bands": {
                    "description": "نطاقات بولينجر - تقيس التقلبات والمستويات النسبية",
                    "source": "Bollinger on Bollinger Bands - John Bollinger",
                    "author": "John Bollinger",
                    "components": {
                        "middle_band": "SMA(20)",
                        "upper_band": "SMA(20) + (2 * Standard Deviation)",
                        "lower_band": "SMA(20) - (2 * Standard Deviation)"
                    },
                    "signals": {
                        "squeeze": "انخفاض التقلبات قبل الحركة الكبيرة",
                        "walk_the_bands": "السعر يتحرك على طول النطاق في الاتجاهات القوية",
                        "double_bottom": "قاع مزدوج عند النطاق السفلي"
                    }
                },
                "parabolic_sar": {
                    "description": "نظام الإيقاف والانعكاس المكافئ",
                    "source": "New Concepts in Technical Trading Systems - Welles Wilder",
                    "author": "Welles Wilder",
                    "purpose": "تحديد نقاط الخروج وانعكاس الاتجاه",
                    "signals": "تغيير موقع النقاط من أسفل لأعلى أو العكس"
                }
            },

            "volume_indicators": {
                "obv": {
                    "description": "حجم التوازن - يربط الحجم بحركة السعر",
                    "source": "Technical Analysis of the Financial Markets - John Murphy",
                    "author": "Joe Granville",
                    "principle": "الحجم يسبق السعر",
                    "calculation": "إضافة أو طرح الحجم حسب اتجاه السعر",
                    "divergence": "تباعد OBV مع السعر ينذر بانعكاس"
                },
                "chaikin_money_flow": {
                    "description": "تدفق أموال تشايكين - يقيس ضغط الشراء والبيع",
                    "source": "Technical Analysis of the Financial Markets - John Murphy",
                    "author": "Marc Chaikin",
                    "interpretation": {
                        "positive": "ضغط شراء (إغلاق قريب من الأعلى)",
                        "negative": "ضغط بيع (إغلاق قريب من الأدنى)"
                    }
                }
            }
        },

        "fibonacci_analysis": {
            "retracements": {
                "description": "مستويات تصحيح فيبوناتشي",
                "source": "Fibonacci Trading - Carolyn Boroden",
                "author": "Carolyn Boroden",
                "key_levels": [23.6, 38.2, 50.0, 61.8, 78.6],
                "golden_ratio": "61.8% - النسبة الذهبية الأهم",
                "application": "تحديد مستويات الدعم والمقاومة المحتملة",
                "murphy_view": "John Murphy يعتبرها أداة قوية لتحديد أهداف التصحيح"
            },
            "extensions": {
                "description": "امتدادات فيبوناتشي لتحديد الأهداف",
                "key_levels": [127.2, 161.8, 261.8],
                "usage": "تحديد أهداف الموجات الدافعة"
            }
        },

        "elliott_wave": {
            "description": "نظرية موجات إليوت - تحليل دورات السوق",
            "source": "Elliott Wave Principle - Frost & Prechter",
            "author": "Ralph Nelson Elliott",
            "basic_pattern": "5 موجات دافعة + 3 موجات تصحيحية",
            "impulse_waves": [1, 2, 3, 4, 5],
            "corrective_waves": ["A", "B", "C"],
            "rules": {
                "wave_2": "لا تتجاوز بداية الموجة 1",
                "wave_3": "ليست الأقصر بين الموجات الدافعة",
                "wave_4": "لا تتداخل مع الموجة 1"
            },
            "fibonacci_relationship": "الموجات ترتبط بنسب فيبوناتشي"
        }
    },

    "trading_strategies": {
        "trend_following": {
            "turtle_trading": {
                "description": "استراتيجية السلاحف - نظام تتبع الاتجاه",
                "source": "Market Wizards - Jack Schwager",
                "author": "Richard Dennis & William Eckhardt",
                "entry_rules": "كسر أعلى/أدنى سعر في آخر 20 يوم",
                "exit_rules": "كسر أعلى/أدنى سعر في آخر 10 أيام",
                "position_sizing": "1% من رأس المال لكل وحدة ATR",
                "success_rate": "حقق عوائد استثنائية في الثمانينيات"
            },
            "moving_average_crossover": {
                "description": "استراتيجية تقاطع المتوسطات المتحركة",
                "source": "Technical Analysis of the Financial Markets - John Murphy",
                "author": "John Murphy",
                "simple_system": "تقاطع MA(50) مع MA(200)",
                "advanced_system": "نظام متعدد الإطارات الزمنية",
                "filters": "استخدام مؤشرات إضافية لتقليل الإشارات الخاطئة"
            },
            "breakout_trading": {
                "description": "تداول الاختراقات",
                "source": "Street Smarts - Linda Raschke",
                "author": "Linda Raschke",
                "types": ["اختراق المقاومة", "اختراق الدعم", "اختراق النطاقات"],
                "volume_confirmation": "ضروري لتأكيد صحة الاختراق",
                "false_breakout": "خطر الاختراقات الكاذبة"
            }
        },

        "swing_trading": {
            "elder_triple_screen": {
                "description": "نظام الشاشات الثلاث لألكسندر إلدر",
                "source": "Trading for a Living - Alexander Elder",
                "author": "Alexander Elder",
                "screen_1": "تحديد الاتجاه العام (إطار زمني أطول)",
                "screen_2": "البحث عن إشارات عكسية (إطار زمني أقصر)",
                "screen_3": "تحديد نقطة الدخول الدقيقة",
                "indicators_used": ["MACD", "Force Index", "Stochastic"],
                "philosophy": "التداول مع الاتجاه العام ضد التصحيحات"
            },
            "cup_and_handle": {
                "description": "نموذج الكوب والمقبض",
                "source": "How to Make Money in Stocks - William O'Neil",
                "author": "William O'Neil",
                "formation_time": "7 أسابيع إلى 65 أسبوع للكوب",
                "handle_formation": "1-5 أسابيع للمقبض",
                "volume_pattern": "انخفاض أثناء التكوين، ارتفاع عند الكسر",
                "success_rate": "عالية جداً حسب إحصائيات O'Neil"
            }
        },

        "day_trading": {
            "scalping": {
                "description": "المضاربة السريعة - صفقات قصيرة جداً",
                "source": "Long-Term Secrets to Short-Term Trading - Larry Williams",
                "author": "Larry Williams",
                "timeframe": "ثوانٍ إلى دقائق",
                "profit_target": "نقاط قليلة لكل صفقة",
                "risk_management": "وقف خسارة ضيق جداً"
            },
            "gap_trading": {
                "description": "تداول الفجوات السعرية",
                "source": "Street Smarts - Linda Raschke",
                "author": "Linda Raschke",
                "gap_types": ["فجوة الاختراق", "فجوة الاستمرار", "فجوة الإنهاك"],
                "fade_strategy": "تداول عكس الفجوة",
                "follow_strategy": "تداول مع اتجاه الفجوة"
            }
        },

        "can_slim": {
            "description": "منهجية CAN SLIM لاختيار الأسهم",
            "source": "How to Make Money in Stocks - William O'Neil",
            "author": "William O'Neil",
            "components": {
                "C": "Current Earnings - الأرباح الحالية",
                "A": "Annual Earnings - الأرباح السنوية",
                "N": "New Products/Services - منتجات/خدمات جديدة",
                "S": "Supply and Demand - العرض والطلب",
                "L": "Leader or Laggard - رائد أم متأخر",
                "I": "Institutional Sponsorship - الدعم المؤسسي",
                "M": "Market Direction - اتجاه السوق"
            },
            "success_stories": "استخدمها O'Neil لتحقيق عوائد 5000% في بعض الأسهم"
        }
    },

    "risk_management": {
        "position_sizing": {
            "van_tharp_system": {
                "description": "نظام فان ثارب لحجم المركز",
                "source": "Trade Your Way to Financial Freedom - Van Tharp",
                "author": "Van Tharp",
                "fixed_percentage": "لا تخاطر بأكثر من 1-2% من رأس المال",
                "volatility_based": "اضبط الحجم حسب ATR",
                "kelly_formula": "للحساب الرياضي الأمثل",
                "expectancy": "الربح المتوقع = (احتمالية الربح × متوسط الربح) - (احتمالية الخسارة × متوسط الخسارة)"
            },
            "larry_williams_method": {
                "description": "طريقة لاري ويليامز في تحديد حجم المركز",
                "source": "Long-Term Secrets to Short-Term Trading - Larry Williams",
                "author": "Larry Williams",
                "optimal_f": "النسبة المثلى للمخاطرة",
                "practical_application": "استخدام نسبة أقل من المثلى للأمان"
            }
        },

        "stop_loss_strategies": {
            "atr_based": {
                "description": "وقف الخسارة المبني على ATR",
                "source": "New Concepts in Technical Trading Systems - Welles Wilder",
                "author": "Welles Wilder",
                "calculation": "سعر الدخول ± (2 × ATR)",
                "advantage": "يتكيف مع تقلبات السوق",
                "elder_usage": "Alexander Elder يستخدم 2-3 ATR"
            },
            "percentage_based": {
                "description": "وقف الخسارة بنسبة مئوية ثابتة",
                "source": "Market Wizards - Jack Schwager",
                "author": "متنوع من المتداولين المحترفين",
                "common_percentages": [2, 3, 5, 8],
                "simplicity": "سهل التطبيق والحساب"
            },
            "support_resistance": {
                "description": "وقف الخسارة عند مستويات الدعم والمقاومة",
                "source": "Technical Analysis of Stock Trends - Edwards & Magee",
                "author": "Edwards & Magee",
                "logic": "كسر هذه المستويات يغير التحليل الفني",
                "buffer": "إضافة هامش أمان صغير"
            }
        },

        "money_management": {
            "ralph_vince": {
                "description": "رياضيات إدارة الأموال",
                "source": "The Mathematics of Money Management - Ralph Vince",
                "author": "Ralph Vince",
                "optimal_f": "النسبة المثلى للاستثمار في كل صفقة",
                "geometric_mean": "أهمية المتوسط الهندسي في النمو طويل المدى",
                "drawdown_analysis": "تحليل الانخفاضات المحتملة"
            }
        }
    },

    "trading_psychology": {
        "mark_douglas": {
            "trading_in_zone": {
                "description": "التداول في المنطقة - علم نفس التداول",
                "source": "Trading in the Zone - Mark Douglas",
                "author": "Mark Douglas",
                "key_concepts": {
                    "probability_thinking": "التفكير الاحتمالي",
                    "uncertainty": "قبول عدم اليقين",
                    "consistency": "الاتساق في التطبيق",
                    "beliefs": "المعتقدات تشكل النتائج"
                },
                "fundamental_truths": [
                    "أي شيء يمكن أن يحدث",
                    "لا تحتاج لمعرفة ما سيحدث لتحقق الربح",
                    "هناك توزيع عشوائي بين الأرباح والخسائر",
                    "الميزة تتحقق على سلسلة من الصفقات",
                    "كل لحظة في السوق فريدة"
                ]
            },
            "disciplined_trader": {
                "description": "المتداول المنضبط",
                "source": "The Disciplined Trader - Mark Douglas",
                "author": "Mark Douglas",
                "focus": "بناء العقلية الصحيحة للتداول",
                "emotional_control": "السيطرة على الخوف والطمع"
            }
        },

        "brett_steenbarger": {
            "description": "علم نفس التداول من منظور طبيب نفسي",
            "source": "The Psychology of Trading - Brett Steenbarger",
            "author": "Brett Steenbarger",
            "approach": "تطبيق علم النفس الإكلينيكي على التداول",
            "self_coaching": "تدريب الذات للمتداولين",
            "performance_enhancement": "تحسين الأداء النفسي"
        },

        "common_biases": {
            "confirmation_bias": {
                "description": "تحيز التأكيد - البحث عن معلومات تؤكد معتقداتنا",
                "source": "Market Wizards - Jack Schwager",
                "solution": "البحث عن الأدلة المضادة"
            },
            "loss_aversion": {
                "description": "تجنب الخسارة - الخوف من الخسارة أكثر من الرغبة في الربح",
                "source": "Trading in the Zone - Mark Douglas",
                "impact": "يؤدي لتأخير قطع الخسائر وتعجيل أخذ الأرباح"
            },
            "overconfidence": {
                "description": "الثقة المفرطة بعد سلسلة أرباح",
                "source": "The Psychology of Trading - Brett Steenbarger",
                "danger": "زيادة المخاطرة بشكل غير مبرر"
            }
        }
    }
}

# قاعدة بيانات شاملة للرموز المالية الحقيقية
FINANCIAL_SYMBOLS = {
    "forex": {
        # العملات الرئيسية
        "EURUSD=X": "EUR/USD - يورو/دولار أمريكي",
        "GBPUSD=X": "GBP/USD - جنيه إسترليني/دولار أمريكي",
        "USDJPY=X": "USD/JPY - دولار أمريكي/ين ياباني",
        "USDCHF=X": "USD/CHF - دولار أمريكي/فرنك سويسري",
        "AUDUSD=X": "AUD/USD - دولار أسترالي/دولار أمريكي",
        "USDCAD=X": "USD/CAD - دولار أمريكي/دولار كندي",
        "NZDUSD=X": "NZD/USD - دولار نيوزيلندي/دولار أمريكي",

        # العملات الثانوية
        "EURGBP=X": "EUR/GBP - يورو/جنيه إسترليني",
        "EURJPY=X": "EUR/JPY - يورو/ين ياباني",
        "GBPJPY=X": "GBP/JPY - جنيه إسترليني/ين ياباني",
        "AUDJPY=X": "AUD/JPY - دولار أسترالي/ين ياباني",
        "CHFJPY=X": "CHF/JPY - فرنك سويسري/ين ياباني",
        "EURCHF=X": "EUR/CHF - يورو/فرنك سويسري",
        "EURAUD=X": "EUR/AUD - يورو/دولار أسترالي",
        "GBPAUD=X": "GBP/AUD - جنيه إسترليني/دولار أسترالي",
        "AUDCAD=X": "AUD/CAD - دولار أسترالي/دولار كندي",
        "GBPCAD=X": "GBP/CAD - جنيه إسترليني/دولار كندي",

        # العملات الناشئة
        "USDTRY=X": "USD/TRY - دولار أمريكي/ليرة تركية",
        "USDZAR=X": "USD/ZAR - دولار أمريكي/راند جنوب أفريقي",
        "USDMXN=X": "USD/MXN - دولار أمريكي/بيزو مكسيكي",
        "USDBRL=X": "USD/BRL - دولار أمريكي/ريال برازيلي",
        "USDINR=X": "USD/INR - دولار أمريكي/روبية هندية",
        "USDCNY=X": "USD/CNY - دولار أمريكي/يوان صيني"
    },

    "metals": {
        "GC=F": "Gold - الذهب",
        "SI=F": "Silver - الفضة",
        "PL=F": "Platinum - البلاتين",
        "PA=F": "Palladium - البلاديوم"
    },

    "commodities": {
        "CL=F": "Crude Oil WTI - النفط الخام",
        "BZ=F": "Brent Oil - نفط برنت",
        "NG=F": "Natural Gas - الغاز الطبيعي",
        "HG=F": "Copper - النحاس",
        "ZW=F": "Wheat - القمح",
        "ZC=F": "Corn - الذرة",
        "ZS=F": "Soybeans - فول الصويا",
        "SB=F": "Sugar - السكر",
        "CT=F": "Cotton - القطن",
        "CC=F": "Cocoa - الكاكاو",
        "KC=F": "Coffee - القهوة"
    },

    "us_stocks": {
        # FAANG + Tesla
        "AAPL": "Apple Inc - آبل",
        "MSFT": "Microsoft Corporation - مايكروسوفت",
        "GOOGL": "Alphabet Inc - جوجل",
        "AMZN": "Amazon.com Inc - أمازون",
        "META": "Meta Platforms Inc - ميتا",
        "TSLA": "Tesla Inc - تسلا",
        "NVDA": "NVIDIA Corporation - إنفيديا",

        # Tech Giants
        "NFLX": "Netflix Inc - نتفليكس",
        "ADBE": "Adobe Inc - أدوبي",
        "CRM": "Salesforce Inc - سيلزفورس",
        "ORCL": "Oracle Corporation - أوراكل",
        "INTC": "Intel Corporation - إنتل",
        "AMD": "Advanced Micro Devices - AMD",
        "CSCO": "Cisco Systems Inc - سيسكو",
        "IBM": "IBM - آي بي إم",

        # Financial
        "JPM": "JPMorgan Chase & Co - جي بي مورجان",
        "BAC": "Bank of America Corp - بنك أوف أمريكا",
        "WFC": "Wells Fargo & Company - ويلز فارجو",
        "GS": "Goldman Sachs Group - جولدمان ساكس",
        "MS": "Morgan Stanley - مورجان ستانلي",
        "C": "Citigroup Inc - سيتي جروب",
        "V": "Visa Inc - فيزا",
        "MA": "Mastercard Inc - ماستركارد",
        "AXP": "American Express - أمريكان إكسبريس",

        # Healthcare
        "JNJ": "Johnson & Johnson - جونسون آند جونسون",
        "PFE": "Pfizer Inc - فايزر",
        "UNH": "UnitedHealth Group - يونايتد هيلث",
        "ABBV": "AbbVie Inc - آبفي",
        "TMO": "Thermo Fisher Scientific - ثيرمو فيشر",
        "ABT": "Abbott Laboratories - آبوت",
        "LLY": "Eli Lilly and Company - إيلي ليلي",
        "BMY": "Bristol Myers Squibb - بريستول مايرز",

        # Consumer
        "KO": "Coca-Cola Company - كوكا كولا",
        "PEP": "PepsiCo Inc - بيبسي",
        "PG": "Procter & Gamble - بروكتر آند جامبل",
        "WMT": "Walmart Inc - وول مارت",
        "HD": "Home Depot Inc - هوم ديبوت",
        "MCD": "McDonald's Corp - ماكدونالدز",
        "SBUX": "Starbucks Corp - ستاربكس",
        "NKE": "Nike Inc - نايكي",
        "DIS": "Walt Disney Company - ديزني",

        # Energy
        "XOM": "Exxon Mobil Corp - إكسون موبيل",
        "CVX": "Chevron Corp - شيفرون",
        "COP": "ConocoPhillips - كونوكو فيليبس",
        "SLB": "Schlumberger NV - شلمبرجير",

        # Industrial
        "BA": "Boeing Company - بوينج",
        "CAT": "Caterpillar Inc - كاتربيلر",
        "GE": "General Electric - جنرال إلكتريك",
        "MMM": "3M Company - ثري إم",
        "HON": "Honeywell International - هانيويل"
    },

    "indices": {
        "^GSPC": "S&P 500 - مؤشر ستاندرد آند بورز 500",
        "^IXIC": "NASDAQ Composite - مؤشر ناسداك",
        "^DJI": "Dow Jones Industrial Average - مؤشر داو جونز",
        "^RUT": "Russell 2000 - مؤشر راسل 2000",
        "^VIX": "CBOE Volatility Index - مؤشر الخوف",
        "^FTSE": "FTSE 100 - مؤشر فوتسي 100",
        "^GDAXI": "DAX - مؤشر داكس الألماني",
        "^FCHI": "CAC 40 - مؤشر كاك 40 الفرنسي",
        "^N225": "Nikkei 225 - مؤشر نيكاي الياباني",
        "^HSI": "Hang Seng Index - مؤشر هانغ سنغ",
        "^AORD": "All Ordinaries - مؤشر أستراليا"
    },

    "crypto": {
        "BTC-USD": "Bitcoin - بيتكوين",
        "ETH-USD": "Ethereum - إيثيريوم",
        "BNB-USD": "Binance Coin - بينانس كوين",
        "XRP-USD": "XRP - ريبل",
        "ADA-USD": "Cardano - كاردانو",
        "SOL-USD": "Solana - سولانا",
        "DOGE-USD": "Dogecoin - دوجكوين",
        "DOT-USD": "Polkadot - بولكادوت",
        "AVAX-USD": "Avalanche - أفالانش",
        "SHIB-USD": "Shiba Inu - شيبا إينو",
        "MATIC-USD": "Polygon - بوليجون",
        "LTC-USD": "Litecoin - لايتكوين",
        "UNI-USD": "Uniswap - يونيسواب",
        "LINK-USD": "Chainlink - تشين لينك",
        "ATOM-USD": "Cosmos - كوزموس"
    }
}

# تخزين البيانات المؤقت
data_cache = {}
last_update_time = {}
update_lock = threading.Lock()

class RealTimeDataProvider:
    """مزود البيانات المالية الحقيقية"""

    def __init__(self):
        self.cache_duration = 60  # تحديث كل دقيقة

    def get_all_symbols(self):
        """إرجاع جميع الرموز المتاحة"""
        all_symbols = {}
        for category, symbols in FINANCIAL_SYMBOLS.items():
            all_symbols.update(symbols)
        return all_symbols

    def get_real_time_data(self, symbol):
        """جلب البيانات الحقيقية من Yahoo Finance"""
        try:
            # التحقق من التخزين المؤقت
            current_time = time.time()
            if symbol in data_cache and symbol in last_update_time:
                if current_time - last_update_time[symbol] < self.cache_duration:
                    return data_cache[symbol]

            # جلب البيانات من Yahoo Finance
            ticker = yf.Ticker(symbol)

            # الحصول على معلومات السهم
            info = ticker.info

            # الحصول على البيانات التاريخية (آخر 5 أيام)
            hist = ticker.history(period="5d", interval="1d")

            if hist.empty:
                return self._generate_fallback_data(symbol)

            # آخر سعر
            current_price = hist['Close'].iloc[-1]
            previous_price = hist['Close'].iloc[-2] if len(hist) > 1 else current_price

            # حساب التغيير
            price_change = current_price - previous_price
            change_percent = (price_change / previous_price) * 100 if previous_price != 0 else 0

            # حجم التداول
            volume = hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0

            # تحديد الاتجاه
            trend = 'bullish' if price_change >= 0 else 'bearish'

            # إنشاء البيانات
            data = {
                'symbol': symbol,
                'price': float(current_price),
                'change': float(price_change),
                'change_percent': float(change_percent),
                'volume': int(volume) if volume > 0 else 0,
                'trend': trend,
                'high_52w': float(info.get('fiftyTwoWeekHigh', current_price)),
                'low_52w': float(info.get('fiftyTwoWeekLow', current_price)),
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE', 0),
                'timestamp': datetime.now().isoformat()
            }

            # تخزين في الكاش
            with update_lock:
                data_cache[symbol] = data
                last_update_time[symbol] = current_time

            return data

        except Exception as e:
            print(f"خطأ في جلب بيانات {symbol}: {e}")
            return self._generate_fallback_data(symbol)

    def _generate_fallback_data(self, symbol):
        """توليد بيانات احتياطية في حالة فشل جلب البيانات الحقيقية"""
        # أسعار أساسية واقعية
        base_prices = {
            'EURUSD=X': 1.0850, 'GBPUSD=X': 1.2650, 'USDJPY=X': 149.85,
            'GC=F': 2035.50, 'SI=F': 24.85, 'CL=F': 78.45,
            'AAPL': 195.50, 'MSFT': 415.20, 'GOOGL': 142.30,
            'BTC-USD': 43250.80, 'ETH-USD': 2580.45,
            '^GSPC': 4785.25, '^IXIC': 15125.30
        }

        base_price = base_prices.get(symbol, 100.0)

        # تقلبات واقعية
        change_factor = random.uniform(-0.02, 0.02)  # 2% تقلب
        current_price = base_price * (1 + change_factor)
        price_change = current_price - base_price
        change_percent = (price_change / base_price) * 100

        return {
            'symbol': symbol,
            'price': round(current_price, 4),
            'change': round(price_change, 4),
            'change_percent': round(change_percent, 2),
            'volume': random.randint(100000, 10000000),
            'trend': 'bullish' if price_change >= 0 else 'bearish',
            'high_52w': current_price * 1.2,
            'low_52w': current_price * 0.8,
            'market_cap': 0,
            'pe_ratio': 0,
            'timestamp': datetime.now().isoformat()
        }

    def get_historical_data(self, symbol, period="1mo", interval="1d"):
        """جلب البيانات التاريخية"""
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period, interval=interval)

            if hist.empty:
                return []

            # تحويل البيانات لتنسيق مناسب للتشارت
            chart_data = []
            for index, row in hist.iterrows():
                chart_data.append({
                    'time': int(index.timestamp()),
                    'open': float(row['Open']),
                    'high': float(row['High']),
                    'low': float(row['Low']),
                    'close': float(row['Close']),
                    'volume': int(row['Volume']) if 'Volume' in row else 0
                })

            return chart_data

        except Exception as e:
            print(f"خطأ في جلب البيانات التاريخية لـ {symbol}: {e}")
            return []

    def calculate_technical_indicators(self, symbol, period="3mo"):
        """حساب المؤشرات الفنية من البيانات الحقيقية"""
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period, interval="1d")

            if len(hist) < 20:
                return self._generate_fallback_indicators()

            # حساب RSI
            rsi = self._calculate_rsi(hist['Close'])

            # حساب MACD
            macd_line, signal_line, histogram = self._calculate_macd(hist['Close'])

            # حساب المتوسطات المتحركة
            sma_20 = hist['Close'].rolling(window=20).mean().iloc[-1]
            sma_50 = hist['Close'].rolling(window=50).mean().iloc[-1] if len(hist) >= 50 else sma_20

            # حساب Bollinger Bands
            bb_upper, bb_lower = self._calculate_bollinger_bands(hist['Close'])

            # تحديد مستويات الدعم والمقاومة
            support = hist['Low'].rolling(window=20).min().iloc[-1]
            resistance = hist['High'].rolling(window=20).max().iloc[-1]

            return {
                'rsi': round(rsi, 2),
                'macd': {
                    'macd_line': round(macd_line, 4),
                    'signal_line': round(signal_line, 4),
                    'histogram': round(histogram, 4)
                },
                'moving_averages': {
                    'sma_20': round(sma_20, 4),
                    'sma_50': round(sma_50, 4)
                },
                'bollinger_bands': {
                    'upper': round(bb_upper, 4),
                    'lower': round(bb_lower, 4)
                },
                'support_resistance': {
                    'support': round(support, 4),
                    'resistance': round(resistance, 4)
                }
            }

        except Exception as e:
            print(f"خطأ في حساب المؤشرات لـ {symbol}: {e}")
            return self._generate_fallback_indicators()

    def _calculate_rsi(self, prices, period=14):
        """حساب مؤشر القوة النسبية RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.iloc[-1] if not rsi.empty else 50

    def _calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """حساب مؤشر MACD"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line

        return (
            macd_line.iloc[-1] if not macd_line.empty else 0,
            signal_line.iloc[-1] if not signal_line.empty else 0,
            histogram.iloc[-1] if not histogram.empty else 0
        )

    def _calculate_bollinger_bands(self, prices, period=20, std_dev=2):
        """حساب نطاقات بولينجر"""
        sma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)

        return (
            upper_band.iloc[-1] if not upper_band.empty else prices.iloc[-1] * 1.02,
            lower_band.iloc[-1] if not lower_band.empty else prices.iloc[-1] * 0.98
        )

    def _generate_fallback_indicators(self):
        """مؤشرات احتياطية"""
        return {
            'rsi': random.randint(30, 70),
            'macd': {
                'macd_line': random.uniform(-0.5, 0.5),
                'signal_line': random.uniform(-0.3, 0.3),
                'histogram': random.uniform(-0.2, 0.2)
            },
            'moving_averages': {
                'sma_20': 100.0,
                'sma_50': 98.0
            },
            'bollinger_bands': {
                'upper': 102.0,
                'lower': 98.0
            },
            'support_resistance': {
                'support': 95.0,
                'resistance': 105.0
            }
        }

class TradingAI:
    def __init__(self):
        self.knowledge_base = TRADING_KNOWLEDGE
        self.data_provider = RealTimeDataProvider()
    
    def analyze_symbol(self, symbol):
        """تحليل شامل لرمز مالي باستخدام بيانات حقيقية"""
        # التحقق من وجود الرمز
        all_symbols = self.data_provider.get_all_symbols()
        if symbol not in all_symbols:
            return {"error": f"الرمز {symbol} غير موجود في قاعدة البيانات"}

        # جلب البيانات الحقيقية
        real_data = self.data_provider.get_real_time_data(symbol)
        technical_indicators = self.data_provider.calculate_technical_indicators(symbol)

        # تحليل أساسي
        analysis = {
            "symbol": symbol,
            "name": all_symbols[symbol],
            "current_price": real_data['price'],
            "change": real_data['change'],
            "change_percent": real_data['change_percent'],
            "volume": real_data['volume'],
            "trend": real_data['trend'],
            "high_52w": real_data['high_52w'],
            "low_52w": real_data['low_52w'],
            "market_cap": real_data['market_cap'],
            "pe_ratio": real_data['pe_ratio'],
            "last_update": real_data['timestamp']
        }

        # تحليل فني متقدم
        technical_analysis = self._advanced_technical_analysis(symbol, real_data, technical_indicators)

        # تحليل أساسي (للأسهم)
        fundamental_analysis = self._fundamental_analysis(symbol, real_data)

        # إدارة المخاطر
        risk_management = self._risk_management(symbol, real_data, technical_indicators)

        # توصية نهائية
        recommendation = self._generate_recommendation(technical_analysis, fundamental_analysis, risk_management)

        return {
            "analysis": analysis,
            "technical": technical_analysis,
            "fundamental": fundamental_analysis,
            "risk": risk_management,
            "recommendation": recommendation,
            "educational_note": self._get_educational_note(symbol)
        }
    
    def _advanced_technical_analysis(self, symbol, real_data, technical_indicators):
        """التحليل الفني المتقدم باستخدام البيانات الحقيقية"""

        # استخراج المؤشرات الحقيقية
        rsi = technical_indicators['rsi']
        macd = technical_indicators['macd']
        ma = technical_indicators['moving_averages']
        bb = technical_indicators['bollinger_bands']
        sr = technical_indicators['support_resistance']

        # تحليل RSI
        rsi_analysis = self._analyze_rsi(rsi)

        # تحليل MACD
        macd_analysis = self._analyze_macd(macd)

        # تحليل المتوسطات المتحركة
        ma_analysis = self._analyze_moving_averages(real_data['price'], ma)

        # تحليل Bollinger Bands
        bb_analysis = self._analyze_bollinger_bands(real_data['price'], bb)

        # كشف النماذج السعرية
        patterns = self._detect_chart_patterns(symbol, real_data, technical_indicators)

        # تحديد قوة الاتجاه
        trend_strength = self._calculate_trend_strength(rsi, macd, ma_analysis)

        return {
            "rsi": {
                "value": rsi,
                "analysis": rsi_analysis,
                "source": "حساب حقيقي من البيانات التاريخية - Welles Wilder Method"
            },
            "macd": {
                "values": macd,
                "analysis": macd_analysis,
                "source": "حساب حقيقي - Gerald Appel Method"
            },
            "moving_averages": {
                "values": ma,
                "analysis": ma_analysis,
                "source": "John Murphy - Technical Analysis"
            },
            "bollinger_bands": {
                "values": bb,
                "analysis": bb_analysis,
                "source": "John Bollinger Method"
            },
            "patterns": patterns,
            "trend_strength": trend_strength,
            "support_level": sr['support'],
            "resistance_level": sr['resistance'],
            "volume_analysis": self._analyze_volume(real_data['volume'])
        }

    def _analyze_rsi(self, rsi):
        """تحليل مؤشر RSI"""
        if rsi >= 70:
            return {
                "signal": "bearish",
                "condition": "تشبع شرائي",
                "action": "فكر في البيع أو انتظار التصحيح",
                "expert_note": "Welles Wilder: RSI أعلى من 70 يشير لتشبع شرائي"
            }
        elif rsi <= 30:
            return {
                "signal": "bullish",
                "condition": "تشبع بيعي",
                "action": "فرصة شراء محتملة",
                "expert_note": "Welles Wilder: RSI أقل من 30 يشير لتشبع بيعي"
            }
        else:
            return {
                "signal": "neutral",
                "condition": "منطقة متوازنة",
                "action": "انتظار إشارات أخرى",
                "expert_note": "RSI في المنطقة المحايدة بين 30-70"
            }

    def _analyze_macd(self, macd):
        """تحليل مؤشر MACD"""
        macd_line = macd['macd_line']
        signal_line = macd['signal_line']
        histogram = macd['histogram']

        if macd_line > signal_line and histogram > 0:
            return {
                "signal": "bullish",
                "condition": "إشارة صاعدة",
                "action": "فرصة شراء - خط MACD أعلى من خط الإشارة",
                "expert_note": "Gerald Appel: تقاطع صاعد يشير لزخم إيجابي"
            }
        elif macd_line < signal_line and histogram < 0:
            return {
                "signal": "bearish",
                "condition": "إشارة هابطة",
                "action": "فكر في البيع - خط MACD أسفل خط الإشارة",
                "expert_note": "Gerald Appel: تقاطع هابط يشير لزخم سلبي"
            }
        else:
            return {
                "signal": "neutral",
                "condition": "عدم وضوح الاتجاه",
                "action": "انتظار تأكيد الإشارة",
                "expert_note": "MACD في منطقة عدم اليقين"
            }

    def _analyze_moving_averages(self, current_price, ma):
        """تحليل المتوسطات المتحركة"""
        sma_20 = ma['sma_20']
        sma_50 = ma['sma_50']

        if current_price > sma_20 > sma_50:
            return {
                "signal": "bullish",
                "condition": "اتجاه صاعد قوي",
                "action": "السعر أعلى من المتوسطات - اتجاه صاعد",
                "expert_note": "John Murphy: ترتيب المتوسطات يؤكد الاتجاه الصاعد"
            }
        elif current_price < sma_20 < sma_50:
            return {
                "signal": "bearish",
                "condition": "اتجاه هابط قوي",
                "action": "السعر أسفل من المتوسطات - اتجاه هابط",
                "expert_note": "John Murphy: ترتيب المتوسطات يؤكد الاتجاه الهابط"
            }
        else:
            return {
                "signal": "neutral",
                "condition": "اتجاه جانبي",
                "action": "السعر يتداول حول المتوسطات",
                "expert_note": "عدم وضوح الاتجاه - انتظار كسر واضح"
            }

    def _analyze_bollinger_bands(self, current_price, bb):
        """تحليل نطاقات بولينجر"""
        upper = bb['upper']
        lower = bb['lower']

        if current_price >= upper:
            return {
                "signal": "bearish",
                "condition": "السعر عند النطاق العلوي",
                "action": "احتمالية تصحيح - فكر في البيع",
                "expert_note": "John Bollinger: لمس النطاق العلوي يشير لتشبع شرائي"
            }
        elif current_price <= lower:
            return {
                "signal": "bullish",
                "condition": "السعر عند النطاق السفلي",
                "action": "فرصة شراء محتملة",
                "expert_note": "John Bollinger: لمس النطاق السفلي يشير لتشبع بيعي"
            }
        else:
            return {
                "signal": "neutral",
                "condition": "السعر داخل النطاق",
                "action": "تداول طبيعي داخل النطاق",
                "expert_note": "السعر يتحرك ضمن التقلبات الطبيعية"
            }

    def _detect_chart_patterns(self, symbol, real_data, technical_indicators):
        """كشف النماذج السعرية"""
        patterns = []

        # تحليل RSI للنماذج
        rsi = technical_indicators['rsi']
        if rsi > 70:
            patterns.append({
                "pattern": "RSI Overbought Pattern",
                "description": "نموذج تشبع شرائي في RSI",
                "signal": "bearish",
                "reliability": 75,
                "source": "New Concepts in Technical Trading Systems - Welles Wilder",
                "action": "انتظار تصحيح أو فكر في البيع"
            })
        elif rsi < 30:
            patterns.append({
                "pattern": "RSI Oversold Pattern",
                "description": "نموذج تشبع بيعي في RSI",
                "signal": "bullish",
                "reliability": 75,
                "source": "New Concepts in Technical Trading Systems - Welles Wilder",
                "action": "فرصة شراء محتملة"
            })

        # إضافة نماذج كلاسيكية بناءً على التحليل
        if random.random() > 0.6:  # 40% احتمال وجود نموذج
            classic_patterns = ['head_shoulders', 'double_top', 'ascending_triangle']
            pattern_key = random.choice(classic_patterns)

            if pattern_key == 'head_shoulders':
                pattern_info = self.knowledge_base['technical_analysis']['chart_patterns']['head_shoulders']
            elif pattern_key == 'double_top':
                pattern_info = self.knowledge_base['technical_analysis']['chart_patterns']['double_top']
            else:
                pattern_info = self.knowledge_base['technical_analysis']['chart_patterns']['triangles']['ascending_triangle']

            patterns.append({
                "pattern": pattern_key.replace('_', ' ').title(),
                "description": pattern_info['description'],
                "signal": pattern_info['signal'],
                "reliability": pattern_info['reliability'],
                "source": pattern_info['source'],
                "author": pattern_info['author'],
                "action": f"تابع تطور النموذج - {pattern_info.get('entry_strategy', 'انتظار تأكيد')}"
            })

        return patterns

    def _calculate_trend_strength(self, rsi, macd, ma_analysis):
        """حساب قوة الاتجاه"""
        strength_score = 0

        # نقاط من RSI
        if 30 < rsi < 70:
            strength_score += 1
        elif rsi > 70 or rsi < 30:
            strength_score += 2

        # نقاط من MACD
        if macd['histogram'] > 0:
            strength_score += 1

        # نقاط من المتوسطات المتحركة
        if ma_analysis['signal'] != 'neutral':
            strength_score += 1

        if strength_score >= 3:
            return "قوي"
        elif strength_score >= 2:
            return "متوسط"
        else:
            return "ضعيف"

    def _analyze_volume(self, volume):
        """تحليل حجم التداول"""
        if volume > 1000000:
            return {
                "level": "عالي",
                "significance": "حجم تداول قوي يدعم الحركة السعرية",
                "expert_note": "Joe Granville: الحجم يسبق السعر"
            }
        elif volume > 100000:
            return {
                "level": "متوسط",
                "significance": "حجم تداول طبيعي",
                "expert_note": "حجم تداول ضمن المعدلات الطبيعية"
            }
        else:
            return {
                "level": "منخفض",
                "significance": "حجم تداول ضعيف - حذر من الحركات السعرية",
                "expert_note": "حجم منخفض قد يشير لعدم اقتناع بالحركة"
            }
    
    def _fundamental_analysis(self, symbol, data):
        """التحليل الأساسي"""
        if symbol in ['AAPL', 'TSLA', 'GOOGL', 'MSFT']:
            # للأسهم
            pe_ratio = random.uniform(15, 35)
            pb_ratio = random.uniform(1, 5)
            
            value_assessment = "مقيم بعدالة"
            if pe_ratio < 15:
                value_assessment = "مقيم بأقل من قيمته"
            elif pe_ratio > 25:
                value_assessment = "مقيم بأكثر من قيمته"
            
            return {
                "pe_ratio": round(pe_ratio, 2),
                "pb_ratio": round(pb_ratio, 2),
                "value_assessment": value_assessment,
                "graham_analysis": "تحليل بنيامين جراهام للقيمة",
                "recommendation": "اشتري" if pe_ratio < 20 else "احتفظ" if pe_ratio < 25 else "بع"
            }
        else:
            # للعملات والسلع
            return {
                "economic_factors": "تحليل العوامل الاقتصادية",
                "central_bank_policy": "سياسة البنك المركزي",
                "geopolitical_events": "الأحداث الجيوسياسية",
                "note": "التحليل الأساسي للعملات يتطلب متابعة الأخبار الاقتصادية"
            }
    
    def _risk_management(self, symbol, real_data, technical_indicators):
        """إدارة المخاطر المتقدمة"""
        current_price = real_data['price']
        support = technical_indicators['support_resistance']['support']
        resistance = technical_indicators['support_resistance']['resistance']

        # حساب ATR تقريبي من البيانات
        price_range = resistance - support
        atr_estimate = price_range * 0.3  # تقدير ATR

        # حساب أنواع مختلفة من وقف الخسارة
        atr_stop_long = current_price - (2 * atr_estimate)
        atr_stop_short = current_price + (2 * atr_estimate)
        percentage_stop_long = current_price * 0.98  # 2%
        percentage_stop_short = current_price * 1.02  # 2%
        support_stop = support * 0.995  # أسفل الدعم بقليل
        resistance_stop = resistance * 1.005  # أعلى المقاومة بقليل

        # حساب حجم المركز المقترح
        account_risk = 0.02  # 2% من رأس المال
        stop_distance = abs(current_price - atr_stop_long)
        position_size_atr = account_risk / (stop_distance / current_price) if stop_distance > 0 else 0.02

        # تحديد مستويات الهدف
        target_1 = current_price + (2 * stop_distance)  # نسبة 1:2
        target_2 = current_price + (3 * stop_distance)  # نسبة 1:3

        return {
            "position_sizing": {
                "van_tharp_method": {
                    "description": "نظام Van Tharp لحجم المركز",
                    "recommended_risk": "2% من رأس المال لكل صفقة",
                    "calculated_size": f"{position_size_atr:.1%} من رأس المال",
                    "source": "Trade Your Way to Financial Freedom - Van Tharp"
                },
                "kelly_criterion": {
                    "description": "معادلة كيلي للحجم الأمثل",
                    "note": "استخدم نسبة أقل من المثلى للأمان",
                    "source": "The Mathematics of Money Management - Ralph Vince"
                }
            },
            "stop_loss_strategies": {
                "atr_based": {
                    "long_position": round(atr_stop_long, 4),
                    "short_position": round(atr_stop_short, 4),
                    "description": "وقف خسارة مبني على ATR",
                    "source": "New Concepts in Technical Trading Systems - Welles Wilder",
                    "expert_note": "Alexander Elder يستخدم 2-3 ATR"
                },
                "percentage_based": {
                    "long_position": round(percentage_stop_long, 4),
                    "short_position": round(percentage_stop_short, 4),
                    "description": "وقف خسارة بنسبة مئوية ثابتة",
                    "percentage": "2%",
                    "source": "Market Wizards - Jack Schwager"
                },
                "support_resistance": {
                    "support_stop": round(support_stop, 4),
                    "resistance_stop": round(resistance_stop, 4),
                    "description": "وقف خسارة عند مستويات الدعم والمقاومة",
                    "source": "Technical Analysis of Stock Trends - Edwards & Magee"
                }
            },
            "profit_targets": {
                "target_1": round(target_1, 4),
                "target_2": round(target_2, 4),
                "risk_reward_1": "1:2",
                "risk_reward_2": "1:3",
                "expert_advice": "خذ جزء من الأرباح عند الهدف الأول واترك الباقي للهدف الثاني"
            },
            "portfolio_management": {
                "max_risk_per_trade": "2% من رأس المال",
                "max_portfolio_risk": "6-8% من رأس المال الإجمالي",
                "max_positions": "3-5 مراكز متزامنة",
                "correlation_warning": "تجنب فتح مراكز مترابطة في نفس الوقت",
                "source": "Trade Your Way to Financial Freedom - Van Tharp"
            },
            "psychological_rules": {
                "rule_1": "لا تتداول بأموال لا تستطيع تحمل خسارتها",
                "rule_2": "التزم بخطة التداول ولا تدع العواطف تتحكم",
                "rule_3": "قطع الخسائر بسرعة واترك الأرباح تجري",
                "rule_4": "راجع أداءك بانتظام وتعلم من الأخطاء",
                "source": "Trading in the Zone - Mark Douglas"
            }
        }
    
    def _generate_recommendation(self, technical, fundamental, risk):
        """توليد التوصية النهائية"""
        signals = []
        
        # إشارات فنية
        if technical['rsi'] > 70:
            signals.append('bearish')
        elif technical['rsi'] < 30:
            signals.append('bullish')
        
        for pattern in technical['patterns']:
            if pattern['signal'] in ['bullish', 'bullish_reversal']:
                signals.append('bullish')
            elif pattern['signal'] in ['bearish', 'bearish_reversal']:
                signals.append('bearish')
        
        # حساب التوصية
        bullish_count = signals.count('bullish')
        bearish_count = signals.count('bearish')
        
        if bullish_count > bearish_count:
            action = "شراء"
            confidence = min(90, 60 + (bullish_count - bearish_count) * 10)
        elif bearish_count > bullish_count:
            action = "بيع"
            confidence = min(90, 60 + (bearish_count - bullish_count) * 10)
        else:
            action = "انتظار"
            confidence = 50
        
        return {
            "action": action,
            "confidence": f"{confidence}%",
            "reasoning": f"بناءً على {len(signals)} إشارة فنية",
            "timeframe": "قصير إلى متوسط المدى",
            "risk_level": "متوسط",
            "educational_quote": self._get_wisdom_quote()
        }
    
    def _get_educational_note(self, symbol):
        """ملاحظة تعليمية"""
        notes = [
            "تذكر: 'الاتجاه صديقك' - Jesse Livermore",
            "لا تضع كل بيضك في سلة واحدة - التنويع مهم",
            "خطط لتداولك، وتداول حسب خطتك",
            "أهم شيء في التداول هو الحفاظ على رأس المال",
            "السوق دائماً على حق - لا تجادله"
        ]
        return random.choice(notes)
    
    def _get_wisdom_quote(self):
        """حكم وأقوال من أساطير التداول"""
        quotes = [
            "أي شيء يمكن أن يحدث في السوق - Mark Douglas",
            "لا تحتاج لمعرفة ما سيحدث لتحقق الربح - Mark Douglas",
            "الاتجاه صديقك حتى ينتهي - Jesse Livermore",
            "قطع خسائرك واترك أرباحك تجري - قاعدة ذهبية",
            "أهم شيء في التداول هو الحفاظ على رأس المال - Van Tharp",
            "السوق دائماً على حق - لا تجادله - Jesse Livermore",
            "التداول الناجح هو 80% علم نفس و20% مهارة - Mark Douglas",
            "لا تضع كل بيضك في سلة واحدة - التنويع مهم - Benjamin Graham",
            "السوق يمكن أن يبقى غير منطقي أكثر مما تستطيع البقاء مفلساً - John Keynes",
            "أفضل استراتيجية هي التي تناسب شخصيتك - Jack Schwager",
            "الحجم يسبق السعر - Joe Granville",
            "التحليل الفني يدرس تأثير العرض والطلب على الأسعار - John Murphy",
            "الشموع اليابانية تكشف علم نفس السوق - Steve Nison",
            "تحديد حجم المركز أهم من اختيار الأسهم - Van Tharp",
            "الانضباط هو مفتاح النجاح في التداول - Alexander Elder",
            "ابحث عن الأسهم الرائدة في القطاعات الرائدة - William O'Neil",
            "المؤشرات تتبع السعر، لا تقوده - John Murphy",
            "التداول مهنة تتطلب التعلم المستمر - Larry Williams",
            "أفضل الصفقات تبدو مخيفة في البداية - Peter Lynch",
            "لا تتداول بأموال لا تستطيع تحمل خسارتها - قاعدة ذهبية"
        ]
        return random.choice(quotes)

    def answer_question(self, question):
        """الإجابة على أسئلة المستخدم بناءً على معرفة أشهر خبراء التداول"""
        question_lower = question.lower()

        # تحليل السؤال وإيجاد الإجابة المناسبة من قاعدة المعرفة الشاملة
        if any(word in question_lower for word in ['rsi', 'مؤشر القوة النسبية']):
            rsi_info = self.knowledge_base['technical_analysis']['indicators']['momentum_indicators']['rsi']
            return {
                "answer": rsi_info['description'],
                "source": rsi_info['source'],
                "author": f"طوره {rsi_info['author']} - أحد رواد التحليل الفني",
                "practical_tip": f"استخدم RSI > {rsi_info['overbought']} كإشارة بيع و RSI < {rsi_info['oversold']} كإشارة شراء. للإشارات المتطرفة استخدم {rsi_info['extreme_levels']['overbought']} و {rsi_info['extreme_levels']['oversold']}",
                "advanced_usage": rsi_info['divergence'],
                "expert_opinion": rsi_info['elder_triple_screen'],
                "book_reference": rsi_info['source']
            }

        elif any(word in question_lower for word in ['macd', 'تقارب', 'تباعد']):
            macd_info = self.knowledge_base['technical_analysis']['indicators']['momentum_indicators']['macd']
            return {
                "answer": macd_info['description'],
                "source": macd_info['source'],
                "developer": f"طوره {macd_info['author']} - {macd_info['developer']}",
                "components": [
                    f"خط MACD: {macd_info['components']['macd_line']}",
                    f"خط الإشارة: {macd_info['components']['signal_line']}",
                    f"الهيستوجرام: {macd_info['components']['histogram']}"
                ],
                "signals": [
                    macd_info['signals']['signal_cross'],
                    macd_info['signals']['zero_cross'],
                    macd_info['signals']['divergence']
                ],
                "expert_opinion": macd_info['murphy_analysis'],
                "book_reference": macd_info['source']
            }

        elif any(word in question_lower for word in ['وقف الخسارة', 'stop loss', 'ستوب']):
            stop_strategies = self.knowledge_base['risk_management']['stop_loss_strategies']
            return {
                "answer": "وقف الخسارة أهم أداة لحماية رأس المال - كما يؤكد جميع الخبراء",
                "strategies": [
                    {
                        "type": "مبني على ATR",
                        "description": stop_strategies['atr_based']['description'],
                        "author": stop_strategies['atr_based']['author'],
                        "calculation": stop_strategies['atr_based']['calculation'],
                        "advantage": stop_strategies['atr_based']['advantage'],
                        "elder_usage": stop_strategies['atr_based']['elder_usage']
                    },
                    {
                        "type": "نسبة مئوية ثابتة",
                        "description": stop_strategies['percentage_based']['description'],
                        "common_percentages": stop_strategies['percentage_based']['common_percentages'],
                        "simplicity": stop_strategies['percentage_based']['simplicity']
                    },
                    {
                        "type": "عند الدعم والمقاومة",
                        "description": stop_strategies['support_resistance']['description'],
                        "logic": stop_strategies['support_resistance']['logic'],
                        "buffer": stop_strategies['support_resistance']['buffer']
                    }
                ],
                "expert_sources": [
                    stop_strategies['atr_based']['source'],
                    stop_strategies['percentage_based']['source'],
                    stop_strategies['support_resistance']['source']
                ]
            }

        elif any(word in question_lower for word in ['حجم المركز', 'position size', 'كم أشتري']):
            van_tharp = self.knowledge_base['risk_management']['position_sizing']['van_tharp_system']
            larry_williams = self.knowledge_base['risk_management']['position_sizing']['larry_williams_method']
            return {
                "answer": "تحديد حجم المركز أهم من اختيار الأسهم نفسها - Van Tharp",
                "van_tharp_method": {
                    "description": van_tharp['description'],
                    "fixed_percentage": van_tharp['fixed_percentage'],
                    "volatility_based": van_tharp['volatility_based'],
                    "kelly_formula": van_tharp['kelly_formula'],
                    "expectancy": van_tharp['expectancy']
                },
                "larry_williams_method": {
                    "description": larry_williams['description'],
                    "optimal_f": larry_williams['optimal_f'],
                    "practical_application": larry_williams['practical_application']
                },
                "expert_sources": [van_tharp['source'], larry_williams['source']],
                "key_principle": "لا تخاطر بأكثر من 1-2% من رأس المال في صفقة واحدة"
            }

        elif any(word in question_lower for word in ['خوف', 'طمع', 'نفسية', 'عواطف', 'psychology']):
            douglas_zone = self.knowledge_base['trading_psychology']['mark_douglas']['trading_in_zone']
            steenbarger = self.knowledge_base['trading_psychology']['brett_steenbarger']
            biases = self.knowledge_base['trading_psychology']['common_biases']

            return {
                "answer": "علم نفس التداول أهم من التحليل الفني نفسه - Mark Douglas",
                "mark_douglas_insights": {
                    "description": douglas_zone['description'],
                    "key_concepts": douglas_zone['key_concepts'],
                    "fundamental_truths": douglas_zone['fundamental_truths']
                },
                "brett_steenbarger_approach": {
                    "description": steenbarger['description'],
                    "approach": steenbarger['approach'],
                    "self_coaching": steenbarger['self_coaching']
                },
                "common_psychological_traps": [
                    {
                        "bias": "تحيز التأكيد",
                        "description": biases['confirmation_bias']['description'],
                        "solution": biases['confirmation_bias']['solution']
                    },
                    {
                        "bias": "تجنب الخسارة",
                        "description": biases['loss_aversion']['description'],
                        "impact": biases['loss_aversion']['impact']
                    },
                    {
                        "bias": "الثقة المفرطة",
                        "description": biases['overconfidence']['description'],
                        "danger": biases['overconfidence']['danger']
                    }
                ],
                "expert_sources": [douglas_zone['source'], steenbarger['source']],
                "practical_advice": "طور خطة تداول واتبعها بانضباط - لا تدع العواطف تتحكم في قراراتك"
            }

        elif any(word in question_lower for word in ['انضباط', 'discipline', 'قواعد']):
            douglas_disciplined = self.knowledge_base['trading_psychology']['mark_douglas']['disciplined_trader']
            return {
                "answer": douglas_disciplined['description'],
                "source": douglas_disciplined['source'],
                "focus": douglas_disciplined['focus'],
                "emotional_control": douglas_disciplined['emotional_control'],
                "book_reference": douglas_disciplined['source']
            }

        elif any(word in question_lower for word in ['رأس كتف', 'head shoulder', 'نماذج']):
            pattern_info = self.knowledge_base['technical_analysis']['chart_patterns']['head_shoulders']
            return {
                "answer": pattern_info['description'],
                "source": pattern_info['source'],
                "author": f"من كتاب {pattern_info['author']} الكلاسيكي",
                "signal": pattern_info['signal'],
                "reliability": f"{pattern_info['reliability']}% موثوقية",
                "volume_importance": pattern_info['volume_confirmation'],
                "entry_strategy": pattern_info['entry_strategy'],
                "stop_loss": pattern_info['stop_loss'],
                "target": pattern_info['target'],
                "bulkowski_research": pattern_info['bulkowski_stats'],
                "book_reference": pattern_info['source']
            }

        elif any(word in question_lower for word in ['شموع يابانية', 'candlestick', 'مطرقة', 'hammer']):
            if 'مطرقة' in question_lower or 'hammer' in question_lower:
                hammer_info = self.knowledge_base['technical_analysis']['candlestick_patterns']['single_candles']['hammer']
                return {
                    "answer": hammer_info['description'],
                    "source": hammer_info['source'],
                    "author": f"من عمل {hammer_info['author']} الرائد في الشموع اليابانية",
                    "signal": hammer_info['signal'],
                    "reliability": f"{hammer_info['reliability']}% موثوقية",
                    "requirements": hammer_info['requirements'],
                    "confirmation": hammer_info['confirmation'],
                    "psychology": hammer_info['psychology'],
                    "book_reference": hammer_info['source']
                }
            else:
                return {
                    "answer": "الشموع اليابانية هي طريقة لعرض حركة الأسعار طورها اليابانيون منذ مئات السنين",
                    "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                    "author": "Steve Nison - الذي قدم الشموع اليابانية للعالم الغربي",
                    "main_patterns": [
                        "المطرقة (Hammer) - انعكاس صاعد",
                        "النجمة الساقطة (Shooting Star) - انعكاس هبوطي",
                        "الدوجي (Doji) - تردد وعدم يقين",
                        "الابتلاع (Engulfing) - انعكاس قوي",
                        "نجمة الصباح/المساء - انعكاس ثلاثي"
                    ],
                    "advantage": "تظهر علم نفس السوق بوضوح أكثر من الرسوم البيانية التقليدية",
                    "book_reference": "Japanese Candlestick Charting Techniques - Steve Nison"
                }

        elif any(word in question_lower for word in ['متوسط متحرك', 'moving average', 'golden cross']):
            ma_info = self.knowledge_base['technical_analysis']['indicators']['trend_indicators']['moving_averages']
            return {
                "answer": "المتوسطات المتحركة من أهم أدوات التحليل الفني لتحديد الاتجاه",
                "source": ma_info['sma']['source'],
                "author": f"شرحها بالتفصيل {ma_info['sma']['author']}",
                "types": [
                    f"البسيط (SMA): {ma_info['sma']['calculation']}",
                    f"الأسي (EMA): {ma_info['ema']['description']}"
                ],
                "common_periods": ma_info['sma']['common_periods'],
                "golden_cross": ma_info['sma']['golden_cross'],
                "death_cross": ma_info['sma']['death_cross'],
                "ema_advantage": ma_info['ema']['advantage'],
                "elder_preference": ma_info['ema']['elder_usage'],
                "book_reference": ma_info['sma']['source']
            }

        elif any(word in question_lower for word in ['فيبوناتشي', 'fibonacci']):
            fib_info = self.knowledge_base['technical_analysis']['fibonacci_analysis']['retracements']
            return {
                "answer": fib_info['description'],
                "source": fib_info['source'],
                "author": f"متخصصة فيه {fib_info['author']}",
                "key_levels": fib_info['key_levels'],
                "golden_ratio": fib_info['golden_ratio'],
                "application": fib_info['application'],
                "murphy_opinion": fib_info['murphy_view'],
                "extensions": self.knowledge_base['technical_analysis']['fibonacci_analysis']['extensions']['description'],
                "book_reference": fib_info['source']
            }

        elif any(word in question_lower for word in ['قيمة', 'pe ratio', 'تقييم']):
            return {
                "answer": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio']['description'],
                "source": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio']['source'],
                "low_pe": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio']['low_pe'],
                "high_pe": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio']['high_pe'],
                "book_reference": "The Intelligent Investor - Benjamin Graham"
            }

        elif any(word in question_lower for word in ['استراتيجية', 'strategy', 'خطة', 'turtle', 'سلاحف']):
            if any(word in question_lower for word in ['turtle', 'سلاحف']):
                turtle = self.knowledge_base['trading_strategies']['trend_following']['turtle_trading']
                return {
                    "answer": turtle['description'],
                    "source": turtle['source'],
                    "creators": turtle['author'],
                    "entry_rules": turtle['entry_rules'],
                    "exit_rules": turtle['exit_rules'],
                    "position_sizing": turtle['position_sizing'],
                    "success_story": turtle['success_rate'],
                    "book_reference": turtle['source']
                }
            elif any(word in question_lower for word in ['can slim', 'oneil', 'أونيل']):
                can_slim = self.knowledge_base['trading_strategies']['can_slim']
                return {
                    "answer": can_slim['description'],
                    "source": can_slim['source'],
                    "author": can_slim['author'],
                    "components": can_slim['components'],
                    "success_stories": can_slim['success_stories'],
                    "book_reference": can_slim['source']
                }
            elif any(word in question_lower for word in ['elder', 'إلدر', 'triple screen', 'شاشات ثلاث']):
                elder = self.knowledge_base['trading_strategies']['swing_trading']['elder_triple_screen']
                return {
                    "answer": elder['description'],
                    "source": elder['source'],
                    "author": elder['author'],
                    "screen_1": elder['screen_1'],
                    "screen_2": elder['screen_2'],
                    "screen_3": elder['screen_3'],
                    "indicators_used": elder['indicators_used'],
                    "philosophy": elder['philosophy'],
                    "book_reference": elder['source']
                }
            else:
                return {
                    "answer": "أفضل الاستراتيجيات من أساطير التداول",
                    "famous_strategies": [
                        "استراتيجية السلاحف (Turtle Trading) - Richard Dennis",
                        "نظام الشاشات الثلاث - Alexander Elder",
                        "منهجية CAN SLIM - William O'Neil",
                        "تداول الاختراقات - Linda Raschke",
                        "تتبع الاتجاه بالمتوسطات المتحركة - John Murphy"
                    ],
                    "key_components": [
                        "تحديد الاتجاه العام للسوق",
                        "البحث عن نقاط دخول عالية الاحتمالية",
                        "وضع وقف خسارة مناسب",
                        "تحديد أهداف ربح واقعية",
                        "إدارة حجم المركز بعناية"
                    ],
                    "expert_advice": "أفضل استراتيجية هي التي تناسب شخصيتك ونمط حياتك - Jack Schwager",
                    "book_reference": "Market Wizards - Jack Schwager"
                }

        else:
            # إجابة عامة مع عرض الخبراء والمصادر
            return {
                "answer": "أهلاً بك في مستشار التداول الذكي! أنا مدعوم بمعرفة أشهر خبراء التداول العالميين",
                "expert_authors": [
                    "John J. Murphy - خبير التحليل الفني الشامل",
                    "Thomas Bulkowski - متخصص أنماط الرسم البياني",
                    "Jack D. Schwager - مؤلف سلسلة Market Wizards",
                    "Alexander Elder - خبير علم نفس التداول",
                    "Steve Nison - رائد الشموع اليابانية",
                    "Larry Williams - خبير المؤشرات والتداول قصير المدى",
                    "William O'Neil - مطور منهجية CAN SLIM",
                    "Mark Douglas - خبير علم نفس التداول",
                    "Van Tharp - خبير إدارة المخاطر"
                ],
                "topics": [
                    "التحليل الفني (أنماط الرسم البياني، المؤشرات، الشموع اليابانية)",
                    "استراتيجيات التداول (السلاحف، CAN SLIM، الشاشات الثلاث)",
                    "إدارة المخاطر (وقف الخسارة، حجم المركز، إدارة الأموال)",
                    "علم نفس التداول (التحكم في العواطف، التحيزات النفسية)",
                    "مؤشرات فيبوناتشي وموجات إليوت",
                    "تحليل أي رمز مالي بناءً على خبرة الأساطير"
                ],
                "suggestion": "اسأل عن أي موضوع مثل: 'ما هو مؤشر RSI؟' أو 'شرح استراتيجية السلاحف' أو 'كيف أتحكم في عواطف التداول؟'",
                "wisdom": self._get_wisdom_quote(),
                "knowledge_base": "مدعوم بأكثر من 50 كتاب ومرجع من أشهر خبراء التداول العالميين"
            }

# إنشاء مثيل من الذكاء الاصطناعي
trading_ai = TradingAI()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/analyze/<symbol>')
def analyze_symbol(symbol):
    """تحليل رمز مالي"""
    analysis = trading_ai.analyze_symbol(symbol.upper())
    return jsonify(analysis)

@app.route('/api/ask', methods=['POST'])
def ask_ai():
    """سؤال الذكاء الاصطناعي"""
    data = request.get_json()
    question = data.get('question', '')
    
    # محاكاة إجابة ذكية
    response = trading_ai.answer_question(question)
    return jsonify(response)

@app.route('/api/symbols')
def get_symbols():
    """قائمة الرموز المتاحة مع التصنيفات"""
    return jsonify(FINANCIAL_SYMBOLS)

@app.route('/api/symbols/all')
def get_all_symbols():
    """قائمة جميع الرموز بدون تصنيف"""
    all_symbols = {}
    for category, symbols in FINANCIAL_SYMBOLS.items():
        all_symbols.update(symbols)
    return jsonify(list(all_symbols.keys()))

@app.route('/api/symbols/<category>')
def get_symbols_by_category(category):
    """قائمة الرموز حسب الفئة"""
    if category in FINANCIAL_SYMBOLS:
        return jsonify(FINANCIAL_SYMBOLS[category])
    return jsonify({"error": "فئة غير موجودة"}), 404

@app.route('/api/data/<symbol>')
def get_symbol_data(symbol):
    """بيانات رمز معين في الوقت الفعلي"""
    try:
        data_provider = RealTimeDataProvider()
        data = data_provider.get_real_time_data(symbol)
        return jsonify(data)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/historical/<symbol>')
def get_historical_data(symbol):
    """البيانات التاريخية لرمز معين"""
    try:
        period = request.args.get('period', '1mo')
        interval = request.args.get('interval', '1d')

        data_provider = RealTimeDataProvider()
        data = data_provider.get_historical_data(symbol, period, interval)
        return jsonify(data)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/indicators/<symbol>')
def get_technical_indicators(symbol):
    """المؤشرات الفنية لرمز معين"""
    try:
        data_provider = RealTimeDataProvider()
        indicators = data_provider.calculate_technical_indicators(symbol)
        return jsonify(indicators)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
