from flask import Flask, render_template, jsonify
import random
import time
from datetime import datetime, timedelta
import threading
import json

app = Flask(__name__)

# بيانات العملات والمؤشرات الأولية
financial_data = {
    'currencies': {
        'EUR/USD': {'price': 1.0850, 'change': 0.0012, 'change_percent': 0.11},
        'GBP/USD': {'price': 1.2650, 'change': -0.0025, 'change_percent': -0.20},
        'USD/JPY': {'price': 149.85, 'change': 0.45, 'change_percent': 0.30},
        'XAU/USD': {'price': 2035.50, 'change': 12.30, 'change_percent': 0.61},
        'XAG/USD': {'price': 24.85, 'change': -0.15, 'change_percent': -0.60},
        'USD/CAD': {'price': 1.3580, 'change': 0.0020, 'change_percent': 0.15},
        'AUD/USD': {'price': 0.6520, 'change': -0.0035, 'change_percent': -0.53},
        'NZD/USD': {'price': 0.5980, 'change': -0.0015, 'change_percent': -0.25},
        'USD/CHF': {'price': 0.9120, 'change': 0.0008, 'change_percent': 0.09},
        'EUR/GBP': {'price': 0.8580, 'change': 0.0018, 'change_percent': 0.21}
    },
    'indices': {
        'S&P 500': {'price': 4785.25, 'change': 25.80, 'change_percent': 0.54},
        'NASDAQ': {'price': 15125.30, 'change': -45.20, 'change_percent': -0.30},
        'DOW JONES': {'price': 37850.15, 'change': 180.50, 'change_percent': 0.48},
        'FTSE 100': {'price': 7650.80, 'change': -12.30, 'change_percent': -0.16},
        'DAX': {'price': 16580.25, 'change': 85.40, 'change_percent': 0.52},
        'NIKKEI 225': {'price': 33250.75, 'change': 125.60, 'change_percent': 0.38},
        'CAC 40': {'price': 7420.90, 'change': -18.50, 'change_percent': -0.25},
        'ASX 200': {'price': 7580.40, 'change': 35.20, 'change_percent': 0.47}
    },
    'commodities': {
        'Crude Oil': {'price': 78.45, 'change': 1.25, 'change_percent': 1.62},
        'Natural Gas': {'price': 2.85, 'change': -0.08, 'change_percent': -2.73},
        'Copper': {'price': 3.85, 'change': 0.05, 'change_percent': 1.32},
        'Platinum': {'price': 925.50, 'change': -8.20, 'change_percent': -0.88},
        'Palladium': {'price': 1125.80, 'change': 15.30, 'change_percent': 1.38}
    },
    'crypto': {
        'BTC/USD': {'price': 43250.80, 'change': 850.25, 'change_percent': 2.01},
        'ETH/USD': {'price': 2580.45, 'change': -45.80, 'change_percent': -1.74},
        'BNB/USD': {'price': 315.20, 'change': 8.50, 'change_percent': 2.77},
        'ADA/USD': {'price': 0.485, 'change': 0.012, 'change_percent': 2.54},
        'SOL/USD': {'price': 98.75, 'change': 3.25, 'change_percent': 3.40}
    }
}

# تخزين البيانات التاريخية للتشارت
chart_data = {}
for category in financial_data:
    chart_data[category] = {}
    for symbol in financial_data[category]:
        chart_data[category][symbol] = {
            'timestamps': [],
            'prices': []
        }

def update_prices():
    """تحديث الأسعار بشكل عشوائي لمحاكاة السوق الحقيقي"""
    while True:
        current_time = datetime.now().strftime('%H:%M:%S')
        
        for category in financial_data:
            for symbol in financial_data[category]:
                # تحديث السعر بشكل عشوائي
                current_price = financial_data[category][symbol]['price']
                change_factor = random.uniform(-0.002, 0.002)  # تغيير بنسبة 0.2%
                new_price = current_price * (1 + change_factor)
                
                # حساب التغيير
                price_change = new_price - current_price
                change_percent = (price_change / current_price) * 100
                
                # تحديث البيانات
                financial_data[category][symbol]['price'] = round(new_price, 2)
                financial_data[category][symbol]['change'] = round(price_change, 4)
                financial_data[category][symbol]['change_percent'] = round(change_percent, 2)
                
                # إضافة البيانات للتشارت
                chart_data[category][symbol]['timestamps'].append(current_time)
                chart_data[category][symbol]['prices'].append(round(new_price, 2))
                
                # الاحتفاظ بآخر 50 نقطة فقط
                if len(chart_data[category][symbol]['timestamps']) > 50:
                    chart_data[category][symbol]['timestamps'].pop(0)
                    chart_data[category][symbol]['prices'].pop(0)
        
        time.sleep(2)  # تحديث كل ثانيتين

# بدء تحديث الأسعار في خيط منفصل
price_thread = threading.Thread(target=update_prices, daemon=True)
price_thread.start()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/data')
def get_data():
    """إرجاع البيانات المالية الحالية"""
    return jsonify(financial_data)

@app.route('/api/chart/<category>/<symbol>')
def get_chart_data(category, symbol):
    """إرجاع بيانات التشارت لرمز معين"""
    if category in chart_data and symbol in chart_data[category]:
        return jsonify(chart_data[category][symbol])
    return jsonify({'error': 'Symbol not found'}), 404

@app.route('/api/all_chart_data')
def get_all_chart_data():
    """إرجاع جميع بيانات التشارت"""
    return jsonify(chart_data)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
