from flask import Flask, render_template, jsonify
import json
import time
from datetime import datetime
import threading
import random

app = Flask(__name__)

# Alpha Vantage API Key (مجاني - يمكن الحصول عليه من alphavantage.co)
ALPHA_VANTAGE_API_KEY = "demo"  # استخدم "demo" للاختبار أو احصل على مفتاح مجاني

# قائمة الرموز المالية المدعومة
SYMBOLS = {
    'EURUSD': 'EUR/USD',
    'GBPUSD': 'GBP/USD',
    'USDJPY': 'USD/JPY',
    'USDCHF': 'USD/CHF',
    'AUDUSD': 'AUD/USD',
    'USDCAD': 'USD/CAD',
    'NZDUSD': 'NZD/USD',
    'XAUUSD': 'XAU/USD (Gold)',
    'XAGUSD': 'XAG/USD (Silver)',
    'AAPL': 'Apple Inc',
    'GOOGL': 'Alphabet Inc',
    'MSFT': 'Microsoft Corp',
    'TSLA': 'Tesla Inc',
    'AMZN': 'Amazon.com Inc',
    'BTC': 'Bitcoin',
    'ETH': 'Ethereum'
}

# تخزين البيانات المؤقت
data_cache = {}
chart_data = {}

def get_real_time_data(symbol):
    """جلب البيانات الحقيقية - مؤقتاً نستخدم بيانات محاكاة واقعية"""
    # مؤقتاً نستخدم بيانات محاكاة حتى نحل مشكلة requests
    return generate_realistic_data(symbol)

def generate_realistic_data(symbol):
    """توليد بيانات واقعية للرموز"""
    base_prices = {
        'EURUSD': 1.0850,
        'GBPUSD': 1.2650,
        'USDJPY': 149.85,
        'USDCHF': 0.9120,
        'AUDUSD': 0.6520,
        'USDCAD': 1.3580,
        'NZDUSD': 0.5980,
        'XAUUSD': 2035.50,
        'XAGUSD': 24.85,
        'AAPL': 195.50,
        'GOOGL': 142.30,
        'MSFT': 415.20,
        'TSLA': 248.50,
        'AMZN': 155.80,
        'BTC': 43250.80,
        'ETH': 2580.45
    }

    base_price = base_prices.get(symbol, 100.0)

    # إضافة تقلبات واقعية
    if symbol in data_cache:
        last_price = data_cache[symbol]['price']
        # تقلبات صغيرة واقعية
        change_factor = random.uniform(-0.001, 0.001)  # 0.1% تقلب
        new_price = last_price * (1 + change_factor)
    else:
        new_price = base_price

    change = new_price - base_price
    change_percent = (change / base_price) * 100

    return {
        'price': round(new_price, 4),
        'change': round(change, 4),
        'change_percent': round(change_percent, 2),
        'timestamp': datetime.now().isoformat()
    }

def update_all_data():
    """تحديث جميع البيانات"""
    while True:
        current_time = datetime.now()

        for symbol in SYMBOLS.keys():
            try:
                new_data = get_real_time_data(symbol)
                data_cache[symbol] = new_data

                # إضافة للتشارت
                if symbol not in chart_data:
                    chart_data[symbol] = {
                        'timestamps': [],
                        'prices': []
                    }

                chart_data[symbol]['timestamps'].append(current_time.strftime('%H:%M:%S'))
                chart_data[symbol]['prices'].append(new_data['price'])

                # الاحتفاظ بآخر 100 نقطة
                if len(chart_data[symbol]['timestamps']) > 100:
                    chart_data[symbol]['timestamps'].pop(0)
                    chart_data[symbol]['prices'].pop(0)

            except Exception as e:
                print(f"Error updating {symbol}: {e}")

        time.sleep(5)  # تحديث كل 5 ثوانٍ

# بدء تحديث البيانات
data_thread = threading.Thread(target=update_all_data, daemon=True)
data_thread.start()

@app.route('/')
def index():
    return render_template('index.html', symbols=SYMBOLS)

@app.route('/api/symbols')
def get_symbols():
    """إرجاع قائمة الرموز المدعومة"""
    return jsonify(SYMBOLS)

@app.route('/api/data/<symbol>')
def get_symbol_data(symbol):
    """إرجاع بيانات رمز معين"""
    if symbol in data_cache:
        return jsonify(data_cache[symbol])
    return jsonify({'error': 'Symbol not found'}), 404

@app.route('/api/chart/<symbol>')
def get_chart_data(symbol):
    """إرجاع بيانات التشارت لرمز معين"""
    if symbol in chart_data:
        return jsonify(chart_data[symbol])
    return jsonify({'error': 'Symbol not found'}), 404

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
