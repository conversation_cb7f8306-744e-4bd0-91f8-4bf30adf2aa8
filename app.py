from flask import Flask, render_template, request, jsonify
import json
import random
import time
from datetime import datetime, timedelta
import threading

app = Flask(__name__)

# قاعدة معرفة التداول من أشهر الكتب
TRADING_KNOWLEDGE = {
    "technical_analysis": {
        "patterns": {
            "head_shoulders": {
                "description": "نموذج الرأس والكتفين - نموذج انعكاسي قوي",
                "source": "Technical Analysis of the Financial Markets - John Murphy",
                "signal": "bearish_reversal",
                "reliability": 85,
                "entry_strategy": "كسر خط العنق مع حجم تداول عالي",
                "stop_loss": "أعلى الكتف الأيمن",
                "target": "المسافة من الرأس إلى خط العنق"
            },
            "double_top": {
                "description": "القمة المزدوجة - إشارة انعكاس هبوطي",
                "source": "Encyclopedia of Chart Patterns - <PERSON>",
                "signal": "bearish_reversal",
                "reliability": 78,
                "entry_strategy": "كسر الدعم بين القمتين",
                "stop_loss": "أعلى القمة الثانية",
                "target": "المسافة بين القمة والدعم"
            },
            "hammer": {
                "description": "شمعة المطرقة - إشارة انعكاس صاعد",
                "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                "signal": "bullish_reversal",
                "reliability": 70,
                "entry_strategy": "إغلاق أعلى من أعلى سعر للمطرقة",
                "stop_loss": "أسفل أدنى سعر للمطرقة",
                "target": "ضعف حجم جسم الشمعة"
            }
        },
        "indicators": {
            "rsi": {
                "description": "مؤشر القوة النسبية - يقيس زخم السعر",
                "source": "New Concepts in Technical Trading Systems - Welles Wilder",
                "overbought": 70,
                "oversold": 30,
                "divergence_signal": "قوي جداً",
                "best_timeframe": "يومي وأسبوعي"
            },
            "macd": {
                "description": "مؤشر تقارب وتباعد المتوسطات المتحركة",
                "source": "Technical Analysis of the Financial Markets - John Murphy",
                "signal_line_cross": "إشارة دخول قوية",
                "histogram": "يظهر قوة الاتجاه",
                "zero_line_cross": "تأكيد تغيير الاتجاه"
            }
        }
    },
    "fundamental_analysis": {
        "value_investing": {
            "pe_ratio": {
                "description": "نسبة السعر إلى الأرباح",
                "source": "The Intelligent Investor - Benjamin Graham",
                "low_pe": "قد يشير لقيمة مقللة",
                "high_pe": "قد يشير لتوقعات نمو عالية",
                "industry_comparison": "مهم جداً"
            },
            "book_value": {
                "description": "القيمة الدفترية للسهم",
                "source": "Security Analysis - Graham & Dodd",
                "pb_ratio": "السعر إلى القيمة الدفترية",
                "margin_of_safety": "اشتري بأقل من القيمة الحقيقية"
            }
        }
    },
    "risk_management": {
        "position_sizing": {
            "description": "حجم المركز المناسب",
            "source": "Trade Your Way to Financial Freedom - Van Tharp",
            "kelly_formula": "للحساب الأمثل لحجم المركز",
            "fixed_percentage": "لا تخاطر بأكثر من 2% من رأس المال",
            "volatility_based": "اضبط الحجم حسب التقلبات"
        },
        "stop_loss": {
            "description": "وقف الخسارة",
            "source": "Market Wizards - Jack Schwager",
            "atr_based": "استخدم متوسط المدى الحقيقي",
            "percentage_based": "نسبة ثابتة من سعر الدخول",
            "support_resistance": "عند مستويات الدعم والمقاومة"
        }
    },
    "psychology": {
        "emotions": {
            "fear_greed": {
                "description": "الخوف والطمع - أكبر أعداء المتداول",
                "source": "Trading in the Zone - Mark Douglas",
                "fear_symptoms": "تجنب الدخول في صفقات جيدة",
                "greed_symptoms": "عدم أخذ الأرباح في الوقت المناسب",
                "solution": "خطة تداول محددة مسبقاً"
            },
            "discipline": {
                "description": "الانضباط في التداول",
                "source": "The Disciplined Trader - Mark Douglas",
                "rules": "اتبع قواعدك دائماً",
                "journal": "احتفظ بسجل تداول مفصل",
                "review": "راجع أداءك بانتظام"
            }
        }
    }
}

# بيانات السوق المحاكاة
MARKET_DATA = {
    'EURUSD': {'price': 1.0850, 'change': 0.0012, 'volume': 1250000, 'trend': 'bullish'},
    'GBPUSD': {'price': 1.2650, 'change': -0.0025, 'volume': 980000, 'trend': 'bearish'},
    'USDJPY': {'price': 149.85, 'change': 0.45, 'volume': 1100000, 'trend': 'bullish'},
    'XAUUSD': {'price': 2035.50, 'change': 12.30, 'volume': 850000, 'trend': 'bullish'},
    'BTCUSD': {'price': 43250.80, 'change': 850.25, 'volume': 2500000, 'trend': 'bullish'},
    'AAPL': {'price': 195.50, 'change': -2.30, 'volume': 45000000, 'trend': 'bearish'},
    'TSLA': {'price': 248.50, 'change': 5.80, 'volume': 38000000, 'trend': 'bullish'}
}

class TradingAI:
    def __init__(self):
        self.knowledge_base = TRADING_KNOWLEDGE
        self.market_data = MARKET_DATA
    
    def analyze_symbol(self, symbol):
        """تحليل شامل لرمز مالي"""
        if symbol not in self.market_data:
            return {"error": "الرمز غير موجود"}
        
        data = self.market_data[symbol]
        analysis = {
            "symbol": symbol,
            "current_price": data['price'],
            "change": data['change'],
            "change_percent": (data['change'] / data['price']) * 100,
            "volume": data['volume'],
            "trend": data['trend']
        }
        
        # تحليل فني
        technical_analysis = self._technical_analysis(symbol, data)
        
        # تحليل أساسي (للأسهم)
        fundamental_analysis = self._fundamental_analysis(symbol, data)
        
        # إدارة المخاطر
        risk_management = self._risk_management(symbol, data)
        
        # توصية نهائية
        recommendation = self._generate_recommendation(technical_analysis, fundamental_analysis, risk_management)
        
        return {
            "analysis": analysis,
            "technical": technical_analysis,
            "fundamental": fundamental_analysis,
            "risk": risk_management,
            "recommendation": recommendation,
            "educational_note": self._get_educational_note(symbol)
        }
    
    def _technical_analysis(self, symbol, data):
        """التحليل الفني"""
        # محاكاة مؤشرات فنية
        rsi = random.randint(20, 80)
        macd_signal = random.choice(['bullish', 'bearish', 'neutral'])
        
        patterns = []
        if rsi > 70:
            patterns.append({
                "pattern": "RSI Overbought",
                "description": self.knowledge_base['technical_analysis']['indicators']['rsi']['description'],
                "signal": "bearish",
                "source": self.knowledge_base['technical_analysis']['indicators']['rsi']['source']
            })
        elif rsi < 30:
            patterns.append({
                "pattern": "RSI Oversold", 
                "description": self.knowledge_base['technical_analysis']['indicators']['rsi']['description'],
                "signal": "bullish",
                "source": self.knowledge_base['technical_analysis']['indicators']['rsi']['source']
            })
        
        # إضافة نماذج عشوائية للتوضيح
        if random.random() > 0.7:
            pattern_key = random.choice(list(self.knowledge_base['technical_analysis']['patterns'].keys()))
            pattern_info = self.knowledge_base['technical_analysis']['patterns'][pattern_key]
            patterns.append({
                "pattern": pattern_key.replace('_', ' ').title(),
                "description": pattern_info['description'],
                "signal": pattern_info['signal'],
                "reliability": pattern_info['reliability'],
                "source": pattern_info['source'],
                "entry_strategy": pattern_info['entry_strategy'],
                "stop_loss": pattern_info['stop_loss'],
                "target": pattern_info['target']
            })
        
        return {
            "rsi": rsi,
            "macd_signal": macd_signal,
            "patterns": patterns,
            "trend_strength": random.choice(['قوي', 'متوسط', 'ضعيف']),
            "support_level": data['price'] * 0.98,
            "resistance_level": data['price'] * 1.02
        }
    
    def _fundamental_analysis(self, symbol, data):
        """التحليل الأساسي"""
        if symbol in ['AAPL', 'TSLA', 'GOOGL', 'MSFT']:
            # للأسهم
            pe_ratio = random.uniform(15, 35)
            pb_ratio = random.uniform(1, 5)
            
            value_assessment = "مقيم بعدالة"
            if pe_ratio < 15:
                value_assessment = "مقيم بأقل من قيمته"
            elif pe_ratio > 25:
                value_assessment = "مقيم بأكثر من قيمته"
            
            return {
                "pe_ratio": round(pe_ratio, 2),
                "pb_ratio": round(pb_ratio, 2),
                "value_assessment": value_assessment,
                "graham_analysis": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio'],
                "recommendation": "اشتري" if pe_ratio < 20 else "احتفظ" if pe_ratio < 25 else "بع"
            }
        else:
            # للعملات والسلع
            return {
                "economic_factors": "تحليل العوامل الاقتصادية",
                "central_bank_policy": "سياسة البنك المركزي",
                "geopolitical_events": "الأحداث الجيوسياسية",
                "note": "التحليل الأساسي للعملات يتطلب متابعة الأخبار الاقتصادية"
            }
    
    def _risk_management(self, symbol, data):
        """إدارة المخاطر"""
        position_size = self.knowledge_base['risk_management']['position_sizing']
        stop_loss_info = self.knowledge_base['risk_management']['stop_loss']
        
        # حساب وقف الخسارة المقترح
        atr_stop = data['price'] * 0.02  # 2% ATR محاكاة
        percentage_stop = data['price'] * 0.015  # 1.5%
        
        return {
            "position_sizing": {
                "description": position_size['description'],
                "source": position_size['source'],
                "recommended_risk": "2% من رأس المال",
                "kelly_criterion": "استخدم معادلة كيلي للحساب الدقيق"
            },
            "stop_loss": {
                "atr_based": round(atr_stop, 4),
                "percentage_based": round(percentage_stop, 4),
                "recommendation": stop_loss_info['atr_based'],
                "source": stop_loss_info['source']
            },
            "risk_reward_ratio": "1:2 كحد أدنى",
            "max_positions": "لا تفتح أكثر من 3-5 مراكز في نفس الوقت"
        }
    
    def _generate_recommendation(self, technical, fundamental, risk):
        """توليد التوصية النهائية"""
        signals = []
        
        # إشارات فنية
        if technical['rsi'] > 70:
            signals.append('bearish')
        elif technical['rsi'] < 30:
            signals.append('bullish')
        
        for pattern in technical['patterns']:
            if pattern['signal'] in ['bullish', 'bullish_reversal']:
                signals.append('bullish')
            elif pattern['signal'] in ['bearish', 'bearish_reversal']:
                signals.append('bearish')
        
        # حساب التوصية
        bullish_count = signals.count('bullish')
        bearish_count = signals.count('bearish')
        
        if bullish_count > bearish_count:
            action = "شراء"
            confidence = min(90, 60 + (bullish_count - bearish_count) * 10)
        elif bearish_count > bullish_count:
            action = "بيع"
            confidence = min(90, 60 + (bearish_count - bullish_count) * 10)
        else:
            action = "انتظار"
            confidence = 50
        
        return {
            "action": action,
            "confidence": f"{confidence}%",
            "reasoning": f"بناءً على {len(signals)} إشارة فنية",
            "timeframe": "قصير إلى متوسط المدى",
            "risk_level": "متوسط",
            "educational_quote": self._get_wisdom_quote()
        }
    
    def _get_educational_note(self, symbol):
        """ملاحظة تعليمية"""
        notes = [
            "تذكر: 'الاتجاه صديقك' - Jesse Livermore",
            "لا تضع كل بيضك في سلة واحدة - التنويع مهم",
            "خطط لتداولك، وتداول حسب خطتك",
            "أهم شيء في التداول هو الحفاظ على رأس المال",
            "السوق دائماً على حق - لا تجادله"
        ]
        return random.choice(notes)
    
    def _get_wisdom_quote(self):
        """حكمة من كتب التداول"""
        quotes = [
            "في الأسواق المالية، الصبر فضيلة والطمع رذيلة - Benjamin Graham",
            "قطع خسائرك واتركي أرباحك تجري - David Ricardo",
            "السوق يمكن أن يبقى غير منطقي أكثر مما تستطيع البقاء مفلساً - John Keynes",
            "التداول الناجح هو 80% علم نفس و20% مهارة - Mark Douglas",
            "لا تتداول بأموال لا تستطيع تحمل خسارتها - قاعدة ذهبية"
        ]
        return random.choice(quotes)

    def answer_question(self, question):
        """الإجابة على أسئلة المستخدم بناءً على معرفة كتب التداول"""
        question_lower = question.lower()

        # تحليل السؤال وإيجاد الإجابة المناسبة
        if any(word in question_lower for word in ['rsi', 'مؤشر القوة النسبية']):
            return {
                "answer": self.knowledge_base['technical_analysis']['indicators']['rsi']['description'],
                "source": self.knowledge_base['technical_analysis']['indicators']['rsi']['source'],
                "practical_tip": f"استخدم RSI > {self.knowledge_base['technical_analysis']['indicators']['rsi']['overbought']} كإشارة بيع و RSI < {self.knowledge_base['technical_analysis']['indicators']['rsi']['oversold']} كإشارة شراء",
                "book_reference": "New Concepts in Technical Trading Systems - Welles Wilder"
            }

        elif any(word in question_lower for word in ['macd', 'تقارب', 'تباعد']):
            return {
                "answer": self.knowledge_base['technical_analysis']['indicators']['macd']['description'],
                "source": self.knowledge_base['technical_analysis']['indicators']['macd']['source'],
                "practical_tip": "ابحث عن تقاطع خط الإشارة مع خط MACD كإشارة دخول",
                "book_reference": "Technical Analysis of the Financial Markets - John Murphy"
            }

        elif any(word in question_lower for word in ['وقف الخسارة', 'stop loss', 'ستوب']):
            return {
                "answer": self.knowledge_base['risk_management']['stop_loss']['description'],
                "source": self.knowledge_base['risk_management']['stop_loss']['source'],
                "practical_tip": "استخدم ATR (متوسط المدى الحقيقي) لحساب وقف الخسارة المناسب",
                "strategies": [
                    self.knowledge_base['risk_management']['stop_loss']['atr_based'],
                    self.knowledge_base['risk_management']['stop_loss']['percentage_based'],
                    self.knowledge_base['risk_management']['stop_loss']['support_resistance']
                ]
            }

        elif any(word in question_lower for word in ['حجم المركز', 'position size', 'كم أشتري']):
            return {
                "answer": self.knowledge_base['risk_management']['position_sizing']['description'],
                "source": self.knowledge_base['risk_management']['position_sizing']['source'],
                "practical_tip": self.knowledge_base['risk_management']['position_sizing']['fixed_percentage'],
                "kelly_formula": self.knowledge_base['risk_management']['position_sizing']['kelly_formula'],
                "book_reference": "Trade Your Way to Financial Freedom - Van Tharp"
            }

        elif any(word in question_lower for word in ['خوف', 'طمع', 'نفسية', 'عواطف']):
            return {
                "answer": self.knowledge_base['psychology']['emotions']['fear_greed']['description'],
                "source": self.knowledge_base['psychology']['emotions']['fear_greed']['source'],
                "fear_symptoms": self.knowledge_base['psychology']['emotions']['fear_greed']['fear_symptoms'],
                "greed_symptoms": self.knowledge_base['psychology']['emotions']['fear_greed']['greed_symptoms'],
                "solution": self.knowledge_base['psychology']['emotions']['fear_greed']['solution'],
                "book_reference": "Trading in the Zone - Mark Douglas"
            }

        elif any(word in question_lower for word in ['انضباط', 'discipline', 'قواعد']):
            return {
                "answer": self.knowledge_base['psychology']['emotions']['discipline']['description'],
                "source": self.knowledge_base['psychology']['emotions']['discipline']['source'],
                "rules": self.knowledge_base['psychology']['emotions']['discipline']['rules'],
                "journal": self.knowledge_base['psychology']['emotions']['discipline']['journal'],
                "review": self.knowledge_base['psychology']['emotions']['discipline']['review']
            }

        elif any(word in question_lower for word in ['رأس كتف', 'head shoulder', 'نماذج']):
            pattern_info = self.knowledge_base['technical_analysis']['patterns']['head_shoulders']
            return {
                "answer": pattern_info['description'],
                "source": pattern_info['source'],
                "signal": pattern_info['signal'],
                "reliability": f"{pattern_info['reliability']}%",
                "entry_strategy": pattern_info['entry_strategy'],
                "stop_loss": pattern_info['stop_loss'],
                "target": pattern_info['target']
            }

        elif any(word in question_lower for word in ['قيمة', 'pe ratio', 'تقييم']):
            return {
                "answer": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio']['description'],
                "source": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio']['source'],
                "low_pe": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio']['low_pe'],
                "high_pe": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio']['high_pe'],
                "book_reference": "The Intelligent Investor - Benjamin Graham"
            }

        elif any(word in question_lower for word in ['استراتيجية', 'strategy', 'خطة']):
            return {
                "answer": "الاستراتيجية الناجحة تتكون من: نقطة دخول، وقف خسارة، هدف ربح، وإدارة مخاطر",
                "components": [
                    "تحديد الاتجاه العام للسوق",
                    "البحث عن نقاط دخول عالية الاحتمالية",
                    "وضع وقف خسارة مناسب",
                    "تحديد أهداف ربح واقعية",
                    "إدارة حجم المركز"
                ],
                "book_reference": "Market Wizards - Jack Schwager",
                "wisdom": "أفضل استراتيجية هي التي تناسب شخصيتك ونمط حياتك"
            }

        else:
            # إجابة عامة
            return {
                "answer": "أهلاً بك في مستشار التداول الذكي! يمكنني مساعدتك في:",
                "topics": [
                    "التحليل الفني (RSI, MACD, النماذج السعرية)",
                    "التحليل الأساسي (تقييم الأسهم)",
                    "إدارة المخاطر (وقف الخسارة، حجم المركز)",
                    "علم نفس التداول (التحكم في العواطف)",
                    "استراتيجيات التداول",
                    "تحليل أي رمز مالي"
                ],
                "suggestion": "جرب أن تسأل عن موضوع محدد مثل: 'ما هو مؤشر RSI؟' أو 'كيف أحسب وقف الخسارة؟'",
                "wisdom": self._get_wisdom_quote()
            }

# إنشاء مثيل من الذكاء الاصطناعي
trading_ai = TradingAI()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/analyze/<symbol>')
def analyze_symbol(symbol):
    """تحليل رمز مالي"""
    analysis = trading_ai.analyze_symbol(symbol.upper())
    return jsonify(analysis)

@app.route('/api/ask', methods=['POST'])
def ask_ai():
    """سؤال الذكاء الاصطناعي"""
    data = request.get_json()
    question = data.get('question', '')
    
    # محاكاة إجابة ذكية
    response = trading_ai.answer_question(question)
    return jsonify(response)

@app.route('/api/symbols')
def get_symbols():
    """قائمة الرموز المتاحة"""
    return jsonify(list(MARKET_DATA.keys()))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
