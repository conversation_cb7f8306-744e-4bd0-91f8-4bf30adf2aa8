from flask import Flask, render_template, request, jsonify
import json
import random
import time
from datetime import datetime, timedelta
import threading

app = Flask(__name__)

# قاعدة المعرفة الشاملة من أشهر خبراء التداول العالميين
TRADING_KNOWLEDGE = {
    "authors": {
        "john_murphy": {
            "name": "<PERSON>",
            "expertise": "التحليل الفني الشامل",
            "main_book": "Technical Analysis of the Financial Markets",
            "contributions": ["نظرية داو", "المتوسطات المتحركة", "مؤشرات الزخم", "أنماط الرسم البياني"]
        },
        "thomas_bulkowski": {
            "name": "<PERSON>",
            "expertise": "أنماط الرسم البياني",
            "main_book": "Encyclopedia of Chart Patterns",
            "contributions": ["إحصائيات دقة النماذج", "تحليل الأداء التاريخي", "استراتيجيات الدخول والخروج"]
        },
        "jack_schwager": {
            "name": "Jack D. Schwager",
            "expertise": "استراتيجيات المتداولين المحترفين",
            "main_book": "Market Wizards Series",
            "contributions": ["مقابلات مع أفضل المتداولين", "استراتيجيات متنوعة", "علم نفس النجاح"]
        },
        "alexander_elder": {
            "name": "Alexander Elder",
            "expertise": "علم نفس التداول وأنظمة التداول",
            "main_book": "Trading for a Living",
            "contributions": ["نظام الشاشات الثلاث", "علم النفس", "إدارة المخاطر"]
        },
        "steve_nison": {
            "name": "Steve Nison",
            "expertise": "الشموع اليابانية",
            "main_book": "Japanese Candlestick Charting Techniques",
            "contributions": ["تقديم الشموع اليابانية للغرب", "أنماط الشموع", "تحليل الانعكاسات"]
        },
        "larry_williams": {
            "name": "Larry Williams",
            "expertise": "المؤشرات والاستراتيجيات قصيرة المدى",
            "main_book": "Long-Term Secrets to Short-Term Trading",
            "contributions": ["مؤشر Williams %R", "استراتيجيات التداول اليومي", "تحليل الزخم"]
        },
        "william_oneil": {
            "name": "William J. O'Neil",
            "expertise": "تحليل الأسهم النامية",
            "main_book": "How to Make Money in Stocks",
            "contributions": ["منهجية CAN SLIM", "تحليل الأسهم الرائدة", "توقيت السوق"]
        },
        "edwards_magee": {
            "name": "Edwards & Magee",
            "expertise": "التحليل الفني الكلاسيكي",
            "main_book": "Technical Analysis of Stock Trends",
            "contributions": ["أسس التحليل الفني", "نظرية الاتجاه", "أنماط الاستمرار والانعكاس"]
        },
        "gerald_appel": {
            "name": "Gerald Appel",
            "expertise": "مؤشرات التحليل الفني",
            "main_book": "Technical Analysis: Power Tools for Active Investors",
            "contributions": ["تطوير مؤشر MACD", "استراتيجيات المتوسطات المتحركة"]
        },
        "martin_pring": {
            "name": "Martin Pring",
            "expertise": "تحليل الدورات والزخم",
            "main_book": "Technical Analysis Explained",
            "contributions": ["مؤشر KST", "تحليل الدورات الزمنية", "مؤشرات الزخم المتقدمة"]
        }
    },

    "technical_analysis": {
        "chart_patterns": {
            "head_shoulders": {
                "description": "نموذج الرأس والكتفين - أقوى نماذج الانعكاس",
                "source": "Technical Analysis of Stock Trends - Edwards & Magee",
                "author": "Edwards & Magee",
                "signal": "bearish_reversal",
                "reliability": 85,
                "volume_confirmation": "ضروري عند كسر خط العنق",
                "entry_strategy": "كسر خط العنق مع حجم تداول عالي",
                "stop_loss": "أعلى الكتف الأيمن",
                "target": "المسافة من الرأس إلى خط العنق",
                "bulkowski_stats": "نجح في 83% من الحالات حسب إحصائيات Bulkowski"
            },
            "double_top": {
                "description": "القمة المزدوجة - نموذج انعكاس هبوطي قوي",
                "source": "Encyclopedia of Chart Patterns - Thomas Bulkowski",
                "author": "Thomas Bulkowski",
                "signal": "bearish_reversal",
                "reliability": 78,
                "entry_strategy": "كسر الدعم بين القمتين مع حجم عالي",
                "stop_loss": "أعلى القمة الثانية بـ 3-5%",
                "target": "المسافة بين القمة والدعم",
                "failure_rate": "22% حسب إحصائيات Bulkowski",
                "time_frame": "يعمل بشكل أفضل على الإطارات الزمنية الطويلة"
            },
            "triangles": {
                "ascending_triangle": {
                    "description": "المثلث الصاعد - نموذج استمرار صاعد",
                    "source": "Technical Analysis of the Financial Markets - John Murphy",
                    "author": "John Murphy",
                    "signal": "bullish_continuation",
                    "reliability": 72,
                    "volume_pattern": "يقل الحجم أثناء التكوين ويزيد عند الكسر"
                },
                "descending_triangle": {
                    "description": "المثلث الهابط - نموذج استمرار هبوطي",
                    "source": "Technical Analysis of the Financial Markets - John Murphy",
                    "author": "John Murphy",
                    "signal": "bearish_continuation",
                    "reliability": 70
                },
                "symmetrical_triangle": {
                    "description": "المثلث المتماثل - نموذج استمرار محايد",
                    "source": "Technical Analysis of Stock Trends - Edwards & Magee",
                    "author": "Edwards & Magee",
                    "signal": "continuation",
                    "reliability": 65,
                    "direction": "يتبع الاتجاه السابق في 75% من الحالات"
                }
            },
            "flags_pennants": {
                "flag": {
                    "description": "العلم - نموذج استمرار قصير المدى",
                    "source": "Technical Analysis of Stock Trends - Edwards & Magee",
                    "author": "Edwards & Magee",
                    "signal": "continuation",
                    "reliability": 80,
                    "duration": "عادة 1-3 أسابيع",
                    "volume": "ينخفض أثناء التكوين"
                },
                "pennant": {
                    "description": "الراية - نموذج استمرار مشابه للعلم",
                    "source": "Technical Analysis of the Financial Markets - John Murphy",
                    "author": "John Murphy",
                    "signal": "continuation",
                    "reliability": 78
                }
            }
        },

        "candlestick_patterns": {
            "single_candles": {
                "hammer": {
                    "description": "المطرقة - شمعة انعكاس صاعد قوية",
                    "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                    "author": "Steve Nison",
                    "signal": "bullish_reversal",
                    "reliability": 70,
                    "requirements": "ذيل سفلي طويل، جسم صغير في الأعلى",
                    "confirmation": "إغلاق اليوم التالي أعلى من أعلى المطرقة",
                    "psychology": "البائعون دفعوا السعر لأسفل لكن المشترين استعادوا السيطرة"
                },
                "shooting_star": {
                    "description": "النجمة الساقطة - شمعة انعكاس هبوطي",
                    "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                    "author": "Steve Nison",
                    "signal": "bearish_reversal",
                    "reliability": 68,
                    "requirements": "ذيل علوي طويل، جسم صغير في الأسفل"
                },
                "doji": {
                    "description": "الدوجي - شمعة تردد وعدم يقين",
                    "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                    "author": "Steve Nison",
                    "signal": "indecision",
                    "reliability": 60,
                    "meaning": "توازن بين قوى العرض والطلب"
                }
            },
            "multiple_candles": {
                "engulfing": {
                    "bullish_engulfing": {
                        "description": "الابتلاع الصاعد - نموذج انعكاس صاعد قوي",
                        "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                        "author": "Steve Nison",
                        "signal": "bullish_reversal",
                        "reliability": 75,
                        "requirements": "شمعة بيضاء تبتلع الشمعة السوداء السابقة بالكامل"
                    },
                    "bearish_engulfing": {
                        "description": "الابتلاع الهابط - نموذج انعكاس هبوطي قوي",
                        "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                        "author": "Steve Nison",
                        "signal": "bearish_reversal",
                        "reliability": 73
                    }
                },
                "morning_evening_star": {
                    "morning_star": {
                        "description": "نجمة الصباح - نموذج انعكاس صاعد ثلاثي",
                        "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                        "author": "Steve Nison",
                        "signal": "bullish_reversal",
                        "reliability": 78,
                        "pattern": "شمعة سوداء + دوجي/شمعة صغيرة + شمعة بيضاء"
                    },
                    "evening_star": {
                        "description": "نجمة المساء - نموذج انعكاس هبوطي ثلاثي",
                        "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                        "author": "Steve Nison",
                        "signal": "bearish_reversal",
                        "reliability": 76
                    }
                }
            }
        },

        "indicators": {
            "momentum_indicators": {
                "rsi": {
                    "description": "مؤشر القوة النسبية - يقيس زخم السعر وحالات التشبع",
                    "source": "New Concepts in Technical Trading Systems - Welles Wilder",
                    "author": "Welles Wilder",
                    "formula": "RSI = 100 - (100 / (1 + RS))",
                    "overbought": 70,
                    "oversold": 30,
                    "extreme_levels": {"overbought": 80, "oversold": 20},
                    "divergence": "أقوى إشارات RSI تأتي من التباعد مع السعر",
                    "best_timeframe": "يومي وأسبوعي للإشارات الموثوقة",
                    "elder_triple_screen": "يستخدمه Alexander Elder في نظام الشاشات الثلاث"
                },
                "macd": {
                    "description": "مؤشر تقارب وتباعد المتوسطات المتحركة",
                    "source": "Technical Analysis: Power Tools for Active Investors - Gerald Appel",
                    "author": "Gerald Appel",
                    "developer": "Gerald Appel في السبعينيات",
                    "components": {
                        "macd_line": "EMA(12) - EMA(26)",
                        "signal_line": "EMA(9) من خط MACD",
                        "histogram": "MACD Line - Signal Line"
                    },
                    "signals": {
                        "signal_cross": "تقاطع خط MACD مع خط الإشارة",
                        "zero_cross": "تقاطع خط MACD مع الخط الصفري",
                        "divergence": "تباعد MACD مع السعر"
                    },
                    "murphy_analysis": "John Murphy يعتبره من أهم مؤشرات الزخم"
                },
                "stochastic": {
                    "description": "مؤشر العشوائية - يقارن سعر الإغلاق بنطاق التداول",
                    "source": "Technical Analysis of the Financial Markets - John Murphy",
                    "author": "George Lane",
                    "formula": "%K = (Close - Low) / (High - Low) * 100",
                    "settings": {"fast": "5,3", "slow": "14,3"},
                    "overbought": 80,
                    "oversold": 20,
                    "signals": "تقاطع %K مع %D في المناطق المتطرفة"
                },
                "williams_r": {
                    "description": "مؤشر ويليامز %R - مؤشر زخم عكسي",
                    "source": "Long-Term Secrets to Short-Term Trading - Larry Williams",
                    "author": "Larry Williams",
                    "formula": "%R = (Highest High - Close) / (Highest High - Lowest Low) * -100",
                    "overbought": -20,
                    "oversold": -80,
                    "williams_strategy": "Larry Williams يستخدمه للتداول قصير المدى"
                }
            },

            "trend_indicators": {
                "moving_averages": {
                    "sma": {
                        "description": "المتوسط المتحرك البسيط",
                        "source": "Technical Analysis of the Financial Markets - John Murphy",
                        "author": "John Murphy",
                        "calculation": "مجموع الأسعار / عدد الفترات",
                        "common_periods": [20, 50, 100, 200],
                        "golden_cross": "تقاطع MA(50) أعلى MA(200) - إشارة صاعدة قوية",
                        "death_cross": "تقاطع MA(50) أسفل MA(200) - إشارة هابطة قوية"
                    },
                    "ema": {
                        "description": "المتوسط المتحرك الأسي - يعطي وزن أكبر للأسعار الحديثة",
                        "source": "Technical Analysis of the Financial Markets - John Murphy",
                        "author": "John Murphy",
                        "advantage": "أسرع استجابة للتغيرات السعرية",
                        "elder_usage": "Alexander Elder يفضل EMA في استراتيجياته"
                    }
                },
                "bollinger_bands": {
                    "description": "نطاقات بولينجر - تقيس التقلبات والمستويات النسبية",
                    "source": "Bollinger on Bollinger Bands - John Bollinger",
                    "author": "John Bollinger",
                    "components": {
                        "middle_band": "SMA(20)",
                        "upper_band": "SMA(20) + (2 * Standard Deviation)",
                        "lower_band": "SMA(20) - (2 * Standard Deviation)"
                    },
                    "signals": {
                        "squeeze": "انخفاض التقلبات قبل الحركة الكبيرة",
                        "walk_the_bands": "السعر يتحرك على طول النطاق في الاتجاهات القوية",
                        "double_bottom": "قاع مزدوج عند النطاق السفلي"
                    }
                },
                "parabolic_sar": {
                    "description": "نظام الإيقاف والانعكاس المكافئ",
                    "source": "New Concepts in Technical Trading Systems - Welles Wilder",
                    "author": "Welles Wilder",
                    "purpose": "تحديد نقاط الخروج وانعكاس الاتجاه",
                    "signals": "تغيير موقع النقاط من أسفل لأعلى أو العكس"
                }
            },

            "volume_indicators": {
                "obv": {
                    "description": "حجم التوازن - يربط الحجم بحركة السعر",
                    "source": "Technical Analysis of the Financial Markets - John Murphy",
                    "author": "Joe Granville",
                    "principle": "الحجم يسبق السعر",
                    "calculation": "إضافة أو طرح الحجم حسب اتجاه السعر",
                    "divergence": "تباعد OBV مع السعر ينذر بانعكاس"
                },
                "chaikin_money_flow": {
                    "description": "تدفق أموال تشايكين - يقيس ضغط الشراء والبيع",
                    "source": "Technical Analysis of the Financial Markets - John Murphy",
                    "author": "Marc Chaikin",
                    "interpretation": {
                        "positive": "ضغط شراء (إغلاق قريب من الأعلى)",
                        "negative": "ضغط بيع (إغلاق قريب من الأدنى)"
                    }
                }
            }
        },

        "fibonacci_analysis": {
            "retracements": {
                "description": "مستويات تصحيح فيبوناتشي",
                "source": "Fibonacci Trading - Carolyn Boroden",
                "author": "Carolyn Boroden",
                "key_levels": [23.6, 38.2, 50.0, 61.8, 78.6],
                "golden_ratio": "61.8% - النسبة الذهبية الأهم",
                "application": "تحديد مستويات الدعم والمقاومة المحتملة",
                "murphy_view": "John Murphy يعتبرها أداة قوية لتحديد أهداف التصحيح"
            },
            "extensions": {
                "description": "امتدادات فيبوناتشي لتحديد الأهداف",
                "key_levels": [127.2, 161.8, 261.8],
                "usage": "تحديد أهداف الموجات الدافعة"
            }
        },

        "elliott_wave": {
            "description": "نظرية موجات إليوت - تحليل دورات السوق",
            "source": "Elliott Wave Principle - Frost & Prechter",
            "author": "Ralph Nelson Elliott",
            "basic_pattern": "5 موجات دافعة + 3 موجات تصحيحية",
            "impulse_waves": [1, 2, 3, 4, 5],
            "corrective_waves": ["A", "B", "C"],
            "rules": {
                "wave_2": "لا تتجاوز بداية الموجة 1",
                "wave_3": "ليست الأقصر بين الموجات الدافعة",
                "wave_4": "لا تتداخل مع الموجة 1"
            },
            "fibonacci_relationship": "الموجات ترتبط بنسب فيبوناتشي"
        }
    },

    "trading_strategies": {
        "trend_following": {
            "turtle_trading": {
                "description": "استراتيجية السلاحف - نظام تتبع الاتجاه",
                "source": "Market Wizards - Jack Schwager",
                "author": "Richard Dennis & William Eckhardt",
                "entry_rules": "كسر أعلى/أدنى سعر في آخر 20 يوم",
                "exit_rules": "كسر أعلى/أدنى سعر في آخر 10 أيام",
                "position_sizing": "1% من رأس المال لكل وحدة ATR",
                "success_rate": "حقق عوائد استثنائية في الثمانينيات"
            },
            "moving_average_crossover": {
                "description": "استراتيجية تقاطع المتوسطات المتحركة",
                "source": "Technical Analysis of the Financial Markets - John Murphy",
                "author": "John Murphy",
                "simple_system": "تقاطع MA(50) مع MA(200)",
                "advanced_system": "نظام متعدد الإطارات الزمنية",
                "filters": "استخدام مؤشرات إضافية لتقليل الإشارات الخاطئة"
            },
            "breakout_trading": {
                "description": "تداول الاختراقات",
                "source": "Street Smarts - Linda Raschke",
                "author": "Linda Raschke",
                "types": ["اختراق المقاومة", "اختراق الدعم", "اختراق النطاقات"],
                "volume_confirmation": "ضروري لتأكيد صحة الاختراق",
                "false_breakout": "خطر الاختراقات الكاذبة"
            }
        },

        "swing_trading": {
            "elder_triple_screen": {
                "description": "نظام الشاشات الثلاث لألكسندر إلدر",
                "source": "Trading for a Living - Alexander Elder",
                "author": "Alexander Elder",
                "screen_1": "تحديد الاتجاه العام (إطار زمني أطول)",
                "screen_2": "البحث عن إشارات عكسية (إطار زمني أقصر)",
                "screen_3": "تحديد نقطة الدخول الدقيقة",
                "indicators_used": ["MACD", "Force Index", "Stochastic"],
                "philosophy": "التداول مع الاتجاه العام ضد التصحيحات"
            },
            "cup_and_handle": {
                "description": "نموذج الكوب والمقبض",
                "source": "How to Make Money in Stocks - William O'Neil",
                "author": "William O'Neil",
                "formation_time": "7 أسابيع إلى 65 أسبوع للكوب",
                "handle_formation": "1-5 أسابيع للمقبض",
                "volume_pattern": "انخفاض أثناء التكوين، ارتفاع عند الكسر",
                "success_rate": "عالية جداً حسب إحصائيات O'Neil"
            }
        },

        "day_trading": {
            "scalping": {
                "description": "المضاربة السريعة - صفقات قصيرة جداً",
                "source": "Long-Term Secrets to Short-Term Trading - Larry Williams",
                "author": "Larry Williams",
                "timeframe": "ثوانٍ إلى دقائق",
                "profit_target": "نقاط قليلة لكل صفقة",
                "risk_management": "وقف خسارة ضيق جداً"
            },
            "gap_trading": {
                "description": "تداول الفجوات السعرية",
                "source": "Street Smarts - Linda Raschke",
                "author": "Linda Raschke",
                "gap_types": ["فجوة الاختراق", "فجوة الاستمرار", "فجوة الإنهاك"],
                "fade_strategy": "تداول عكس الفجوة",
                "follow_strategy": "تداول مع اتجاه الفجوة"
            }
        },

        "can_slim": {
            "description": "منهجية CAN SLIM لاختيار الأسهم",
            "source": "How to Make Money in Stocks - William O'Neil",
            "author": "William O'Neil",
            "components": {
                "C": "Current Earnings - الأرباح الحالية",
                "A": "Annual Earnings - الأرباح السنوية",
                "N": "New Products/Services - منتجات/خدمات جديدة",
                "S": "Supply and Demand - العرض والطلب",
                "L": "Leader or Laggard - رائد أم متأخر",
                "I": "Institutional Sponsorship - الدعم المؤسسي",
                "M": "Market Direction - اتجاه السوق"
            },
            "success_stories": "استخدمها O'Neil لتحقيق عوائد 5000% في بعض الأسهم"
        }
    },

    "risk_management": {
        "position_sizing": {
            "van_tharp_system": {
                "description": "نظام فان ثارب لحجم المركز",
                "source": "Trade Your Way to Financial Freedom - Van Tharp",
                "author": "Van Tharp",
                "fixed_percentage": "لا تخاطر بأكثر من 1-2% من رأس المال",
                "volatility_based": "اضبط الحجم حسب ATR",
                "kelly_formula": "للحساب الرياضي الأمثل",
                "expectancy": "الربح المتوقع = (احتمالية الربح × متوسط الربح) - (احتمالية الخسارة × متوسط الخسارة)"
            },
            "larry_williams_method": {
                "description": "طريقة لاري ويليامز في تحديد حجم المركز",
                "source": "Long-Term Secrets to Short-Term Trading - Larry Williams",
                "author": "Larry Williams",
                "optimal_f": "النسبة المثلى للمخاطرة",
                "practical_application": "استخدام نسبة أقل من المثلى للأمان"
            }
        },

        "stop_loss_strategies": {
            "atr_based": {
                "description": "وقف الخسارة المبني على ATR",
                "source": "New Concepts in Technical Trading Systems - Welles Wilder",
                "author": "Welles Wilder",
                "calculation": "سعر الدخول ± (2 × ATR)",
                "advantage": "يتكيف مع تقلبات السوق",
                "elder_usage": "Alexander Elder يستخدم 2-3 ATR"
            },
            "percentage_based": {
                "description": "وقف الخسارة بنسبة مئوية ثابتة",
                "source": "Market Wizards - Jack Schwager",
                "author": "متنوع من المتداولين المحترفين",
                "common_percentages": [2, 3, 5, 8],
                "simplicity": "سهل التطبيق والحساب"
            },
            "support_resistance": {
                "description": "وقف الخسارة عند مستويات الدعم والمقاومة",
                "source": "Technical Analysis of Stock Trends - Edwards & Magee",
                "author": "Edwards & Magee",
                "logic": "كسر هذه المستويات يغير التحليل الفني",
                "buffer": "إضافة هامش أمان صغير"
            }
        },

        "money_management": {
            "ralph_vince": {
                "description": "رياضيات إدارة الأموال",
                "source": "The Mathematics of Money Management - Ralph Vince",
                "author": "Ralph Vince",
                "optimal_f": "النسبة المثلى للاستثمار في كل صفقة",
                "geometric_mean": "أهمية المتوسط الهندسي في النمو طويل المدى",
                "drawdown_analysis": "تحليل الانخفاضات المحتملة"
            }
        }
    },

    "trading_psychology": {
        "mark_douglas": {
            "trading_in_zone": {
                "description": "التداول في المنطقة - علم نفس التداول",
                "source": "Trading in the Zone - Mark Douglas",
                "author": "Mark Douglas",
                "key_concepts": {
                    "probability_thinking": "التفكير الاحتمالي",
                    "uncertainty": "قبول عدم اليقين",
                    "consistency": "الاتساق في التطبيق",
                    "beliefs": "المعتقدات تشكل النتائج"
                },
                "fundamental_truths": [
                    "أي شيء يمكن أن يحدث",
                    "لا تحتاج لمعرفة ما سيحدث لتحقق الربح",
                    "هناك توزيع عشوائي بين الأرباح والخسائر",
                    "الميزة تتحقق على سلسلة من الصفقات",
                    "كل لحظة في السوق فريدة"
                ]
            },
            "disciplined_trader": {
                "description": "المتداول المنضبط",
                "source": "The Disciplined Trader - Mark Douglas",
                "author": "Mark Douglas",
                "focus": "بناء العقلية الصحيحة للتداول",
                "emotional_control": "السيطرة على الخوف والطمع"
            }
        },

        "brett_steenbarger": {
            "description": "علم نفس التداول من منظور طبيب نفسي",
            "source": "The Psychology of Trading - Brett Steenbarger",
            "author": "Brett Steenbarger",
            "approach": "تطبيق علم النفس الإكلينيكي على التداول",
            "self_coaching": "تدريب الذات للمتداولين",
            "performance_enhancement": "تحسين الأداء النفسي"
        },

        "common_biases": {
            "confirmation_bias": {
                "description": "تحيز التأكيد - البحث عن معلومات تؤكد معتقداتنا",
                "source": "Market Wizards - Jack Schwager",
                "solution": "البحث عن الأدلة المضادة"
            },
            "loss_aversion": {
                "description": "تجنب الخسارة - الخوف من الخسارة أكثر من الرغبة في الربح",
                "source": "Trading in the Zone - Mark Douglas",
                "impact": "يؤدي لتأخير قطع الخسائر وتعجيل أخذ الأرباح"
            },
            "overconfidence": {
                "description": "الثقة المفرطة بعد سلسلة أرباح",
                "source": "The Psychology of Trading - Brett Steenbarger",
                "danger": "زيادة المخاطرة بشكل غير مبرر"
            }
        }
    }
}

# بيانات السوق المحاكاة
MARKET_DATA = {
    'EURUSD': {'price': 1.0850, 'change': 0.0012, 'volume': 1250000, 'trend': 'bullish'},
    'GBPUSD': {'price': 1.2650, 'change': -0.0025, 'volume': 980000, 'trend': 'bearish'},
    'USDJPY': {'price': 149.85, 'change': 0.45, 'volume': 1100000, 'trend': 'bullish'},
    'XAUUSD': {'price': 2035.50, 'change': 12.30, 'volume': 850000, 'trend': 'bullish'},
    'BTCUSD': {'price': 43250.80, 'change': 850.25, 'volume': 2500000, 'trend': 'bullish'},
    'AAPL': {'price': 195.50, 'change': -2.30, 'volume': 45000000, 'trend': 'bearish'},
    'TSLA': {'price': 248.50, 'change': 5.80, 'volume': 38000000, 'trend': 'bullish'}
}

class TradingAI:
    def __init__(self):
        self.knowledge_base = TRADING_KNOWLEDGE
        self.market_data = MARKET_DATA
    
    def analyze_symbol(self, symbol):
        """تحليل شامل لرمز مالي"""
        if symbol not in self.market_data:
            return {"error": "الرمز غير موجود"}
        
        data = self.market_data[symbol]
        analysis = {
            "symbol": symbol,
            "current_price": data['price'],
            "change": data['change'],
            "change_percent": (data['change'] / data['price']) * 100,
            "volume": data['volume'],
            "trend": data['trend']
        }
        
        # تحليل فني
        technical_analysis = self._technical_analysis(symbol, data)
        
        # تحليل أساسي (للأسهم)
        fundamental_analysis = self._fundamental_analysis(symbol, data)
        
        # إدارة المخاطر
        risk_management = self._risk_management(symbol, data)
        
        # توصية نهائية
        recommendation = self._generate_recommendation(technical_analysis, fundamental_analysis, risk_management)
        
        return {
            "analysis": analysis,
            "technical": technical_analysis,
            "fundamental": fundamental_analysis,
            "risk": risk_management,
            "recommendation": recommendation,
            "educational_note": self._get_educational_note(symbol)
        }
    
    def _technical_analysis(self, symbol, data):
        """التحليل الفني"""
        # محاكاة مؤشرات فنية
        rsi = random.randint(20, 80)
        macd_signal = random.choice(['bullish', 'bearish', 'neutral'])
        
        patterns = []
        if rsi > 70:
            patterns.append({
                "pattern": "RSI Overbought",
                "description": self.knowledge_base['technical_analysis']['indicators']['momentum_indicators']['rsi']['description'],
                "signal": "bearish",
                "source": self.knowledge_base['technical_analysis']['indicators']['momentum_indicators']['rsi']['source']
            })
        elif rsi < 30:
            patterns.append({
                "pattern": "RSI Oversold",
                "description": self.knowledge_base['technical_analysis']['indicators']['momentum_indicators']['rsi']['description'],
                "signal": "bullish",
                "source": self.knowledge_base['technical_analysis']['indicators']['momentum_indicators']['rsi']['source']
            })
        
        # إضافة نماذج عشوائية للتوضيح
        if random.random() > 0.7:
            pattern_key = random.choice(['head_shoulders', 'double_top'])
            if pattern_key == 'head_shoulders':
                pattern_info = self.knowledge_base['technical_analysis']['chart_patterns']['head_shoulders']
            else:
                pattern_info = self.knowledge_base['technical_analysis']['chart_patterns']['double_top']
            patterns.append({
                "pattern": pattern_key.replace('_', ' ').title(),
                "description": pattern_info['description'],
                "signal": pattern_info['signal'],
                "reliability": pattern_info['reliability'],
                "source": pattern_info['source'],
                "entry_strategy": pattern_info['entry_strategy'],
                "stop_loss": pattern_info['stop_loss'],
                "target": pattern_info['target']
            })
        
        return {
            "rsi": rsi,
            "macd_signal": macd_signal,
            "patterns": patterns,
            "trend_strength": random.choice(['قوي', 'متوسط', 'ضعيف']),
            "support_level": data['price'] * 0.98,
            "resistance_level": data['price'] * 1.02
        }
    
    def _fundamental_analysis(self, symbol, data):
        """التحليل الأساسي"""
        if symbol in ['AAPL', 'TSLA', 'GOOGL', 'MSFT']:
            # للأسهم
            pe_ratio = random.uniform(15, 35)
            pb_ratio = random.uniform(1, 5)
            
            value_assessment = "مقيم بعدالة"
            if pe_ratio < 15:
                value_assessment = "مقيم بأقل من قيمته"
            elif pe_ratio > 25:
                value_assessment = "مقيم بأكثر من قيمته"
            
            return {
                "pe_ratio": round(pe_ratio, 2),
                "pb_ratio": round(pb_ratio, 2),
                "value_assessment": value_assessment,
                "graham_analysis": "تحليل بنيامين جراهام للقيمة",
                "recommendation": "اشتري" if pe_ratio < 20 else "احتفظ" if pe_ratio < 25 else "بع"
            }
        else:
            # للعملات والسلع
            return {
                "economic_factors": "تحليل العوامل الاقتصادية",
                "central_bank_policy": "سياسة البنك المركزي",
                "geopolitical_events": "الأحداث الجيوسياسية",
                "note": "التحليل الأساسي للعملات يتطلب متابعة الأخبار الاقتصادية"
            }
    
    def _risk_management(self, symbol, data):
        """إدارة المخاطر"""
        position_size = self.knowledge_base['risk_management']['position_sizing']['van_tharp_system']
        stop_loss_info = self.knowledge_base['risk_management']['stop_loss_strategies']['atr_based']
        
        # حساب وقف الخسارة المقترح
        atr_stop = data['price'] * 0.02  # 2% ATR محاكاة
        percentage_stop = data['price'] * 0.015  # 1.5%
        
        return {
            "position_sizing": {
                "description": position_size['description'],
                "source": position_size['source'],
                "recommended_risk": "2% من رأس المال",
                "kelly_criterion": "استخدم معادلة كيلي للحساب الدقيق"
            },
            "stop_loss": {
                "atr_based": round(atr_stop, 4),
                "percentage_based": round(percentage_stop, 4),
                "recommendation": stop_loss_info['description'],
                "source": stop_loss_info['source']
            },
            "risk_reward_ratio": "1:2 كحد أدنى",
            "max_positions": "لا تفتح أكثر من 3-5 مراكز في نفس الوقت"
        }
    
    def _generate_recommendation(self, technical, fundamental, risk):
        """توليد التوصية النهائية"""
        signals = []
        
        # إشارات فنية
        if technical['rsi'] > 70:
            signals.append('bearish')
        elif technical['rsi'] < 30:
            signals.append('bullish')
        
        for pattern in technical['patterns']:
            if pattern['signal'] in ['bullish', 'bullish_reversal']:
                signals.append('bullish')
            elif pattern['signal'] in ['bearish', 'bearish_reversal']:
                signals.append('bearish')
        
        # حساب التوصية
        bullish_count = signals.count('bullish')
        bearish_count = signals.count('bearish')
        
        if bullish_count > bearish_count:
            action = "شراء"
            confidence = min(90, 60 + (bullish_count - bearish_count) * 10)
        elif bearish_count > bullish_count:
            action = "بيع"
            confidence = min(90, 60 + (bearish_count - bullish_count) * 10)
        else:
            action = "انتظار"
            confidence = 50
        
        return {
            "action": action,
            "confidence": f"{confidence}%",
            "reasoning": f"بناءً على {len(signals)} إشارة فنية",
            "timeframe": "قصير إلى متوسط المدى",
            "risk_level": "متوسط",
            "educational_quote": self._get_wisdom_quote()
        }
    
    def _get_educational_note(self, symbol):
        """ملاحظة تعليمية"""
        notes = [
            "تذكر: 'الاتجاه صديقك' - Jesse Livermore",
            "لا تضع كل بيضك في سلة واحدة - التنويع مهم",
            "خطط لتداولك، وتداول حسب خطتك",
            "أهم شيء في التداول هو الحفاظ على رأس المال",
            "السوق دائماً على حق - لا تجادله"
        ]
        return random.choice(notes)
    
    def _get_wisdom_quote(self):
        """حكم وأقوال من أساطير التداول"""
        quotes = [
            "أي شيء يمكن أن يحدث في السوق - Mark Douglas",
            "لا تحتاج لمعرفة ما سيحدث لتحقق الربح - Mark Douglas",
            "الاتجاه صديقك حتى ينتهي - Jesse Livermore",
            "قطع خسائرك واترك أرباحك تجري - قاعدة ذهبية",
            "أهم شيء في التداول هو الحفاظ على رأس المال - Van Tharp",
            "السوق دائماً على حق - لا تجادله - Jesse Livermore",
            "التداول الناجح هو 80% علم نفس و20% مهارة - Mark Douglas",
            "لا تضع كل بيضك في سلة واحدة - التنويع مهم - Benjamin Graham",
            "السوق يمكن أن يبقى غير منطقي أكثر مما تستطيع البقاء مفلساً - John Keynes",
            "أفضل استراتيجية هي التي تناسب شخصيتك - Jack Schwager",
            "الحجم يسبق السعر - Joe Granville",
            "التحليل الفني يدرس تأثير العرض والطلب على الأسعار - John Murphy",
            "الشموع اليابانية تكشف علم نفس السوق - Steve Nison",
            "تحديد حجم المركز أهم من اختيار الأسهم - Van Tharp",
            "الانضباط هو مفتاح النجاح في التداول - Alexander Elder",
            "ابحث عن الأسهم الرائدة في القطاعات الرائدة - William O'Neil",
            "المؤشرات تتبع السعر، لا تقوده - John Murphy",
            "التداول مهنة تتطلب التعلم المستمر - Larry Williams",
            "أفضل الصفقات تبدو مخيفة في البداية - Peter Lynch",
            "لا تتداول بأموال لا تستطيع تحمل خسارتها - قاعدة ذهبية"
        ]
        return random.choice(quotes)

    def answer_question(self, question):
        """الإجابة على أسئلة المستخدم بناءً على معرفة أشهر خبراء التداول"""
        question_lower = question.lower()

        # تحليل السؤال وإيجاد الإجابة المناسبة من قاعدة المعرفة الشاملة
        if any(word in question_lower for word in ['rsi', 'مؤشر القوة النسبية']):
            rsi_info = self.knowledge_base['technical_analysis']['indicators']['momentum_indicators']['rsi']
            return {
                "answer": rsi_info['description'],
                "source": rsi_info['source'],
                "author": f"طوره {rsi_info['author']} - أحد رواد التحليل الفني",
                "practical_tip": f"استخدم RSI > {rsi_info['overbought']} كإشارة بيع و RSI < {rsi_info['oversold']} كإشارة شراء. للإشارات المتطرفة استخدم {rsi_info['extreme_levels']['overbought']} و {rsi_info['extreme_levels']['oversold']}",
                "advanced_usage": rsi_info['divergence'],
                "expert_opinion": rsi_info['elder_triple_screen'],
                "book_reference": rsi_info['source']
            }

        elif any(word in question_lower for word in ['macd', 'تقارب', 'تباعد']):
            macd_info = self.knowledge_base['technical_analysis']['indicators']['momentum_indicators']['macd']
            return {
                "answer": macd_info['description'],
                "source": macd_info['source'],
                "developer": f"طوره {macd_info['author']} - {macd_info['developer']}",
                "components": [
                    f"خط MACD: {macd_info['components']['macd_line']}",
                    f"خط الإشارة: {macd_info['components']['signal_line']}",
                    f"الهيستوجرام: {macd_info['components']['histogram']}"
                ],
                "signals": [
                    macd_info['signals']['signal_cross'],
                    macd_info['signals']['zero_cross'],
                    macd_info['signals']['divergence']
                ],
                "expert_opinion": macd_info['murphy_analysis'],
                "book_reference": macd_info['source']
            }

        elif any(word in question_lower for word in ['وقف الخسارة', 'stop loss', 'ستوب']):
            stop_strategies = self.knowledge_base['risk_management']['stop_loss_strategies']
            return {
                "answer": "وقف الخسارة أهم أداة لحماية رأس المال - كما يؤكد جميع الخبراء",
                "strategies": [
                    {
                        "type": "مبني على ATR",
                        "description": stop_strategies['atr_based']['description'],
                        "author": stop_strategies['atr_based']['author'],
                        "calculation": stop_strategies['atr_based']['calculation'],
                        "advantage": stop_strategies['atr_based']['advantage'],
                        "elder_usage": stop_strategies['atr_based']['elder_usage']
                    },
                    {
                        "type": "نسبة مئوية ثابتة",
                        "description": stop_strategies['percentage_based']['description'],
                        "common_percentages": stop_strategies['percentage_based']['common_percentages'],
                        "simplicity": stop_strategies['percentage_based']['simplicity']
                    },
                    {
                        "type": "عند الدعم والمقاومة",
                        "description": stop_strategies['support_resistance']['description'],
                        "logic": stop_strategies['support_resistance']['logic'],
                        "buffer": stop_strategies['support_resistance']['buffer']
                    }
                ],
                "expert_sources": [
                    stop_strategies['atr_based']['source'],
                    stop_strategies['percentage_based']['source'],
                    stop_strategies['support_resistance']['source']
                ]
            }

        elif any(word in question_lower for word in ['حجم المركز', 'position size', 'كم أشتري']):
            van_tharp = self.knowledge_base['risk_management']['position_sizing']['van_tharp_system']
            larry_williams = self.knowledge_base['risk_management']['position_sizing']['larry_williams_method']
            return {
                "answer": "تحديد حجم المركز أهم من اختيار الأسهم نفسها - Van Tharp",
                "van_tharp_method": {
                    "description": van_tharp['description'],
                    "fixed_percentage": van_tharp['fixed_percentage'],
                    "volatility_based": van_tharp['volatility_based'],
                    "kelly_formula": van_tharp['kelly_formula'],
                    "expectancy": van_tharp['expectancy']
                },
                "larry_williams_method": {
                    "description": larry_williams['description'],
                    "optimal_f": larry_williams['optimal_f'],
                    "practical_application": larry_williams['practical_application']
                },
                "expert_sources": [van_tharp['source'], larry_williams['source']],
                "key_principle": "لا تخاطر بأكثر من 1-2% من رأس المال في صفقة واحدة"
            }

        elif any(word in question_lower for word in ['خوف', 'طمع', 'نفسية', 'عواطف', 'psychology']):
            douglas_zone = self.knowledge_base['trading_psychology']['mark_douglas']['trading_in_zone']
            steenbarger = self.knowledge_base['trading_psychology']['brett_steenbarger']
            biases = self.knowledge_base['trading_psychology']['common_biases']

            return {
                "answer": "علم نفس التداول أهم من التحليل الفني نفسه - Mark Douglas",
                "mark_douglas_insights": {
                    "description": douglas_zone['description'],
                    "key_concepts": douglas_zone['key_concepts'],
                    "fundamental_truths": douglas_zone['fundamental_truths']
                },
                "brett_steenbarger_approach": {
                    "description": steenbarger['description'],
                    "approach": steenbarger['approach'],
                    "self_coaching": steenbarger['self_coaching']
                },
                "common_psychological_traps": [
                    {
                        "bias": "تحيز التأكيد",
                        "description": biases['confirmation_bias']['description'],
                        "solution": biases['confirmation_bias']['solution']
                    },
                    {
                        "bias": "تجنب الخسارة",
                        "description": biases['loss_aversion']['description'],
                        "impact": biases['loss_aversion']['impact']
                    },
                    {
                        "bias": "الثقة المفرطة",
                        "description": biases['overconfidence']['description'],
                        "danger": biases['overconfidence']['danger']
                    }
                ],
                "expert_sources": [douglas_zone['source'], steenbarger['source']],
                "practical_advice": "طور خطة تداول واتبعها بانضباط - لا تدع العواطف تتحكم في قراراتك"
            }

        elif any(word in question_lower for word in ['انضباط', 'discipline', 'قواعد']):
            return {
                "answer": self.knowledge_base['psychology']['emotions']['discipline']['description'],
                "source": self.knowledge_base['psychology']['emotions']['discipline']['source'],
                "rules": self.knowledge_base['psychology']['emotions']['discipline']['rules'],
                "journal": self.knowledge_base['psychology']['emotions']['discipline']['journal'],
                "review": self.knowledge_base['psychology']['emotions']['discipline']['review']
            }

        elif any(word in question_lower for word in ['رأس كتف', 'head shoulder', 'نماذج']):
            pattern_info = self.knowledge_base['technical_analysis']['chart_patterns']['head_shoulders']
            return {
                "answer": pattern_info['description'],
                "source": pattern_info['source'],
                "author": f"من كتاب {pattern_info['author']} الكلاسيكي",
                "signal": pattern_info['signal'],
                "reliability": f"{pattern_info['reliability']}% موثوقية",
                "volume_importance": pattern_info['volume_confirmation'],
                "entry_strategy": pattern_info['entry_strategy'],
                "stop_loss": pattern_info['stop_loss'],
                "target": pattern_info['target'],
                "bulkowski_research": pattern_info['bulkowski_stats'],
                "book_reference": pattern_info['source']
            }

        elif any(word in question_lower for word in ['شموع يابانية', 'candlestick', 'مطرقة', 'hammer']):
            if 'مطرقة' in question_lower or 'hammer' in question_lower:
                hammer_info = self.knowledge_base['technical_analysis']['candlestick_patterns']['single_candles']['hammer']
                return {
                    "answer": hammer_info['description'],
                    "source": hammer_info['source'],
                    "author": f"من عمل {hammer_info['author']} الرائد في الشموع اليابانية",
                    "signal": hammer_info['signal'],
                    "reliability": f"{hammer_info['reliability']}% موثوقية",
                    "requirements": hammer_info['requirements'],
                    "confirmation": hammer_info['confirmation'],
                    "psychology": hammer_info['psychology'],
                    "book_reference": hammer_info['source']
                }
            else:
                return {
                    "answer": "الشموع اليابانية هي طريقة لعرض حركة الأسعار طورها اليابانيون منذ مئات السنين",
                    "source": "Japanese Candlestick Charting Techniques - Steve Nison",
                    "author": "Steve Nison - الذي قدم الشموع اليابانية للعالم الغربي",
                    "main_patterns": [
                        "المطرقة (Hammer) - انعكاس صاعد",
                        "النجمة الساقطة (Shooting Star) - انعكاس هبوطي",
                        "الدوجي (Doji) - تردد وعدم يقين",
                        "الابتلاع (Engulfing) - انعكاس قوي",
                        "نجمة الصباح/المساء - انعكاس ثلاثي"
                    ],
                    "advantage": "تظهر علم نفس السوق بوضوح أكثر من الرسوم البيانية التقليدية",
                    "book_reference": "Japanese Candlestick Charting Techniques - Steve Nison"
                }

        elif any(word in question_lower for word in ['متوسط متحرك', 'moving average', 'golden cross']):
            ma_info = self.knowledge_base['technical_analysis']['indicators']['trend_indicators']['moving_averages']
            return {
                "answer": "المتوسطات المتحركة من أهم أدوات التحليل الفني لتحديد الاتجاه",
                "source": ma_info['sma']['source'],
                "author": f"شرحها بالتفصيل {ma_info['sma']['author']}",
                "types": [
                    f"البسيط (SMA): {ma_info['sma']['calculation']}",
                    f"الأسي (EMA): {ma_info['ema']['description']}"
                ],
                "common_periods": ma_info['sma']['common_periods'],
                "golden_cross": ma_info['sma']['golden_cross'],
                "death_cross": ma_info['sma']['death_cross'],
                "ema_advantage": ma_info['ema']['advantage'],
                "elder_preference": ma_info['ema']['elder_usage'],
                "book_reference": ma_info['sma']['source']
            }

        elif any(word in question_lower for word in ['فيبوناتشي', 'fibonacci']):
            fib_info = self.knowledge_base['technical_analysis']['fibonacci_analysis']['retracements']
            return {
                "answer": fib_info['description'],
                "source": fib_info['source'],
                "author": f"متخصصة فيه {fib_info['author']}",
                "key_levels": fib_info['key_levels'],
                "golden_ratio": fib_info['golden_ratio'],
                "application": fib_info['application'],
                "murphy_opinion": fib_info['murphy_view'],
                "extensions": self.knowledge_base['technical_analysis']['fibonacci_analysis']['extensions']['description'],
                "book_reference": fib_info['source']
            }

        elif any(word in question_lower for word in ['قيمة', 'pe ratio', 'تقييم']):
            return {
                "answer": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio']['description'],
                "source": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio']['source'],
                "low_pe": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio']['low_pe'],
                "high_pe": self.knowledge_base['fundamental_analysis']['value_investing']['pe_ratio']['high_pe'],
                "book_reference": "The Intelligent Investor - Benjamin Graham"
            }

        elif any(word in question_lower for word in ['استراتيجية', 'strategy', 'خطة', 'turtle', 'سلاحف']):
            if any(word in question_lower for word in ['turtle', 'سلاحف']):
                turtle = self.knowledge_base['trading_strategies']['trend_following']['turtle_trading']
                return {
                    "answer": turtle['description'],
                    "source": turtle['source'],
                    "creators": turtle['author'],
                    "entry_rules": turtle['entry_rules'],
                    "exit_rules": turtle['exit_rules'],
                    "position_sizing": turtle['position_sizing'],
                    "success_story": turtle['success_rate'],
                    "book_reference": turtle['source']
                }
            elif any(word in question_lower for word in ['can slim', 'oneil', 'أونيل']):
                can_slim = self.knowledge_base['trading_strategies']['can_slim']
                return {
                    "answer": can_slim['description'],
                    "source": can_slim['source'],
                    "author": can_slim['author'],
                    "components": can_slim['components'],
                    "success_stories": can_slim['success_stories'],
                    "book_reference": can_slim['source']
                }
            elif any(word in question_lower for word in ['elder', 'إلدر', 'triple screen', 'شاشات ثلاث']):
                elder = self.knowledge_base['trading_strategies']['swing_trading']['elder_triple_screen']
                return {
                    "answer": elder['description'],
                    "source": elder['source'],
                    "author": elder['author'],
                    "screen_1": elder['screen_1'],
                    "screen_2": elder['screen_2'],
                    "screen_3": elder['screen_3'],
                    "indicators_used": elder['indicators_used'],
                    "philosophy": elder['philosophy'],
                    "book_reference": elder['source']
                }
            else:
                return {
                    "answer": "أفضل الاستراتيجيات من أساطير التداول",
                    "famous_strategies": [
                        "استراتيجية السلاحف (Turtle Trading) - Richard Dennis",
                        "نظام الشاشات الثلاث - Alexander Elder",
                        "منهجية CAN SLIM - William O'Neil",
                        "تداول الاختراقات - Linda Raschke",
                        "تتبع الاتجاه بالمتوسطات المتحركة - John Murphy"
                    ],
                    "key_components": [
                        "تحديد الاتجاه العام للسوق",
                        "البحث عن نقاط دخول عالية الاحتمالية",
                        "وضع وقف خسارة مناسب",
                        "تحديد أهداف ربح واقعية",
                        "إدارة حجم المركز بعناية"
                    ],
                    "expert_advice": "أفضل استراتيجية هي التي تناسب شخصيتك ونمط حياتك - Jack Schwager",
                    "book_reference": "Market Wizards - Jack Schwager"
                }

        else:
            # إجابة عامة مع عرض الخبراء والمصادر
            return {
                "answer": "أهلاً بك في مستشار التداول الذكي! أنا مدعوم بمعرفة أشهر خبراء التداول العالميين",
                "expert_authors": [
                    "John J. Murphy - خبير التحليل الفني الشامل",
                    "Thomas Bulkowski - متخصص أنماط الرسم البياني",
                    "Jack D. Schwager - مؤلف سلسلة Market Wizards",
                    "Alexander Elder - خبير علم نفس التداول",
                    "Steve Nison - رائد الشموع اليابانية",
                    "Larry Williams - خبير المؤشرات والتداول قصير المدى",
                    "William O'Neil - مطور منهجية CAN SLIM",
                    "Mark Douglas - خبير علم نفس التداول",
                    "Van Tharp - خبير إدارة المخاطر"
                ],
                "topics": [
                    "التحليل الفني (أنماط الرسم البياني، المؤشرات، الشموع اليابانية)",
                    "استراتيجيات التداول (السلاحف، CAN SLIM، الشاشات الثلاث)",
                    "إدارة المخاطر (وقف الخسارة، حجم المركز، إدارة الأموال)",
                    "علم نفس التداول (التحكم في العواطف، التحيزات النفسية)",
                    "مؤشرات فيبوناتشي وموجات إليوت",
                    "تحليل أي رمز مالي بناءً على خبرة الأساطير"
                ],
                "suggestion": "اسأل عن أي موضوع مثل: 'ما هو مؤشر RSI؟' أو 'شرح استراتيجية السلاحف' أو 'كيف أتحكم في عواطف التداول؟'",
                "wisdom": self._get_wisdom_quote(),
                "knowledge_base": "مدعوم بأكثر من 50 كتاب ومرجع من أشهر خبراء التداول العالميين"
            }

# إنشاء مثيل من الذكاء الاصطناعي
trading_ai = TradingAI()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/analyze/<symbol>')
def analyze_symbol(symbol):
    """تحليل رمز مالي"""
    analysis = trading_ai.analyze_symbol(symbol.upper())
    return jsonify(analysis)

@app.route('/api/ask', methods=['POST'])
def ask_ai():
    """سؤال الذكاء الاصطناعي"""
    data = request.get_json()
    question = data.get('question', '')
    
    # محاكاة إجابة ذكية
    response = trading_ai.answer_question(question)
    return jsonify(response)

@app.route('/api/symbols')
def get_symbols():
    """قائمة الرموز المتاحة"""
    return jsonify(list(MARKET_DATA.keys()))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
