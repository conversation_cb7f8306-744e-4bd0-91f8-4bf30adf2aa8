# =====================================================
# M&M AI Trading System - Docker Compose Configuration
# نظام التداول الذكي المتكامل - تكوين Docker Compose
# =====================================================

version: '3.8'

services:
  # =====================================================
  # Database Services
  # =====================================================
  
  # PostgreSQL - Main relational database
  postgres:
    image: postgres:15-alpine
    container_name: mmai_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: mmai_trading
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_123}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./databases/postgresql/advanced_schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./databases/postgresql/indexes.sql:/docker-entrypoint-initdb.d/02-indexes.sql
      - ./databases/postgresql/functions.sql:/docker-entrypoint-initdb.d/03-functions.sql
    ports:
      - "5432:5432"
    networks:
      - mmai_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d mmai_trading"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB - Document database for unstructured data
  mongodb:
    image: mongo:7.0
    container_name: mmai_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USER:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-secure_password_123}
      MONGO_INITDB_DATABASE: mmai_trading
    volumes:
      - mongodb_data:/data/db
      - ./databases/mongodb/advanced_setup.js:/docker-entrypoint-initdb.d/setup.js
    ports:
      - "27017:27017"
    networks:
      - mmai_network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # InfluxDB - Time series database
  influxdb:
    image: influxdb:2.7-alpine
    container_name: mmai_influxdb
    restart: unless-stopped
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: ${INFLUX_USER:-admin}
      DOCKER_INFLUXDB_INIT_PASSWORD: ${INFLUX_PASSWORD:-secure_password_123}
      DOCKER_INFLUXDB_INIT_ORG: mmai
      DOCKER_INFLUXDB_INIT_BUCKET: trading_data
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: ${INFLUX_TOKEN:-mmai_super_secret_token_12345}
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    ports:
      - "8086:8086"
    networks:
      - mmai_network
    healthcheck:
      test: ["CMD", "influx", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis - In-memory cache and message broker
  redis:
    image: redis:7.2-alpine
    container_name: mmai_redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-secure_password_123} --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - mmai_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =====================================================
  # Message Queue Services
  # =====================================================

  # RabbitMQ - Message broker
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: mmai_rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-admin}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-secure_password_123}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "5672:5672"   # AMQP port
      - "15672:15672" # Management UI
    networks:
      - mmai_network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =====================================================
  # Monitoring Services
  # =====================================================

  # Prometheus - Metrics collection
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: mmai_prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - mmai_network

  # Grafana - Metrics visualization
  grafana:
    image: grafana/grafana:10.1.0
    container_name: mmai_grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-secure_password_123}
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3000:3000"
    networks:
      - mmai_network
    depends_on:
      - prometheus

  # =====================================================
  # Application Services
  # =====================================================

  # C++ High-Performance Engine
  cpp_engine:
    build:
      context: ./high_performance/cpp_engine
      dockerfile: Dockerfile
    container_name: mmai_cpp_engine
    restart: unless-stopped
    environment:
      - LOG_LEVEL=INFO
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=mmai_trading
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password_123}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-secure_password_123}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    ports:
      - "8001:8001"
    networks:
      - mmai_network
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Python AI/ML Services
  python_ai:
    build:
      context: .
      dockerfile: Dockerfile.python
    container_name: mmai_python_ai
    restart: unless-stopped
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=mmai_trading
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password_123}
      - MONGODB_HOST=mongodb
      - MONGODB_PORT=27017
      - MONGODB_DB=mmai_trading
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-secure_password_123}
      - INFLUX_URL=http://influxdb:8086
      - INFLUX_TOKEN=${INFLUX_TOKEN:-mmai_super_secret_token_12345}
      - INFLUX_ORG=mmai
      - INFLUX_BUCKET=trading_data
    volumes:
      - ./models:/app/models
      - ./data:/app/data
      - ./logs:/app/logs
      - ./cache:/app/cache
    ports:
      - "8000:8000"
    networks:
      - mmai_network
    depends_on:
      - postgres
      - mongodb
      - influxdb
      - redis
      - cpp_engine
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # React Frontend
  frontend:
    build:
      context: ./frontend/react_dashboard
      dockerfile: Dockerfile
      args:
        - REACT_APP_API_URL=http://localhost:8000
        - REACT_APP_WS_URL=ws://localhost:8000/ws
    container_name: mmai_frontend
    restart: unless-stopped
    ports:
      - "3001:80"
    networks:
      - mmai_network
    depends_on:
      - python_ai
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:1.25-alpine
    container_name: mmai_nginx
    restart: unless-stopped
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - mmai_network
    depends_on:
      - frontend
      - python_ai
      - cpp_engine
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =====================================================
  # Data Processing Services
  # =====================================================

  # Celery Worker for background tasks
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile.python
    container_name: mmai_celery_worker
    restart: unless-stopped
    command: celery -A intelligent_trading_system.celery_app worker --loglevel=info --concurrency=4
    environment:
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-secure_password_123}@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-secure_password_123}@redis:6379/2
    volumes:
      - ./models:/app/models
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - mmai_network
    depends_on:
      - redis
      - postgres
      - mongodb

  # Celery Beat for scheduled tasks
  celery_beat:
    build:
      context: .
      dockerfile: Dockerfile.python
    container_name: mmai_celery_beat
    restart: unless-stopped
    command: celery -A intelligent_trading_system.celery_app beat --loglevel=info
    environment:
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-secure_password_123}@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-secure_password_123}@redis:6379/2
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - mmai_network
    depends_on:
      - redis
      - celery_worker

  # Jupyter Notebook for development and analysis
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile.jupyter
    container_name: mmai_jupyter
    restart: unless-stopped
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=${JUPYTER_TOKEN:-mmai_jupyter_token_123}
    volumes:
      - ./notebooks:/home/<USER>/work
      - ./data:/home/<USER>/data
      - ./models:/home/<USER>/models
    ports:
      - "8888:8888"
    networks:
      - mmai_network
    depends_on:
      - postgres
      - mongodb
      - influxdb

# =====================================================
# Networks
# =====================================================

networks:
  mmai_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =====================================================
# Volumes
# =====================================================

volumes:
  postgres_data:
    driver: local
  mongodb_data:
    driver: local
  influxdb_data:
    driver: local
  influxdb_config:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
