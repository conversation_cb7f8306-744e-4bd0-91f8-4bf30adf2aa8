#!/usr/bin/env python3
"""
🤖 AI Trading Chatbot
روبوت المحادثة الذكي للتداول

المرحلة 8: تطوير واجهة الدردشة الذكية
- دمج قاعدة المعرفة مع التحليل الفني
- الإجابة على أسئلة التداول
- تقديم توصيات ذكية
- دعم اللغة العربية والإنجليزية
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional
import logging

# إضافة مسار المجلدات الأخرى
sys.path.append('../ai_engines')
sys.path.append('../data_ingestion')

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AITradingChatbot:
    """روبوت المحادثة الذكي للتداول"""
    
    def __init__(self):
        """تهيئة الروبوت"""
        self.name = "M&M AI Trading Assistant"
        self.version = "1.0.0"
        self.conversation_history = []
        self.user_preferences = {}
        
        # تحميل قاعدة المعرفة
        try:
            from knowledge_base_engine import TradingKnowledgeBase
            self.knowledge_base = TradingKnowledgeBase()
            logger.info("✅ Knowledge base loaded")
        except Exception as e:
            logger.warning(f"⚠️ Knowledge base not available: {str(e)}")
            self.knowledge_base = None
        
        # تحميل محرك التحليل
        self.analysis_engine = None
        try:
            # محاولة تحميل محرك التحليل
            self.load_analysis_engine()
        except Exception as e:
            logger.warning(f"⚠️ Analysis engine not available: {str(e)}")
    
    def load_analysis_engine(self):
        """تحميل محرك التحليل الفني"""
        # محاكاة محرك التحليل
        self.analysis_engine = {
            'available': True,
            'last_analysis': {
                'AAPL': {
                    'price': 198.25,
                    'signal': 'HOLD',
                    'confidence': 65,
                    'rsi': 45.2
                },
                'GOLD': {
                    'price': 3296.60,
                    'signal': 'BUY',
                    'confidence': 75,
                    'rsi': 28.5
                }
            }
        }
    
    def detect_intent(self, message: str) -> str:
        """تحديد نية المستخدم"""
        message_lower = message.lower()
        
        # نوايا مختلفة
        if any(word in message_lower for word in ['سعر', 'price', 'كم', 'how much']):
            return 'price_inquiry'
        elif any(word in message_lower for word in ['تحليل', 'analysis', 'analyze']):
            return 'analysis_request'
        elif any(word in message_lower for word in ['شراء', 'buy', 'بيع', 'sell', 'توصية', 'recommendation']):
            return 'trading_advice'
        elif any(word in message_lower for word in ['rsi', 'macd', 'مؤشر', 'indicator']):
            return 'technical_question'
        elif any(word in message_lower for word in ['ذهب', 'gold', 'xau']):
            return 'gold_inquiry'
        elif any(word in message_lower for word in ['مخاطر', 'risk', 'خسارة', 'loss']):
            return 'risk_management'
        elif any(word in message_lower for word in ['تعلم', 'learn', 'كيف', 'how']):
            return 'educational'
        elif any(word in message_lower for word in ['مرحبا', 'hello', 'hi', 'السلام']):
            return 'greeting'
        else:
            return 'general_question'
    
    def extract_symbol(self, message: str) -> Optional[str]:
        """استخراج رمز الأصل من الرسالة"""
        message_upper = message.upper()
        
        # رموز شائعة
        symbols = {
            'AAPL': ['AAPL', 'APPLE', 'أبل'],
            'GOLD': ['GOLD', 'XAU', 'ذهب', 'الذهب'],
            'GOOGL': ['GOOGL', 'GOOGLE', 'جوجل'],
            'MSFT': ['MSFT', 'MICROSOFT', 'مايكروسوفت'],
            'TSLA': ['TSLA', 'TESLA', 'تسلا']
        }
        
        for symbol, keywords in symbols.items():
            if any(keyword in message_upper for keyword in keywords):
                return symbol
        
        return None
    
    def get_price_info(self, symbol: str) -> str:
        """الحصول على معلومات السعر"""
        if self.analysis_engine and symbol in self.analysis_engine['last_analysis']:
            data = self.analysis_engine['last_analysis'][symbol]
            return f"💰 سعر {symbol} الحالي: ${data['price']:.2f}"
        else:
            return f"عذراً، لا تتوفر بيانات السعر لـ {symbol} حالياً."
    
    def get_analysis(self, symbol: str) -> str:
        """الحصول على التحليل الفني"""
        if self.analysis_engine and symbol in self.analysis_engine['last_analysis']:
            data = self.analysis_engine['last_analysis'][symbol]
            
            analysis = f"""📊 تحليل {symbol}:
💰 السعر الحالي: ${data['price']:.2f}
🚨 الإشارة: {data['signal']}
🎯 مستوى الثقة: {data['confidence']}%
📈 RSI: {data['rsi']}

"""
            
            # إضافة توصية
            if data['signal'] == 'BUY':
                analysis += "✅ التوصية: فرصة شراء جيدة"
            elif data['signal'] == 'SELL':
                analysis += "❌ التوصية: فرصة بيع"
            else:
                analysis += "⏸️ التوصية: انتظار إشارة أوضح"
            
            return analysis
        else:
            return f"عذراً، لا يتوفر تحليل لـ {symbol} حالياً."
    
    def get_trading_advice(self, symbol: str = None) -> str:
        """تقديم نصائح التداول"""
        if symbol:
            # نصيحة محددة للرمز
            if symbol == 'GOLD':
                return """🥇 نصائح تداول الذهب:
• راقب قوة الدولار الأمريكي
• تابع أخبار البنوك المركزية
• استخدم وقف خسارة عند 1.5%
• أفضل أوقات التداول: جلسة لندن ونيويورك"""
            else:
                return f"""📈 نصائح تداول {symbol}:
• استخدم التحليل الفني والأساسي
• لا تخاطر بأكثر من 2% من رأس المال
• ضع وقف خسارة دائماً
• تابع أخبار الشركة والقطاع"""
        else:
            return """💡 نصائح تداول عامة:
• ابدأ بحساب تجريبي
• تعلم إدارة المخاطر أولاً
• لا تتداول بالعواطف
• طور استراتيجية واضحة
• تعلم باستمرار"""
    
    def handle_greeting(self) -> str:
        """التعامل مع التحيات"""
        greetings = [
            f"مرحباً! أنا {self.name} 🤖",
            "أهلاً وسهلاً! كيف يمكنني مساعدتك في التداول اليوم؟",
            "السلام عليكم! أنا هنا لمساعدتك في تحليل الأسواق المالية.",
        ]
        
        import random
        greeting = random.choice(greetings)
        
        return f"""{greeting}

يمكنني مساعدتك في:
📊 تحليل الأسهم والسلع
💰 معرفة الأسعار الحالية
🎯 تقديم توصيات التداول
📚 تعليم مفاهيم التداول
🛡️ إدارة المخاطر

ما الذي تريد معرفته؟"""
    
    def process_message(self, message: str) -> str:
        """معالجة رسالة المستخدم"""
        logger.info(f"💬 User message: {message}")
        
        # حفظ الرسالة في التاريخ
        self.conversation_history.append({
            'timestamp': datetime.now().isoformat(),
            'user': message,
            'type': 'user_message'
        })
        
        # تحديد النية
        intent = self.detect_intent(message)
        symbol = self.extract_symbol(message)
        
        # معالجة حسب النية
        if intent == 'greeting':
            response = self.handle_greeting()
        
        elif intent == 'price_inquiry':
            if symbol:
                response = self.get_price_info(symbol)
            else:
                response = "أي سهم أو أصل تريد معرفة سعره؟ (مثل: AAPL، الذهب، TSLA)"
        
        elif intent == 'analysis_request':
            if symbol:
                response = self.get_analysis(symbol)
            else:
                response = "أي أصل تريد تحليله؟ يمكنني تحليل AAPL، الذهب، وأسهم أخرى."
        
        elif intent == 'trading_advice':
            response = self.get_trading_advice(symbol)
        
        elif intent == 'technical_question' or intent == 'educational':
            if self.knowledge_base:
                kb_response = self.knowledge_base.ask_question(message)
                response = f"📚 {kb_response['answer']}"
            else:
                response = "عذراً، قاعدة المعرفة غير متوفرة حالياً."
        
        elif intent == 'gold_inquiry':
            response = self.get_analysis('GOLD')
        
        elif intent == 'risk_management':
            response = """🛡️ قواعد إدارة المخاطر:

1️⃣ لا تخاطر بأكثر من 1-2% من رأس المال في صفقة واحدة
2️⃣ استخدم وقف الخسارة دائماً
3️⃣ نسبة المخاطرة للمكافأة: 1:2 كحد أدنى
4️⃣ نوع محفظتك عبر أصول مختلفة
5️⃣ لا تتداول بأموال لا تستطيع خسارتها

💡 تذكر: الحفاظ على رأس المال أهم من تحقيق الأرباح!"""
        
        else:
            # سؤال عام
            if self.knowledge_base:
                kb_response = self.knowledge_base.ask_question(message)
                if kb_response['confidence'] > 0.3:
                    response = f"🤖 {kb_response['answer']}"
                else:
                    response = self.get_default_response()
            else:
                response = self.get_default_response()
        
        # حفظ الرد في التاريخ
        self.conversation_history.append({
            'timestamp': datetime.now().isoformat(),
            'bot': response,
            'intent': intent,
            'symbol': symbol,
            'type': 'bot_response'
        })
        
        return response
    
    def get_default_response(self) -> str:
        """رد افتراضي"""
        return """🤖 أعتذر، لم أفهم سؤالك بوضوح.

يمكنك أن تسأل عن:
• أسعار الأسهم: "كم سعر AAPL؟"
• التحليل الفني: "حلل لي الذهب"
• التوصيات: "هل أشتري أم أبيع؟"
• التعليم: "ما هو مؤشر RSI؟"

كيف يمكنني مساعدتك؟"""
    
    def get_conversation_summary(self) -> Dict:
        """ملخص المحادثة"""
        return {
            'total_messages': len(self.conversation_history),
            'session_start': self.conversation_history[0]['timestamp'] if self.conversation_history else None,
            'last_activity': self.conversation_history[-1]['timestamp'] if self.conversation_history else None,
            'user_preferences': self.user_preferences
        }
    
    def save_conversation(self):
        """حفظ المحادثة"""
        try:
            if not os.path.exists('conversations'):
                os.makedirs('conversations')
            
            filename = f"conversations/chat_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            conversation_data = {
                'bot_info': {
                    'name': self.name,
                    'version': self.version
                },
                'summary': self.get_conversation_summary(),
                'history': self.conversation_history
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(conversation_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"💾 Conversation saved to {filename}")
            
        except Exception as e:
            logger.error(f"❌ Error saving conversation: {str(e)}")

def main():
    """الدالة الرئيسية - محاكاة محادثة"""
    print("🤖 AI Trading Chatbot Demo")
    print("=" * 60)
    
    # إنشاء الروبوت
    bot = AITradingChatbot()
    
    # رسائل تجريبية
    test_messages = [
        "مرحبا",
        "كم سعر AAPL؟",
        "حلل لي الذهب",
        "ما هو مؤشر RSI؟",
        "هل أشتري الذهب الآن؟",
        "كيف أدير المخاطر؟"
    ]
    
    for message in test_messages:
        print(f"\n👤 المستخدم: {message}")
        response = bot.process_message(message)
        print(f"🤖 الروبوت: {response}")
        print("-" * 60)
    
    # حفظ المحادثة
    bot.save_conversation()
    
    print(f"\n🎉 Chatbot Demo Completed!")
    print(f"📊 Total messages: {len(bot.conversation_history)}")

if __name__ == "__main__":
    main()
