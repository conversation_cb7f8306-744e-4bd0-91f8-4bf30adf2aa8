# 🔍 Vector Database - قاعدة البيانات الشعاعية

## 🎯 الغرض
تطوير نظام قاعدة بيانات شعاعية لتخزين واسترجاع المعلومات باستخدام Pinecone أو Chroma للذكاء الاصطناعي.

## 🛠️ التقنيات المستخدمة
- **Pinecone** - قاعدة بيانات شعاعية سحابية
- **Chroma** - قاعدة بيانات شعاعية مفتوحة المصدر
- **OpenAI Embeddings** - تحويل النصوص لشعاعات
- **FAISS** - بحث سريع في الشعاعات
- **LangChain** - إطار عمل LLM

## 📁 هيكل المجلد

```
vector_db/
├── embeddings/
│   ├── text/           # شعاعات النصوص
│   ├── financial/      # شعاعات البيانات المالية
│   ├── news/           # شعاعات الأخبار
│   └── documents/      # شعاعات المستندات
├── indexes/
│   ├── pinecone/       # فهارس Pinecone
│   ├── chroma/         # فهارس Chroma
│   └── faiss/          # فهارس FAISS
├── processors/
│   ├── text_processor/ # معالج النصوص
│   ├── embedding_gen/  # مولد الشعاعات
│   └── similarity/     # حساب التشابه
├── apis/
│   ├── search/         # APIs البحث
│   ├── insert/         # APIs الإدراج
│   └── update/         # APIs التحديث
└── configs/            # ملفات التكوين
```

## 🎯 حالات الاستخدام

### 📚 قاعدة معرفة التداول
- **المحتوى:**
  - كتب التداول الكلاسيكية
  - استراتيجيات التداول
  - تحليلات السوق
  - أدلة المؤشرات الفنية
- **الاستخدام:**
  - الإجابة على أسئلة التداول
  - اقتراح استراتيجيات
  - شرح المفاهيم المالية

### 📰 أرشيف الأخبار المالية
- **المحتوى:**
  - أخبار الأسواق المالية
  - تقارير الشركات
  - تحليلات الخبراء
  - بيانات اقتصادية
- **الاستخدام:**
  - البحث في الأخبار ذات الصلة
  - تحليل المشاعر التاريخية
  - ربط الأحداث بحركة الأسعار

### 💬 ذاكرة المحادثات
- **المحتوى:**
  - محادثات المستخدمين
  - تفضيلات التداول
  - استفسارات سابقة
  - سياق المحادثة
- **الاستخدام:**
  - تخصيص الردود
  - فهم السياق
  - تحسين التجربة

### 📊 قاعدة بيانات الأنماط
- **المحتوى:**
  - أنماط الرسوم البيانية
  - إشارات التداول
  - نماذج السوق
  - حالات تاريخية
- **الاستخدام:**
  - التعرف على الأنماط
  - مقارنة الحالات المشابهة
  - توليد إشارات ذكية

## 🔧 التطبيق التقني

### 🌐 إعداد Pinecone
```python
import pinecone
from pinecone import Pinecone

# تهيئة Pinecone
pc = Pinecone(api_key="your-api-key")

# إنشاء فهرس
index = pc.create_index(
    name="trading-knowledge",
    dimension=1536,  # OpenAI embeddings
    metric="cosine",
    spec=ServerlessSpec(
        cloud="aws",
        region="us-east-1"
    )
)
```

### 🏠 إعداد Chroma (محلي)
```python
import chromadb
from chromadb.config import Settings

# تهيئة Chroma
client = chromadb.Client(Settings(
    chroma_db_impl="duckdb+parquet",
    persist_directory="./chroma_db"
))

# إنشاء مجموعة
collection = client.create_collection(
    name="financial_documents",
    metadata={"description": "Financial trading documents"}
)
```

### 🔄 معالجة وإدراج البيانات
```python
class VectorDBManager:
    def __init__(self, db_type="pinecone"):
        self.db_type = db_type
        self.embeddings_model = OpenAIEmbeddings()
    
    def process_document(self, document):
        # تقسيم المستند لقطع
        chunks = self.split_document(document)
        
        # توليد الشعاعات
        embeddings = self.embeddings_model.embed_documents(chunks)
        
        # إدراج في قاعدة البيانات
        self.insert_vectors(chunks, embeddings)
    
    def search_similar(self, query, top_k=5):
        # البحث عن المحتوى المشابه
        query_embedding = self.embeddings_model.embed_query(query)
        results = self.vector_search(query_embedding, top_k)
        return results
```

### 🔍 APIs البحث والاسترجاع
```python
from fastapi import FastAPI
from pydantic import BaseModel

app = FastAPI()

class SearchQuery(BaseModel):
    query: str
    top_k: int = 5
    filter: dict = {}

@app.post("/search")
async def search_knowledge(query: SearchQuery):
    """البحث في قاعدة المعرفة"""
    results = vector_db.search_similar(
        query.query, 
        top_k=query.top_k
    )
    return {"results": results}

@app.post("/insert")
async def insert_document(document: dict):
    """إدراج مستند جديد"""
    vector_db.process_document(document)
    return {"status": "inserted"}
```

## 📊 أنواع البيانات المخزنة

### 📖 النصوص المالية
```python
# مثال على بيانات مخزنة
financial_text = {
    "id": "trading_strategy_001",
    "content": "استراتيجية المتوسط المتحرك...",
    "metadata": {
        "type": "strategy",
        "category": "technical_analysis",
        "author": "John Murphy",
        "difficulty": "beginner"
    },
    "embedding": [0.1, 0.2, 0.3, ...]  # 1536 dimensions
}
```

### 📰 الأخبار المالية
```python
news_article = {
    "id": "news_20240101_001",
    "content": "البنك المركزي يرفع أسعار الفائدة...",
    "metadata": {
        "type": "news",
        "date": "2024-01-01",
        "source": "Reuters",
        "sentiment": "negative",
        "impact": "high"
    },
    "embedding": [0.4, 0.5, 0.6, ...]
}
```

### 💬 سياق المحادثات
```python
conversation_context = {
    "id": "user_123_context",
    "content": "المستخدم مهتم بتداول الذهب...",
    "metadata": {
        "type": "context",
        "user_id": "123",
        "preferences": ["gold", "conservative"],
        "experience": "intermediate"
    },
    "embedding": [0.7, 0.8, 0.9, ...]
}
```

## 🚀 مميزات متقدمة

### 🔄 التحديث التلقائي
- مراقبة مصادر البيانات الجديدة
- تحديث الشعاعات تلقائياً
- إعادة فهرسة دورية
- تنظيف البيانات القديمة

### 🎯 البحث الذكي
- البحث الدلالي المتقدم
- فلترة حسب المعايير
- ترتيب النتائج بالأهمية
- اقتراحات البحث

### 📈 تحليل الأداء
- مراقبة دقة البحث
- إحصائيات الاستخدام
- تحسين الفهارس
- تقارير الأداء

## 📋 المهام القادمة
- [ ] إعداد حسابات Pinecone/Chroma
- [ ] تطوير معالجات النصوص
- [ ] إنشاء APIs البحث والإدراج
- [ ] تجميع المحتوى المالي
- [ ] تطوير نظام الفهرسة
- [ ] اختبار دقة البحث
- [ ] تحسين الأداء
- [ ] مراقبة النظام
