<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M&M AI Ultimate Trading Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* خلفية متحركة */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(45deg, #1e3c72, #2a5298, #3b82f6, #1e40af);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* شعاع ضوئي متحرك */
        .light-beam {
            position: absolute;
            width: 2px;
            height: 100px;
            background: linear-gradient(to bottom, transparent, #00d4ff, transparent);
            animation: lightMove 8s linear infinite;
        }

        @keyframes lightMove {
            0% { transform: translateX(-100px) translateY(-100px) rotate(45deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(calc(100vw + 100px)) translateY(calc(100vh + 100px)) rotate(45deg); opacity: 0; }
        }

        /* الحاوي الرئيسي */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        /* الهيدر */
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .logo {
            font-size: 3.5rem;
            font-weight: bold;
            background: linear-gradient(45deg, #00d4ff, #ffffff, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
            animation: logoGlow 3s ease-in-out infinite alternate;
        }

        @keyframes logoGlow {
            from { filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5)); }
            to { filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.8)); }
        }

        .subtitle {
            font-size: 1.2rem;
            color: #e0e7ff;
            margin-bottom: 30px;
        }

        /* الروبوت المتحرك */
        .ai-robot {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            position: relative;
            animation: robotFloat 4s ease-in-out infinite;
        }

        @keyframes robotFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        .robot-body {
            width: 80px;
            height: 80px;
            background: linear-gradient(145deg, #4f46e5, #7c3aed);
            border-radius: 20px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0 10px 30px rgba(79, 70, 229, 0.4);
        }

        .robot-eyes {
            display: flex;
            justify-content: space-between;
            padding: 15px 20px 0;
        }

        .robot-eye {
            width: 12px;
            height: 12px;
            background: #00d4ff;
            border-radius: 50%;
            animation: eyeBlink 3s infinite;
            box-shadow: 0 0 10px #00d4ff;
        }

        @keyframes eyeBlink {
            0%, 90%, 100% { transform: scaleY(1); }
            95% { transform: scaleY(0.1); }
        }

        .robot-mouth {
            width: 30px;
            height: 15px;
            background: #00d4ff;
            border-radius: 0 0 15px 15px;
            margin: 10px auto;
            animation: mouthTalk 2s infinite;
        }

        @keyframes mouthTalk {
            0%, 100% { transform: scaleY(1); }
            50% { transform: scaleY(0.5); }
        }

        .robot-antenna {
            width: 4px;
            height: 25px;
            background: #ffd700;
            margin: 0 auto;
            position: relative;
            top: -10px;
        }

        .robot-antenna::after {
            content: '';
            width: 8px;
            height: 8px;
            background: #ffd700;
            border-radius: 50%;
            position: absolute;
            top: -4px;
            left: -2px;
            animation: antennaGlow 2s infinite;
        }

        @keyframes antennaGlow {
            0%, 100% { box-shadow: 0 0 5px #ffd700; }
            50% { box-shadow: 0 0 15px #ffd700, 0 0 25px #ffd700; }
        }

        /* منطقة الدردشة */
        .chat-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .message {
            margin-bottom: 15px;
            padding: 15px 20px;
            border-radius: 20px;
            max-width: 80%;
            animation: messageSlide 0.5s ease-out;
        }

        @keyframes messageSlide {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-message {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: linear-gradient(135deg, #059669, #0d9488);
            margin-right: auto;
            text-align: left;
        }

        .message-time {
            font-size: 0.8rem;
            opacity: 0.7;
            margin-top: 5px;
        }

        /* منطقة الإدخال */
        .input-container {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            color: #1f2937;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
        }

        .chat-input:focus {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 20px rgba(79, 70, 229, 0.3);
        }

        .send-btn {
            padding: 15px 25px;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            border: none;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.4);
        }

        /* لوحة المعلومات */
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #00d4ff;
        }

        .card-content {
            font-size: 1rem;
            line-height: 1.6;
        }

        /* مؤشر الحالة */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
            animation: statusPulse 2s infinite;
        }

        @keyframes statusPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* أزرار سريعة */
        .quick-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .quick-btn {
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .quick-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .logo {
                font-size: 2.5rem;
            }
            
            .chat-container {
                padding: 20px;
            }
            
            .input-container {
                flex-direction: column;
                gap: 10px;
            }
            
            .chat-input, .send-btn {
                width: 100%;
            }
        }

        /* تأثيرات إضافية */
        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffd700;
            border-radius: 50%;
            animation: sparkleFloat 3s infinite;
        }

        @keyframes sparkleFloat {
            0%, 100% { opacity: 0; transform: translateY(0px); }
            50% { opacity: 1; transform: translateY(-30px); }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    
    <!-- أشعة ضوئية متحركة -->
    <div class="light-beam" style="top: 10%; animation-delay: 0s;"></div>
    <div class="light-beam" style="top: 30%; animation-delay: 2s;"></div>
    <div class="light-beam" style="top: 50%; animation-delay: 4s;"></div>
    <div class="light-beam" style="top: 70%; animation-delay: 6s;"></div>

    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="ai-robot">
                <div class="robot-antenna"></div>
                <div class="robot-body">
                    <div class="robot-eyes">
                        <div class="robot-eye"></div>
                        <div class="robot-eye"></div>
                    </div>
                    <div class="robot-mouth"></div>
                </div>
            </div>
            
            <h1 class="logo">M&M AI</h1>
            <p class="subtitle">🚀 النظام النهائي للذكاء الاصطناعي في التداول</p>
            
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>النظام متصل ويعمل بكامل طاقته</span>
            </div>
        </header>

        <!-- الأزرار السريعة -->
        <div class="quick-actions">
            <button class="quick-btn" onclick="quickAnalysis('AAPL')">📊 تحليل AAPL</button>
            <button class="quick-btn" onclick="quickAnalysis('GOLD')">🥇 تحليل الذهب</button>
            <button class="quick-btn" onclick="askQuestion('ما هو مؤشر RSI؟')">❓ شرح RSI</button>
            <button class="quick-btn" onclick="askQuestion('أريد استراتيجية تداول')">🎯 استراتيجيات</button>
            <button class="quick-btn" onclick="askQuestion('كيف أدير المخاطر؟')">🛡️ إدارة المخاطر</button>
        </div>

        <!-- لوحة المعلومات -->
        <div class="dashboard">
            <div class="dashboard-card">
                <div class="card-title">📈 التحليل الفني المتقدم</div>
                <div class="card-content">
                    • مؤشرات فنية شاملة (RSI, MACD, Bollinger Bands)<br>
                    • تحليل الأنماط والاتجاهات<br>
                    • إشارات دخول وخروج دقيقة<br>
                    • تحديث مباشر كل ثانية
                </div>
            </div>
            
            <div class="dashboard-card">
                <div class="card-title">🧠 الذكاء الاصطناعي المتطور</div>
                <div class="card-content">
                    • نماذج لغوية متقدمة (GPT-4, LLaMA)<br>
                    • قاعدة معرفة من كتب التداول الكلاسيكية<br>
                    • تعلم من أفضل المتداولين في العالم<br>
                    • إجابات مخصصة لمستوى خبرتك
                </div>
            </div>
            
            <div class="dashboard-card">
                <div class="card-title">🌍 دعم متعدد اللغات</div>
                <div class="card-content">
                    • العربية والإنجليزية<br>
                    • ترجمة فورية للمصطلحات<br>
                    • شرح مبسط للمفاهيم المعقدة<br>
                    • تكيف مع ثقافة التداول المحلية
                </div>
            </div>
            
            <div class="dashboard-card">
                <div class="card-title">⚡ البيانات المباشرة</div>
                <div class="card-content">
                    • أسعار مباشرة من الأسواق العالمية<br>
                    • تحديث كل ثانية<br>
                    • تغطية شاملة (أسهم، فوركس، سلع)<br>
                    • تحليل فوري للتغيرات
                </div>
            </div>
        </div>

        <!-- منطقة الدردشة -->
        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="message ai-message">
                    <div>🤖 مرحباً! أنا M&M AI، خبير التداول الذكي. كيف يمكنني مساعدتك اليوم؟</div>
                    <div class="message-time" id="currentTime"></div>
                </div>
            </div>
            
            <div class="input-container">
                <input type="text" class="chat-input" id="chatInput" 
                       placeholder="اسأل عن أي شيء يخص التداول والاستثمار..." 
                       onkeypress="handleKeyPress(event)">
                <button class="send-btn" onclick="sendMessage()">إرسال 🚀</button>
            </div>
        </div>
    </div>

    <!-- تأثيرات البريق -->
    <div class="sparkle" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
    <div class="sparkle" style="top: 60%; left: 80%; animation-delay: 1s;"></div>
    <div class="sparkle" style="top: 40%; left: 20%; animation-delay: 2s;"></div>
    <div class="sparkle" style="top: 80%; left: 60%; animation-delay: 3s;"></div>

    <script>
        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);

        // إرسال رسالة
        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (message) {
                addMessage(message, 'user');
                input.value = '';
                
                // محاكاة رد الذكاء الاصطناعي
                setTimeout(() => {
                    const aiResponse = generateAIResponse(message);
                    addMessage(aiResponse, 'ai');
                }, 1000);
            }
        }

        // إضافة رسالة للدردشة
        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            messageDiv.innerHTML = `
                <div>${text}</div>
                <div class="message-time">${timeString}</div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // توليد رد الذكاء الاصطناعي
        function generateAIResponse(userMessage) {
            const message = userMessage.toLowerCase();
            
            if (message.includes('aapl') || message.includes('أبل')) {
                return `📊 **تحليل سهم AAPL**

💰 **السعر الحالي**: $198.25 (+0.85%)

📈 **التحليل الفني**:
• RSI: 52.3 - منطقة محايدة
• MACD: إشارة صاعدة ضعيفة
• الاتجاه: صاعد على المدى المتوسط

🎯 **التوصية**: انتظار كسر مستوى $200 للشراء
⚠️ **المخاطر**: متوسطة - ضع وقف خسارة عند $195`;
            }
            
            if (message.includes('rsi') || message.includes('مؤشر')) {
                return `📚 **مؤشر القوة النسبية (RSI)**

🔍 **التعريف**: مؤشر يقيس سرعة وتغيير حركة السعر

📊 **القراءة**:
• فوق 70: تشبع شرائي (فرصة بيع)
• تحت 30: تشبع بيعي (فرصة شراء)
• 50: خط التوازن

💡 **نصائح الاستخدام**:
- لا تعتمد عليه وحده
- ادمجه مع مؤشرات أخرى
- انتبه للتباعد مع السعر`;
            }
            
            if (message.includes('استراتيجية') || message.includes('strategy')) {
                return `🎯 **استراتيجيات التداول المتقدمة**

⚡ **للمبتدئين - Swing Trading**:
• الإطار الزمني: 1-4 أسابيع
• المؤشرات: SMA 20/50، RSI
• معدل النجاح: 70-80%

🚀 **للمتقدمين - Scalping**:
• الإطار الزمني: 1-5 دقائق
• المؤشرات: EMA 9/21، Stochastic
• معدل النجاح: 60-70%

💡 **نصيحة**: ابدأ بالتداول المتأرجح وتدرج للأسرع`;
            }
            
            if (message.includes('مخاطر') || message.includes('risk')) {
                return `🛡️ **إدارة المخاطر الذكية**

📏 **قاعدة الـ 2%**: لا تخاطر بأكثر من 2% من رأس المال

🧮 **حساب حجم المركز**:
حجم المركز = (رأس المال × 2%) ÷ (سعر الدخول - وقف الخسارة)

⚖️ **نسبة المخاطرة/المكافأة**: 1:3 على الأقل

🔄 **أنواع وقف الخسارة**:
• ثابت: مستوى سعري محدد
• متحرك: يتحرك مع السعر
• زمني: إغلاق بعد فترة محددة`;
            }
            
            if (message.includes('ذهب') || message.includes('gold')) {
                return `🥇 **تحليل الذهب (XAU/USD)**

💰 **السعر الحالي**: $2,045.30 (+0.65%)

📈 **التحليل**:
• الاتجاه: صاعد قوي
• الدعم: $2,020
• المقاومة: $2,060

🌍 **العوامل المؤثرة**:
• التضخم العالمي
• سياسة البنوك المركزية
• التوترات الجيوسياسية

🎯 **التوصية**: شراء عند التراجع للدعم`;
            }
            
            // رد عام
            return `🤖 شكراً لسؤالك! أنا هنا لمساعدتك في:

📊 **التحليل الفني**: RSI، MACD، Bollinger Bands
🎯 **الاستراتيجيات**: Scalping، Swing Trading، Position Trading  
🛡️ **إدارة المخاطر**: حساب حجم المركز، وقف الخسارة
📚 **التعليم**: شرح المفاهيم والمؤشرات
💰 **تحليل الأسهم**: AAPL، GOOGL، MSFT، والمزيد

اسأل عن أي شيء محدد وسأقدم لك تحليلاً مفصلاً!`;
        }

        // التحليل السريع
        function quickAnalysis(symbol) {
            const input = document.getElementById('chatInput');
            input.value = `حلل لي ${symbol} تحليلاً شاملاً`;
            sendMessage();
        }

        // سؤال سريع
        function askQuestion(question) {
            const input = document.getElementById('chatInput');
            input.value = question;
            sendMessage();
        }

        // التعامل مع الضغط على Enter
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // تأثيرات إضافية عند التحميل
        window.addEventListener('load', function() {
            // إضافة رسالة ترحيب متقدمة
            setTimeout(() => {
                addMessage(`🎉 **مرحباً بك في M&M AI Ultimate Trading Platform!**

✨ **ما الجديد في النسخة 3.0:**
• 🧠 ذكاء اصطناعي متطور مع GPT-4
• 📚 قاعدة معرفة من أفضل كتب التداول
• ⚡ بيانات مباشرة كل ثانية
• 🌍 دعم كامل للغة العربية
• 🎯 تحليل متقدم للمخاطر

💡 **جرب الأزرار السريعة أعلاه أو اسأل أي سؤال!**`, 'ai');
            }, 2000);
        });
    </script>
</body>
</html>
