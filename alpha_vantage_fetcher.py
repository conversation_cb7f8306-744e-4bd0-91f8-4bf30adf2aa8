#!/usr/bin/env python3
"""
📈 Alpha Vantage Data Fetcher
جالب بيانات Alpha Vantage

المرحلة 4: جلب البيانات المالية المتقدمة
- بيانات العملات الأجنبية
- بيانات العملات المشفرة
- المؤشرات الاقتصادية
- الأخبار والمشاعر
"""

from alpha_vantage.timeseries import TimeSeries
from alpha_vantage.foreignexchange import ForeignExchange
from alpha_vantage.cryptocurrencies import CryptoCurrencies
from alpha_vantage.fundamentaldata import FundamentalData
import pandas as pd
import os
import logging
import time
from datetime import datetime
from typing import Dict, Optional, List
import json

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('alpha_vantage_fetcher.log'),
        logging.StreamHandler()
    ]
)

class AlphaVantageFetcher:
    """فئة جلب البيانات من Alpha Vantage"""
    
    def __init__(self, api_key: str, data_dir: str = "alpha_vantage_data"):
        """
        تهيئة جالب البيانات
        
        Args:
            api_key: مفتاح API من Alpha Vantage
            data_dir: مجلد حفظ البيانات
        """
        self.api_key = api_key
        self.data_dir = data_dir
        self.create_data_directory()
        
        # تهيئة العملاء
        self.ts = TimeSeries(key=api_key, output_format='pandas')
        self.fx = ForeignExchange(key=api_key, output_format='pandas')
        self.crypto = CryptoCurrencies(key=api_key, output_format='pandas')
        self.fundamentals = FundamentalData(key=api_key, output_format='pandas')
        
        # رموز العملات المهمة
        self.forex_pairs = [
            ('EUR', 'USD'),
            ('GBP', 'USD'),
            ('USD', 'JPY'),
            ('USD', 'CHF'),
            ('AUD', 'USD'),
            ('USD', 'CAD'),
            ('NZD', 'USD'),
            ('USD', 'SEK'),
            ('USD', 'NOK'),
            ('USD', 'DKK')
        ]
        
        # العملات المشفرة المهمة
        self.crypto_symbols = [
            'BTC', 'ETH', 'BNB', 'XRP', 'ADA',
            'SOL', 'DOGE', 'DOT', 'AVAX', 'SHIB'
        ]
        
        # الأسهم المهمة
        self.stock_symbols = [
            'AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA',
            'META', 'NVDA', 'NFLX', 'BABA', 'V'
        ]
    
    def create_data_directory(self):
        """إنشاء مجلد البيانات"""
        directories = [
            self.data_dir,
            os.path.join(self.data_dir, 'stocks'),
            os.path.join(self.data_dir, 'forex'),
            os.path.join(self.data_dir, 'crypto'),
            os.path.join(self.data_dir, 'fundamentals')
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logging.info(f"✅ تم إنشاء مجلد: {directory}")
    
    def fetch_stock_data(self, symbol: str, outputsize: str = 'compact') -> Optional[pd.DataFrame]:
        """
        جلب بيانات الأسهم
        
        Args:
            symbol: رمز السهم
            outputsize: حجم البيانات ('compact' أو 'full')
        
        Returns:
            DataFrame مع بيانات السهم
        """
        try:
            logging.info(f"📊 جلب بيانات السهم: {symbol}")
            
            # جلب البيانات اليومية
            data, meta_data = self.ts.get_daily_adjusted(symbol=symbol, outputsize=outputsize)
            
            if data.empty:
                logging.warning(f"⚠️ لا توجد بيانات للسهم {symbol}")
                return None
            
            # إعادة تسمية الأعمدة
            data.columns = ['Open', 'High', 'Low', 'Close', 'Adjusted_Close', 'Volume', 'Dividend', 'Split']
            data['Symbol'] = symbol
            data['Date'] = data.index
            
            logging.info(f"✅ تم جلب {len(data)} سجل للسهم {symbol}")
            return data
            
        except Exception as e:
            logging.error(f"❌ خطأ في جلب بيانات السهم {symbol}: {str(e)}")
            return None
    
    def fetch_forex_data(self, from_currency: str, to_currency: str) -> Optional[pd.DataFrame]:
        """
        جلب بيانات العملات الأجنبية
        
        Args:
            from_currency: العملة الأساسية
            to_currency: العملة المقابلة
        
        Returns:
            DataFrame مع بيانات العملة
        """
        try:
            pair = f"{from_currency}/{to_currency}"
            logging.info(f"💱 جلب بيانات العملة: {pair}")
            
            # جلب البيانات اليومية
            data, meta_data = self.fx.get_daily(from_symbol=from_currency, to_symbol=to_currency)
            
            if data.empty:
                logging.warning(f"⚠️ لا توجد بيانات لزوج العملة {pair}")
                return None
            
            # إعادة تسمية الأعمدة
            data.columns = ['Open', 'High', 'Low', 'Close']
            data['Pair'] = pair
            data['Date'] = data.index
            
            logging.info(f"✅ تم جلب {len(data)} سجل لزوج العملة {pair}")
            return data
            
        except Exception as e:
            logging.error(f"❌ خطأ في جلب بيانات العملة {from_currency}/{to_currency}: {str(e)}")
            return None
    
    def fetch_crypto_data(self, symbol: str, market: str = 'USD') -> Optional[pd.DataFrame]:
        """
        جلب بيانات العملات المشفرة
        
        Args:
            symbol: رمز العملة المشفرة
            market: السوق (USD, EUR, etc.)
        
        Returns:
            DataFrame مع بيانات العملة المشفرة
        """
        try:
            pair = f"{symbol}/{market}"
            logging.info(f"₿ جلب بيانات العملة المشفرة: {pair}")
            
            # جلب البيانات اليومية
            data, meta_data = self.crypto.get_daily(symbol=symbol, market=market)
            
            if data.empty:
                logging.warning(f"⚠️ لا توجد بيانات للعملة المشفرة {pair}")
                return None
            
            # إعادة تسمية الأعمدة
            data.columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'Market_Cap']
            data['Symbol'] = symbol
            data['Market'] = market
            data['Date'] = data.index
            
            logging.info(f"✅ تم جلب {len(data)} سجل للعملة المشفرة {pair}")
            return data
            
        except Exception as e:
            logging.error(f"❌ خطأ في جلب بيانات العملة المشفرة {symbol}: {str(e)}")
            return None
    
    def fetch_company_overview(self, symbol: str) -> Optional[Dict]:
        """
        جلب نظرة عامة على الشركة
        
        Args:
            symbol: رمز السهم
        
        Returns:
            قاموس يحتوي على معلومات الشركة
        """
        try:
            logging.info(f"🏢 جلب معلومات الشركة: {symbol}")
            
            data, meta_data = self.fundamentals.get_company_overview(symbol=symbol)
            
            if data.empty:
                logging.warning(f"⚠️ لا توجد معلومات للشركة {symbol}")
                return None
            
            # تحويل إلى قاموس
            company_info = data.to_dict('records')[0] if not data.empty else {}
            
            logging.info(f"✅ تم جلب معلومات الشركة {symbol}")
            return company_info
            
        except Exception as e:
            logging.error(f"❌ خطأ في جلب معلومات الشركة {symbol}: {str(e)}")
            return None
    
    def save_to_csv(self, data: pd.DataFrame, filename: str, category: str):
        """
        حفظ البيانات في ملف CSV
        
        Args:
            data: البيانات المراد حفظها
            filename: اسم الملف
            category: فئة البيانات
        """
        try:
            file_path = os.path.join(self.data_dir, category, f"{filename}.csv")
            data.to_csv(file_path, index=False)
            logging.info(f"💾 تم حفظ البيانات في {file_path}")
            
        except Exception as e:
            logging.error(f"❌ خطأ في حفظ البيانات: {str(e)}")
    
    def save_to_json(self, data: Dict, filename: str, category: str):
        """
        حفظ البيانات في ملف JSON
        
        Args:
            data: البيانات المراد حفظها
            filename: اسم الملف
            category: فئة البيانات
        """
        try:
            file_path = os.path.join(self.data_dir, category, f"{filename}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logging.info(f"💾 تم حفظ البيانات في {file_path}")
            
        except Exception as e:
            logging.error(f"❌ خطأ في حفظ البيانات: {str(e)}")
    
    def fetch_all_data(self):
        """جلب جميع البيانات"""
        logging.info("🚀 بدء جلب جميع البيانات من Alpha Vantage...")
        
        # جلب بيانات الأسهم
        logging.info("📊 جلب بيانات الأسهم...")
        for symbol in self.stock_symbols[:5]:  # تحديد العدد لتجنب تجاوز الحد
            stock_data = self.fetch_stock_data(symbol)
            if stock_data is not None:
                self.save_to_csv(stock_data, symbol, 'stocks')
            
            # معلومات الشركة
            company_info = self.fetch_company_overview(symbol)
            if company_info:
                self.save_to_json(company_info, f"{symbol}_overview", 'fundamentals')
            
            time.sleep(12)  # تجنب تحديد المعدل (5 calls per minute)
        
        # جلب بيانات العملات
        logging.info("💱 جلب بيانات العملات...")
        for from_curr, to_curr in self.forex_pairs[:3]:  # تحديد العدد
            forex_data = self.fetch_forex_data(from_curr, to_curr)
            if forex_data is not None:
                self.save_to_csv(forex_data, f"{from_curr}_{to_curr}", 'forex')
            
            time.sleep(12)
        
        # جلب بيانات العملات المشفرة
        logging.info("₿ جلب بيانات العملات المشفرة...")
        for symbol in self.crypto_symbols[:3]:  # تحديد العدد
            crypto_data = self.fetch_crypto_data(symbol)
            if crypto_data is not None:
                self.save_to_csv(crypto_data, f"{symbol}_USD", 'crypto')
            
            time.sleep(12)
        
        logging.info("🎉 تم الانتهاء من جلب البيانات!")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء جلب البيانات من Alpha Vantage")
    
    # مفتاح API (يجب الحصول عليه من Alpha Vantage)
    api_key = "demo"  # استبدل بمفتاحك الحقيقي
    
    if api_key == "demo":
        print("⚠️ تحذير: يتم استخدام مفتاح تجريبي. للحصول على مفتاح مجاني:")
        print("🔗 https://www.alphavantage.co/support/#api-key")
        print("💡 ضع مفتاحك في متغير البيئة ALPHA_VANTAGE_API_KEY")
    
    # إنشاء جالب البيانات
    fetcher = AlphaVantageFetcher(api_key)
    
    # جلب عينة من البيانات
    print("\n📊 جلب عينة من البيانات...")
    
    # مثال على جلب بيانات سهم
    stock_data = fetcher.fetch_stock_data('AAPL')
    if stock_data is not None:
        fetcher.save_to_csv(stock_data, 'AAPL_sample', 'stocks')
        print(f"✅ تم جلب بيانات AAPL: {len(stock_data)} سجل")

if __name__ == "__main__":
    main()
