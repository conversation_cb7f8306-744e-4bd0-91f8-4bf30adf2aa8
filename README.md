# 🧠 منصة التداول الذكية - AI Trading Platform

## 🎯 نظرة عامة

منصة تداول متطورة تدمج الذكاء الاصطناعي والتعلم الآلي لتوفير تجربة تداول ذكية ومتقدمة.

## 📊 المرحلة الحالية: إعداد قواعد البيانات

### ✅ المرحلة 1: إعداد البنية التحتية الأساسية
- ✅ إنشاء مجلد المشروع الرئيسي
- ✅ إعداد بيئة التطوير

### 🗄️ المرحلة 2: إعداد قواعد البيانات
- ✅ PostgreSQL - بيانات المستخدمين والإشارات
- ✅ MongoDB - المحادثات والبيانات غير المنظمة
- ✅ Redis - كاش البيانات الفورية
- ✅ InfluxDB - بيانات السوق الزمنية

## 🛠️ التقنيات المستخدمة

### قواعد البيانات
- **PostgreSQL 15** - قاعدة بيانات علائقية للبيانات المنظمة
- **MongoDB 7.0** - قاعدة بيانات NoSQL للبيانات غير المنظمة
- **Redis 7** - ذاكرة تخزين مؤقت للبيانات السريعة
- **InfluxDB 2.7** - قاعدة بيانات زمنية لبيانات السوق

### أدوات التطوير
- **Docker & Docker Compose** - إدارة الحاويات
- **Adminer** - إدارة قواعد البيانات عبر الويب

## 🚀 كيفية التشغيل

### المتطلبات الأساسية
- Docker Desktop
- Python 3.11+

### خطوات التشغيل

1. **تشغيل قواعد البيانات:**
```bash
docker-compose up -d
```

2. **التحقق من حالة الخدمات:**
```bash
docker-compose ps
```

3. **الوصول لإدارة قواعد البيانات:**
- Adminer: http://localhost:8080

## 📁 هيكل المشروع

```
ai-trading-platform/
├── .env                    # متغيرات البيئة
├── docker-compose.yml      # إعداد Docker
├── setup_databases.py      # سكريبت إعداد قواعد البيانات
├── README.md              # دليل المشروع
└── database/              # مجلد قواعد البيانات
    ├── postgres/
    │   └── init/          # سكريبتات تهيئة PostgreSQL
    ├── mongodb/
    │   └── init/          # سكريبتات تهيئة MongoDB
    ├── schemas/           # مخططات قواعد البيانات
    └── migrations/        # ملفات الترحيل
```

## 🔐 معلومات الاتصال

### PostgreSQL
- **Host:** localhost:5432
- **Database:** ai_trading_db
- **Username:** trading_admin
- **Password:** TradingSecure2024!

### MongoDB
- **Host:** localhost:27017
- **Username:** mongo_admin
- **Password:** MongoSecure2024!

### Redis
- **Host:** localhost:6379
- **Password:** RedisSecure2024!

### InfluxDB
- **Host:** localhost:8086
- **Username:** influx_admin
- **Password:** InfluxSecure2024!

## 📋 المراحل القادمة

- [ ] المرحلة 3: إعداد Backend API
- [ ] المرحلة 4: تطوير نماذج الذكاء الاصطناعي
- [ ] المرحلة 5: إنشاء Frontend
- [ ] المرحلة 6: التكامل والاختبار

## 🔧 الأوامر المفيدة

```bash
# إيقاف جميع الخدمات
docker-compose down

# إعادة بناء الحاويات
docker-compose up --build -d

# عرض السجلات
docker-compose logs -f

# تنظيف البيانات (احذر!)
docker-compose down -v
```

---

**🎉 تم إنجاز المرحلة الثانية بنجاح!**
