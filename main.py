#!/usr/bin/env python3
"""
🚀 Main Data Ingestion Runner
المشغل الرئيسي لجلب البيانات المالية

المرحلة 4: تشغيل نظام جلب البيانات المالية
- تشغيل جلب البيانات من مصادر متعددة
- عرض النتائج والإحصائيات
- إدارة العمليات
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime
from typing import Dict, List
import time

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from yahoo_fetcher import YahooFinanceFetcher
from alpha_vantage_fetcher import AlphaVantageFetcher
from data_scheduler import DataScheduler

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('main_data_ingestion.log'),
        logging.StreamHandler()
    ]
)

class DataIngestionManager:
    """مدير نظام جلب البيانات المالية"""
    
    def __init__(self):
        """تهيئة المدير"""
        self.config = self.load_config()
        self.yahoo_fetcher = None
        self.alpha_vantage_fetcher = None
        self.scheduler = None
        
        # إحصائيات العمليات
        self.stats = {
            'start_time': datetime.now(),
            'operations': {
                'yahoo_finance': {'attempted': 0, 'successful': 0, 'failed': 0},
                'alpha_vantage': {'attempted': 0, 'successful': 0, 'failed': 0}
            },
            'data_points': 0,
            'files_created': 0
        }
    
    def load_config(self) -> Dict:
        """تحميل ملف التكوين"""
        try:
            with open('data_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            logging.info("✅ تم تحميل ملف التكوين")
            return config
        except Exception as e:
            logging.error(f"❌ خطأ في تحميل التكوين: {str(e)}")
            return {}
    
    def initialize_fetchers(self):
        """تهيئة جالبات البيانات"""
        logging.info("🔧 تهيئة جالبات البيانات...")
        
        # Yahoo Finance
        if self.config.get('data_sources', {}).get('yahoo_finance', {}).get('enabled', True):
            self.yahoo_fetcher = YahooFinanceFetcher("data/yahoo_finance")
            logging.info("✅ تم تهيئة Yahoo Finance fetcher")
        
        # Alpha Vantage
        alpha_vantage_config = self.config.get('data_sources', {}).get('alpha_vantage', {})
        if alpha_vantage_config.get('enabled', False):
            api_key = os.getenv('ALPHA_VANTAGE_API_KEY')
            if api_key and api_key != 'demo':
                self.alpha_vantage_fetcher = AlphaVantageFetcher(api_key, "data/alpha_vantage")
                logging.info("✅ تم تهيئة Alpha Vantage fetcher")
            else:
                logging.warning("⚠️ مفتاح Alpha Vantage غير متوفر")
    
    def run_yahoo_finance_demo(self):
        """تشغيل عرض توضيحي لـ Yahoo Finance"""
        if not self.yahoo_fetcher:
            logging.error("❌ Yahoo Finance fetcher غير متوفر")
            return
        
        print("\n" + "="*60)
        print("📊 عرض توضيحي - Yahoo Finance")
        print("="*60)
        
        # جلب بيانات عينة من الأسهم
        sample_stocks = ['AAPL', 'GOOGL', 'MSFT', 'TSLA']
        
        for symbol in sample_stocks:
            try:
                self.stats['operations']['yahoo_finance']['attempted'] += 1
                
                print(f"\n📈 جلب بيانات {symbol}...")
                data = self.yahoo_fetcher.fetch_single_symbol(symbol, period="1mo")
                
                if data is not None:
                    self.yahoo_fetcher.save_to_csv(data, symbol, "demo_stocks")
                    self.stats['operations']['yahoo_finance']['successful'] += 1
                    self.stats['data_points'] += len(data)
                    self.stats['files_created'] += 1
                    
                    # عرض معلومات السهم
                    latest_price = data['Close'].iloc[-1]
                    price_change = data['Close'].iloc[-1] - data['Close'].iloc[-2]
                    price_change_pct = (price_change / data['Close'].iloc[-2]) * 100
                    
                    print(f"  💰 آخر سعر: ${latest_price:.2f}")
                    print(f"  📊 التغيير: ${price_change:.2f} ({price_change_pct:+.2f}%)")
                    print(f"  📅 عدد السجلات: {len(data)}")
                    print(f"  ✅ تم الحفظ في: data/yahoo_finance/demo_stocks/{symbol}.csv")
                else:
                    self.stats['operations']['yahoo_finance']['failed'] += 1
                    print(f"  ❌ فشل في جلب بيانات {symbol}")
                
                time.sleep(1)  # تجنب تحديد المعدل
                
            except Exception as e:
                self.stats['operations']['yahoo_finance']['failed'] += 1
                logging.error(f"❌ خطأ في جلب {symbol}: {str(e)}")
        
        # جلب بيانات الذهب (XAU/USD)
        print(f"\n🥇 جلب بيانات الذهب...")
        try:
            gold_data = self.yahoo_fetcher.fetch_single_symbol('GC=F', period="1mo")
            if gold_data is not None:
                self.yahoo_fetcher.save_to_csv(gold_data, 'GOLD', "demo_commodities")
                latest_gold_price = gold_data['Close'].iloc[-1]
                print(f"  💰 سعر الذهب الحالي: ${latest_gold_price:.2f}")
                print(f"  ✅ تم حفظ بيانات الذهب")
        except Exception as e:
            logging.error(f"❌ خطأ في جلب بيانات الذهب: {str(e)}")
    
    def run_alpha_vantage_demo(self):
        """تشغيل عرض توضيحي لـ Alpha Vantage"""
        if not self.alpha_vantage_fetcher:
            print("\n⚠️ Alpha Vantage غير متوفر (يحتاج مفتاح API)")
            return
        
        print("\n" + "="*60)
        print("📈 عرض توضيحي - Alpha Vantage")
        print("="*60)
        
        # جلب بيانات سهم واحد
        try:
            self.stats['operations']['alpha_vantage']['attempted'] += 1
            
            print(f"\n📊 جلب بيانات AAPL من Alpha Vantage...")
            stock_data = self.alpha_vantage_fetcher.fetch_stock_data('AAPL')
            
            if stock_data is not None:
                self.alpha_vantage_fetcher.save_to_csv(stock_data, 'AAPL_alpha', 'demo_stocks')
                self.stats['operations']['alpha_vantage']['successful'] += 1
                self.stats['data_points'] += len(stock_data)
                self.stats['files_created'] += 1
                print(f"  ✅ تم جلب {len(stock_data)} سجل")
            else:
                self.stats['operations']['alpha_vantage']['failed'] += 1
                print(f"  ❌ فشل في جلب البيانات")
                
        except Exception as e:
            self.stats['operations']['alpha_vantage']['failed'] += 1
            logging.error(f"❌ خطأ في Alpha Vantage: {str(e)}")
    
    def display_stats(self):
        """عرض إحصائيات العمليات"""
        print("\n" + "="*60)
        print("📊 إحصائيات العمليات")
        print("="*60)
        
        duration = datetime.now() - self.stats['start_time']
        
        print(f"⏱️  مدة التشغيل: {duration}")
        print(f"📈 نقاط البيانات المجمعة: {self.stats['data_points']:,}")
        print(f"📁 الملفات المنشأة: {self.stats['files_created']}")
        
        print(f"\n📊 Yahoo Finance:")
        yahoo_stats = self.stats['operations']['yahoo_finance']
        print(f"  🎯 المحاولات: {yahoo_stats['attempted']}")
        print(f"  ✅ الناجحة: {yahoo_stats['successful']}")
        print(f"  ❌ الفاشلة: {yahoo_stats['failed']}")
        if yahoo_stats['attempted'] > 0:
            success_rate = (yahoo_stats['successful'] / yahoo_stats['attempted']) * 100
            print(f"  📈 معدل النجاح: {success_rate:.1f}%")
        
        print(f"\n📈 Alpha Vantage:")
        alpha_stats = self.stats['operations']['alpha_vantage']
        print(f"  🎯 المحاولات: {alpha_stats['attempted']}")
        print(f"  ✅ الناجحة: {alpha_stats['successful']}")
        print(f"  ❌ الفاشلة: {alpha_stats['failed']}")
        if alpha_stats['attempted'] > 0:
            success_rate = (alpha_stats['successful'] / alpha_stats['attempted']) * 100
            print(f"  📈 معدل النجاح: {success_rate:.1f}%")
    
    def run_demo(self):
        """تشغيل العرض التوضيحي الكامل"""
        print("🚀 بدء نظام جلب البيانات المالية")
        print("=" * 60)
        
        # تهيئة الجالبات
        self.initialize_fetchers()
        
        # تشغيل العروض التوضيحية
        self.run_yahoo_finance_demo()
        self.run_alpha_vantage_demo()
        
        # عرض الإحصائيات
        self.display_stats()
        
        print("\n🎉 تم الانتهاء من العرض التوضيحي!")
        print("📁 تحقق من مجلد 'data' لرؤية الملفات المحفوظة")
    
    def run_scheduler(self):
        """تشغيل المجدول"""
        print("⏰ بدء مجدول جلب البيانات...")
        self.scheduler = DataScheduler()
        self.scheduler.start()

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description='نظام جلب البيانات المالية')
    parser.add_argument('--mode', choices=['demo', 'schedule'], default='demo',
                       help='وضع التشغيل (demo أو schedule)')
    
    args = parser.parse_args()
    
    # إنشاء مدير النظام
    manager = DataIngestionManager()
    
    if args.mode == 'demo':
        manager.run_demo()
    elif args.mode == 'schedule':
        manager.run_scheduler()

if __name__ == "__main__":
    main()
