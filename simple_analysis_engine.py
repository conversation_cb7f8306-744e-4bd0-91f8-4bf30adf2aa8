#!/usr/bin/env python3
"""
📊 Simple Technical Analysis Engine
محرك التحليل الفني المبسط

المرحلة 5: تحليل فني بسيط بدون مكتبات خارجية
- حساب المؤشرات الأساسية
- توليد إشارات التداول
- تقييم الاتجاهات
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime
import sys

class SimpleAnalysisEngine:
    """محرك التحليل الفني المبسط"""
    
    def __init__(self, data_path="../data_ingestion"):
        """تهيئة المحرك"""
        self.data_path = data_path
        self.results_dir = "analysis_results"
        self.create_directories()
    
    def create_directories(self):
        """إنشاء مجلدات النتائج"""
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
            print(f"📁 Created directory: {self.results_dir}")
    
    def load_data(self, symbol):
        """تحميل بيانات الرمز"""
        try:
            # البحث عن الملف
            possible_files = [
                f"{self.data_path}/{symbol}.csv",
                f"{self.data_path}/{symbol.replace('=', '_').replace('-', '_')}.csv"
            ]
            
            file_path = None
            for path in possible_files:
                if os.path.exists(path):
                    file_path = path
                    break
            
            if not file_path:
                print(f"❌ File not found for {symbol}")
                return None
            
            df = pd.read_csv(file_path)
            
            # تحويل التاريخ
            if 'Date' in df.columns:
                df['Date'] = pd.to_datetime(df['Date'])
                df.set_index('Date', inplace=True)
            
            print(f"✅ Loaded {symbol}: {len(df)} records")
            return df
            
        except Exception as e:
            print(f"❌ Error loading {symbol}: {str(e)}")
            return None
    
    def calculate_sma(self, prices, period):
        """حساب المتوسط المتحرك البسيط"""
        return prices.rolling(window=period).mean()
    
    def calculate_ema(self, prices, period):
        """حساب المتوسط المتحرك الأسي"""
        return prices.ewm(span=period).mean()
    
    def calculate_rsi(self, prices, period=14):
        """حساب مؤشر القوة النسبية"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """حساب MACD"""
        ema_fast = self.calculate_ema(prices, fast)
        ema_slow = self.calculate_ema(prices, slow)
        macd = ema_fast - ema_slow
        macd_signal = self.calculate_ema(macd, signal)
        macd_histogram = macd - macd_signal
        return macd, macd_signal, macd_histogram
    
    def calculate_bollinger_bands(self, prices, period=20, std_dev=2):
        """حساب نطاقات بولينجر"""
        sma = self.calculate_sma(prices, period)
        std = prices.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, sma, lower_band
    
    def analyze_symbol(self, symbol):
        """تحليل رمز واحد"""
        print(f"\n📊 Analyzing {symbol}...")
        
        # تحميل البيانات
        df = self.load_data(symbol)
        if df is None:
            return None
        
        # التأكد من وجود البيانات الكافية
        if len(df) < 50:
            print(f"⚠️ Not enough data for {symbol}")
            return None
        
        # حساب المؤشرات
        df['SMA_10'] = self.calculate_sma(df['Close'], 10)
        df['SMA_20'] = self.calculate_sma(df['Close'], 20)
        df['SMA_50'] = self.calculate_sma(df['Close'], 50)
        
        df['EMA_12'] = self.calculate_ema(df['Close'], 12)
        df['EMA_26'] = self.calculate_ema(df['Close'], 26)
        
        df['RSI'] = self.calculate_rsi(df['Close'])
        
        macd, macd_signal, macd_hist = self.calculate_macd(df['Close'])
        df['MACD'] = macd
        df['MACD_Signal'] = macd_signal
        df['MACD_Histogram'] = macd_hist
        
        bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(df['Close'])
        df['BB_Upper'] = bb_upper
        df['BB_Middle'] = bb_middle
        df['BB_Lower'] = bb_lower
        
        # حساب مؤشرات إضافية
        df['Volume_SMA'] = self.calculate_sma(df['Volume'], 20)
        df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA']
        
        # توليد الإشارات
        signals = self.generate_signals(df, symbol)
        
        # حفظ النتائج
        self.save_analysis(df, signals, symbol)
        
        return signals
    
    def generate_signals(self, df, symbol):
        """توليد إشارات التداول"""
        latest = df.iloc[-1]
        previous = df.iloc[-2]
        
        signals = {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'current_price': float(latest['Close']),
            'analysis': {},
            'signals': [],
            'overall_signal': 'HOLD',
            'confidence': 0
        }
        
        # تحليل RSI
        rsi = latest['RSI']
        if rsi < 30:
            signals['analysis']['RSI'] = 'Oversold'
            signals['signals'].append('BUY - RSI Oversold')
        elif rsi > 70:
            signals['analysis']['RSI'] = 'Overbought'
            signals['signals'].append('SELL - RSI Overbought')
        else:
            signals['analysis']['RSI'] = 'Neutral'
        
        # تحليل MACD
        if latest['MACD'] > latest['MACD_Signal'] and previous['MACD'] <= previous['MACD_Signal']:
            signals['analysis']['MACD'] = 'Bullish Crossover'
            signals['signals'].append('BUY - MACD Bullish Crossover')
        elif latest['MACD'] < latest['MACD_Signal'] and previous['MACD'] >= previous['MACD_Signal']:
            signals['analysis']['MACD'] = 'Bearish Crossover'
            signals['signals'].append('SELL - MACD Bearish Crossover')
        else:
            signals['analysis']['MACD'] = 'No Clear Signal'
        
        # تحليل المتوسطات المتحركة
        price = latest['Close']
        sma_20 = latest['SMA_20']
        sma_50 = latest['SMA_50']
        
        if price > sma_20 > sma_50:
            signals['analysis']['Moving_Averages'] = 'Bullish Trend'
            signals['signals'].append('BUY - Price Above Moving Averages')
        elif price < sma_20 < sma_50:
            signals['analysis']['Moving_Averages'] = 'Bearish Trend'
            signals['signals'].append('SELL - Price Below Moving Averages')
        else:
            signals['analysis']['Moving_Averages'] = 'Sideways'
        
        # تحليل نطاقات بولينجر
        bb_position = (price - latest['BB_Lower']) / (latest['BB_Upper'] - latest['BB_Lower'])
        if bb_position < 0.2:
            signals['analysis']['Bollinger_Bands'] = 'Near Lower Band'
            signals['signals'].append('BUY - Near Bollinger Lower Band')
        elif bb_position > 0.8:
            signals['analysis']['Bollinger_Bands'] = 'Near Upper Band'
            signals['signals'].append('SELL - Near Bollinger Upper Band')
        else:
            signals['analysis']['Bollinger_Bands'] = 'Middle Range'
        
        # تحليل الحجم
        volume_ratio = latest['Volume_Ratio']
        if volume_ratio > 1.5:
            signals['analysis']['Volume'] = 'High Volume'
            signals['signals'].append(f'HIGH VOLUME - {volume_ratio:.1f}x average')
        else:
            signals['analysis']['Volume'] = 'Normal Volume'
        
        # تحديد الإشارة الإجمالية
        buy_signals = len([s for s in signals['signals'] if s.startswith('BUY')])
        sell_signals = len([s for s in signals['signals'] if s.startswith('SELL')])
        
        if buy_signals > sell_signals and buy_signals >= 2:
            signals['overall_signal'] = 'BUY'
            signals['confidence'] = min(buy_signals * 25, 100)
        elif sell_signals > buy_signals and sell_signals >= 2:
            signals['overall_signal'] = 'SELL'
            signals['confidence'] = min(sell_signals * 25, 100)
        else:
            signals['overall_signal'] = 'HOLD'
            signals['confidence'] = 50
        
        return signals
    
    def save_analysis(self, df, signals, symbol):
        """حفظ نتائج التحليل"""
        try:
            # حفظ البيانات مع المؤشرات
            data_file = f"{self.results_dir}/{symbol}_analysis_data.csv"
            df.to_csv(data_file)
            
            # حفظ الإشارات
            signals_file = f"{self.results_dir}/{symbol}_signals.json"
            with open(signals_file, 'w', encoding='utf-8') as f:
                json.dump(signals, f, ensure_ascii=False, indent=2)
            
            print(f"💾 Saved analysis: {data_file}")
            print(f"💾 Saved signals: {signals_file}")
            
        except Exception as e:
            print(f"❌ Error saving analysis: {str(e)}")
    
    def display_results(self, signals):
        """عرض النتائج"""
        if not signals:
            return
        
        print(f"\n{'='*50}")
        print(f"📊 ANALYSIS RESULTS - {signals['symbol']}")
        print(f"{'='*50}")
        print(f"💰 Current Price: ${signals['current_price']:.2f}")
        print(f"📈 Overall Signal: {signals['overall_signal']}")
        print(f"🎯 Confidence: {signals['confidence']}%")
        
        print(f"\n📋 Technical Analysis:")
        for indicator, result in signals['analysis'].items():
            print(f"  • {indicator}: {result}")
        
        print(f"\n🚨 Trading Signals:")
        for signal in signals['signals']:
            print(f"  • {signal}")
        
        print(f"\n🕐 Analysis Time: {signals['timestamp']}")

def main():
    """الدالة الرئيسية"""
    print("🚀 Simple Technical Analysis Engine")
    print("=" * 60)
    
    # إنشاء المحرك
    engine = SimpleAnalysisEngine()
    
    # رموز للتحليل
    symbols = ['AAPL', 'GOLD']
    
    for symbol in symbols:
        try:
            # تحليل الرمز
            signals = engine.analyze_symbol(symbol)
            
            if signals:
                # عرض النتائج
                engine.display_results(signals)
            else:
                print(f"❌ Failed to analyze {symbol}")
                
        except Exception as e:
            print(f"❌ Error analyzing {symbol}: {str(e)}")
    
    print(f"\n🎉 Analysis completed!")
    print(f"📁 Check '{engine.results_dir}' directory for detailed results")

if __name__ == "__main__":
    main()
