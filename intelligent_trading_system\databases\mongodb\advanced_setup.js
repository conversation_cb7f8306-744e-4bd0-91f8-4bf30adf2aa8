// =====================================================
// M&M AI Trading System - Advanced MongoDB Setup
// نظام التداول الذكي المتكامل - إعداد MongoDB المتقدم
// =====================================================

// Switch to trading database
use('mmai_trading');

// =====================================================
// Create Collections with Validation Schemas
// =====================================================

// Market Data Collection (Time Series)
db.createCollection("market_data_ts", {
    timeseries: {
        timeField: "timestamp",
        metaField: "symbol",
        granularity: "minutes"
    },
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["symbol", "timestamp", "open", "high", "low", "close", "volume"],
            properties: {
                symbol: {
                    bsonType: "string",
                    description: "Trading symbol"
                },
                timestamp: {
                    bsonType: "date",
                    description: "Data timestamp"
                },
                open: {
                    bsonType: "double",
                    minimum: 0,
                    description: "Opening price"
                },
                high: {
                    bsonType: "double",
                    minimum: 0,
                    description: "Highest price"
                },
                low: {
                    bsonType: "double",
                    minimum: 0,
                    description: "Lowest price"
                },
                close: {
                    bsonType: "double",
                    minimum: 0,
                    description: "Closing price"
                },
                volume: {
                    bsonType: "long",
                    minimum: 0,
                    description: "Trading volume"
                },
                bid: {
                    bsonType: "double",
                    minimum: 0,
                    description: "Bid price"
                },
                ask: {
                    bsonType: "double",
                    minimum: 0,
                    description: "Ask price"
                },
                spread: {
                    bsonType: "double",
                    minimum: 0,
                    description: "Bid-ask spread"
                }
            }
        }
    }
});

// News and Sentiment Collection
db.createCollection("news_sentiment", {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["title", "content", "source", "published_at", "sentiment"],
            properties: {
                title: {
                    bsonType: "string",
                    maxLength: 500,
                    description: "News article title"
                },
                content: {
                    bsonType: "string",
                    description: "Full article content"
                },
                summary: {
                    bsonType: "string",
                    maxLength: 1000,
                    description: "Article summary"
                },
                source: {
                    bsonType: "string",
                    description: "News source"
                },
                author: {
                    bsonType: "string",
                    description: "Article author"
                },
                published_at: {
                    bsonType: "date",
                    description: "Publication timestamp"
                },
                url: {
                    bsonType: "string",
                    description: "Article URL"
                },
                language: {
                    bsonType: "string",
                    enum: ["en", "ar", "es", "fr", "de", "zh", "ja"],
                    description: "Article language"
                },
                symbols: {
                    bsonType: "array",
                    items: {
                        bsonType: "string"
                    },
                    description: "Related trading symbols"
                },
                sentiment: {
                    bsonType: "object",
                    required: ["score", "label"],
                    properties: {
                        score: {
                            bsonType: "double",
                            minimum: -1.0,
                            maximum: 1.0,
                            description: "Sentiment score (-1 to 1)"
                        },
                        label: {
                            bsonType: "string",
                            enum: ["POSITIVE", "NEGATIVE", "NEUTRAL"],
                            description: "Sentiment classification"
                        },
                        confidence: {
                            bsonType: "double",
                            minimum: 0.0,
                            maximum: 1.0,
                            description: "Confidence in sentiment analysis"
                        }
                    }
                },
                keywords: {
                    bsonType: "array",
                    items: {
                        bsonType: "string"
                    },
                    description: "Extracted keywords"
                },
                entities: {
                    bsonType: "object",
                    description: "Named entities extracted from text"
                },
                impact_score: {
                    bsonType: "double",
                    minimum: 0.0,
                    maximum: 1.0,
                    description: "Potential market impact score"
                }
            }
        }
    }
});

// AI Model Predictions Collection
db.createCollection("ai_predictions", {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["symbol", "model_info", "prediction", "timestamp"],
            properties: {
                symbol: {
                    bsonType: "string",
                    description: "Trading symbol"
                },
                model_info: {
                    bsonType: "object",
                    required: ["name", "version", "type"],
                    properties: {
                        name: {
                            bsonType: "string",
                            description: "Model name"
                        },
                        version: {
                            bsonType: "string",
                            description: "Model version"
                        },
                        type: {
                            bsonType: "string",
                            enum: ["LSTM", "CNN", "TRANSFORMER", "ENSEMBLE", "REINFORCEMENT"],
                            description: "Model type"
                        },
                        architecture: {
                            bsonType: "object",
                            description: "Model architecture details"
                        }
                    }
                },
                prediction: {
                    bsonType: "object",
                    required: ["type", "value", "confidence"],
                    properties: {
                        type: {
                            bsonType: "string",
                            enum: ["PRICE", "DIRECTION", "VOLATILITY", "SIGNAL"],
                            description: "Prediction type"
                        },
                        value: {
                            bsonType: "double",
                            description: "Predicted value"
                        },
                        confidence: {
                            bsonType: "double",
                            minimum: 0.0,
                            maximum: 1.0,
                            description: "Prediction confidence"
                        },
                        horizon: {
                            bsonType: "int",
                            minimum: 1,
                            description: "Prediction horizon in minutes"
                        },
                        target_time: {
                            bsonType: "date",
                            description: "Target prediction time"
                        }
                    }
                },
                features: {
                    bsonType: "object",
                    description: "Input features used for prediction"
                },
                timestamp: {
                    bsonType: "date",
                    description: "Prediction creation time"
                },
                actual_value: {
                    bsonType: "double",
                    description: "Actual value (filled later)"
                },
                accuracy: {
                    bsonType: "double",
                    minimum: 0.0,
                    maximum: 1.0,
                    description: "Prediction accuracy"
                }
            }
        }
    }
});

// Trading Strategies Collection
db.createCollection("trading_strategies", {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["name", "type", "parameters", "created_by"],
            properties: {
                name: {
                    bsonType: "string",
                    maxLength: 255,
                    description: "Strategy name"
                },
                description: {
                    bsonType: "string",
                    description: "Strategy description"
                },
                type: {
                    bsonType: "string",
                    enum: ["TECHNICAL", "FUNDAMENTAL", "AI", "HYBRID", "QUANTITATIVE"],
                    description: "Strategy type"
                },
                parameters: {
                    bsonType: "object",
                    description: "Strategy parameters and configuration"
                },
                symbols: {
                    bsonType: "array",
                    items: {
                        bsonType: "string"
                    },
                    description: "Applicable symbols"
                },
                timeframes: {
                    bsonType: "array",
                    items: {
                        bsonType: "string",
                        enum: ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"]
                    },
                    description: "Applicable timeframes"
                },
                risk_management: {
                    bsonType: "object",
                    properties: {
                        risk_per_trade: {
                            bsonType: "double",
                            minimum: 0.001,
                            maximum: 0.1,
                            description: "Risk per trade (percentage)"
                        },
                        max_positions: {
                            bsonType: "int",
                            minimum: 1,
                            maximum: 100,
                            description: "Maximum concurrent positions"
                        },
                        stop_loss: {
                            bsonType: "double",
                            minimum: 0.001,
                            description: "Stop loss percentage"
                        },
                        take_profit: {
                            bsonType: "double",
                            minimum: 0.001,
                            description: "Take profit percentage"
                        }
                    }
                },
                performance: {
                    bsonType: "object",
                    properties: {
                        total_trades: {
                            bsonType: "int",
                            minimum: 0
                        },
                        winning_trades: {
                            bsonType: "int",
                            minimum: 0
                        },
                        win_rate: {
                            bsonType: "double",
                            minimum: 0.0,
                            maximum: 1.0
                        },
                        total_return: {
                            bsonType: "double"
                        },
                        sharpe_ratio: {
                            bsonType: "double"
                        },
                        max_drawdown: {
                            bsonType: "double"
                        }
                    }
                },
                created_by: {
                    bsonType: "string",
                    description: "Strategy creator user ID"
                },
                is_active: {
                    bsonType: "bool",
                    description: "Strategy active status"
                },
                created_at: {
                    bsonType: "date",
                    description: "Creation timestamp"
                },
                updated_at: {
                    bsonType: "date",
                    description: "Last update timestamp"
                }
            }
        }
    }
});

// Social Media Sentiment Collection
db.createCollection("social_sentiment", {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["symbol", "platform", "sentiment_data", "timestamp"],
            properties: {
                symbol: {
                    bsonType: "string",
                    description: "Trading symbol"
                },
                platform: {
                    bsonType: "string",
                    enum: ["twitter", "reddit", "stocktwits", "telegram", "discord"],
                    description: "Social media platform"
                },
                sentiment_data: {
                    bsonType: "object",
                    required: ["score", "volume"],
                    properties: {
                        score: {
                            bsonType: "double",
                            minimum: -1.0,
                            maximum: 1.0,
                            description: "Aggregated sentiment score"
                        },
                        volume: {
                            bsonType: "int",
                            minimum: 0,
                            description: "Number of mentions"
                        },
                        trending_score: {
                            bsonType: "double",
                            minimum: 0.0,
                            maximum: 1.0,
                            description: "Trending intensity"
                        },
                        top_keywords: {
                            bsonType: "array",
                            items: {
                                bsonType: "string"
                            },
                            description: "Most mentioned keywords"
                        },
                        influencer_sentiment: {
                            bsonType: "double",
                            minimum: -1.0,
                            maximum: 1.0,
                            description: "Sentiment from influential accounts"
                        }
                    }
                },
                timestamp: {
                    bsonType: "date",
                    description: "Data collection timestamp"
                },
                raw_data: {
                    bsonType: "array",
                    description: "Raw social media posts (optional)"
                }
            }
        }
    }
});

// Alternative Data Collection
db.createCollection("alternative_data", {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["data_type", "source", "data", "timestamp"],
            properties: {
                data_type: {
                    bsonType: "string",
                    enum: ["SATELLITE", "GOOGLE_TRENDS", "ECONOMIC", "WEATHER", "SUPPLY_CHAIN"],
                    description: "Type of alternative data"
                },
                source: {
                    bsonType: "string",
                    description: "Data source"
                },
                symbols: {
                    bsonType: "array",
                    items: {
                        bsonType: "string"
                    },
                    description: "Related symbols"
                },
                data: {
                    bsonType: "object",
                    description: "Raw alternative data"
                },
                processed_data: {
                    bsonType: "object",
                    description: "Processed and normalized data"
                },
                impact_score: {
                    bsonType: "double",
                    minimum: 0.0,
                    maximum: 1.0,
                    description: "Potential market impact"
                },
                timestamp: {
                    bsonType: "date",
                    description: "Data timestamp"
                },
                metadata: {
                    bsonType: "object",
                    description: "Additional metadata"
                }
            }
        }
    }
});

// Model Training Logs Collection
db.createCollection("model_training_logs", {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["model_name", "training_session", "metrics", "timestamp"],
            properties: {
                model_name: {
                    bsonType: "string",
                    description: "Model identifier"
                },
                training_session: {
                    bsonType: "object",
                    required: ["session_id", "start_time", "end_time"],
                    properties: {
                        session_id: {
                            bsonType: "string",
                            description: "Unique training session ID"
                        },
                        start_time: {
                            bsonType: "date",
                            description: "Training start time"
                        },
                        end_time: {
                            bsonType: "date",
                            description: "Training end time"
                        },
                        duration_minutes: {
                            bsonType: "double",
                            minimum: 0,
                            description: "Training duration"
                        }
                    }
                },
                hyperparameters: {
                    bsonType: "object",
                    description: "Model hyperparameters"
                },
                metrics: {
                    bsonType: "object",
                    description: "Training and validation metrics"
                },
                dataset_info: {
                    bsonType: "object",
                    properties: {
                        size: {
                            bsonType: "int",
                            minimum: 0
                        },
                        features: {
                            bsonType: "array",
                            items: {
                                bsonType: "string"
                            }
                        },
                        date_range: {
                            bsonType: "object",
                            properties: {
                                start: {
                                    bsonType: "date"
                                },
                                end: {
                                    bsonType: "date"
                                }
                            }
                        }
                    }
                },
                model_artifacts: {
                    bsonType: "object",
                    properties: {
                        model_path: {
                            bsonType: "string"
                        },
                        weights_path: {
                            bsonType: "string"
                        },
                        config_path: {
                            bsonType: "string"
                        }
                    }
                },
                timestamp: {
                    bsonType: "date",
                    description: "Log creation time"
                }
            }
        }
    }
});

// =====================================================
// Create Indexes for Performance
// =====================================================

// Market Data Indexes
db.market_data_ts.createIndex({ "symbol": 1, "timestamp": -1 });
db.market_data_ts.createIndex({ "timestamp": -1 });

// News Sentiment Indexes
db.news_sentiment.createIndex({ "published_at": -1 });
db.news_sentiment.createIndex({ "symbols": 1 });
db.news_sentiment.createIndex({ "sentiment.score": 1 });
db.news_sentiment.createIndex({ "source": 1, "published_at": -1 });
db.news_sentiment.createIndex({ "keywords": 1 });

// AI Predictions Indexes
db.ai_predictions.createIndex({ "symbol": 1, "timestamp": -1 });
db.ai_predictions.createIndex({ "model_info.name": 1, "timestamp": -1 });
db.ai_predictions.createIndex({ "prediction.target_time": 1 });
db.ai_predictions.createIndex({ "prediction.confidence": -1 });

// Trading Strategies Indexes
db.trading_strategies.createIndex({ "created_by": 1 });
db.trading_strategies.createIndex({ "type": 1 });
db.trading_strategies.createIndex({ "symbols": 1 });
db.trading_strategies.createIndex({ "is_active": 1 });

// Social Sentiment Indexes
db.social_sentiment.createIndex({ "symbol": 1, "timestamp": -1 });
db.social_sentiment.createIndex({ "platform": 1, "timestamp": -1 });
db.social_sentiment.createIndex({ "sentiment_data.trending_score": -1 });

// Alternative Data Indexes
db.alternative_data.createIndex({ "data_type": 1, "timestamp": -1 });
db.alternative_data.createIndex({ "symbols": 1, "timestamp": -1 });
db.alternative_data.createIndex({ "impact_score": -1 });

// Model Training Logs Indexes
db.model_training_logs.createIndex({ "model_name": 1, "timestamp": -1 });
db.model_training_logs.createIndex({ "training_session.session_id": 1 });

// Text Search Indexes
db.news_sentiment.createIndex({ 
    "title": "text", 
    "content": "text", 
    "keywords": "text" 
});

// =====================================================
// Sample Data Insertion
// =====================================================

// Insert sample market data
db.market_data_ts.insertMany([
    {
        symbol: "EURUSD",
        timestamp: new Date(),
        open: 1.0850,
        high: 1.0875,
        low: 1.0840,
        close: 1.0860,
        volume: NumberLong(1500000),
        bid: 1.0859,
        ask: 1.0861,
        spread: 0.0002
    },
    {
        symbol: "BTCUSD",
        timestamp: new Date(),
        open: 45000.00,
        high: 45500.00,
        low: 44800.00,
        close: 45200.00,
        volume: NumberLong(850),
        bid: 45195.00,
        ask: 45205.00,
        spread: 10.00
    }
]);

// Insert sample news
db.news_sentiment.insertOne({
    title: "Federal Reserve Announces Interest Rate Decision",
    content: "The Federal Reserve announced its latest interest rate decision...",
    summary: "Fed maintains current interest rates amid economic uncertainty",
    source: "Reuters",
    author: "Financial Reporter",
    published_at: new Date(),
    url: "https://example.com/fed-decision",
    language: "en",
    symbols: ["EURUSD", "USDJPY", "GBPUSD"],
    sentiment: {
        score: -0.2,
        label: "NEGATIVE",
        confidence: 0.85
    },
    keywords: ["federal reserve", "interest rates", "monetary policy"],
    entities: {
        organizations: ["Federal Reserve"],
        locations: ["United States"]
    },
    impact_score: 0.9
});

// Insert sample AI prediction
db.ai_predictions.insertOne({
    symbol: "EURUSD",
    model_info: {
        name: "LSTM_Price_Predictor",
        version: "1.0.0",
        type: "LSTM",
        architecture: {
            layers: 3,
            hidden_units: [128, 64, 32],
            dropout: 0.2
        }
    },
    prediction: {
        type: "PRICE",
        value: 1.0875,
        confidence: 0.78,
        horizon: 60,
        target_time: new Date(Date.now() + 60 * 60 * 1000)
    },
    features: {
        technical_indicators: ["SMA_20", "RSI", "MACD"],
        market_data_points: 100,
        sentiment_score: -0.1
    },
    timestamp: new Date()
});

// Insert sample trading strategy
db.trading_strategies.insertOne({
    name: "AI Enhanced Momentum Strategy",
    description: "Combines technical analysis with AI predictions for momentum trading",
    type: "AI",
    parameters: {
        rsi_period: 14,
        rsi_overbought: 70,
        rsi_oversold: 30,
        ai_confidence_threshold: 0.7,
        momentum_period: 20
    },
    symbols: ["EURUSD", "GBPUSD", "USDJPY"],
    timeframes: ["1h", "4h"],
    risk_management: {
        risk_per_trade: 0.02,
        max_positions: 5,
        stop_loss: 0.015,
        take_profit: 0.03
    },
    performance: {
        total_trades: 0,
        winning_trades: 0,
        win_rate: 0.0,
        total_return: 0.0,
        sharpe_ratio: 0.0,
        max_drawdown: 0.0
    },
    created_by: "system",
    is_active: true,
    created_at: new Date(),
    updated_at: new Date()
});

print("✅ MongoDB setup completed successfully!");
print("📊 Collections created with validation schemas");
print("🔍 Indexes created for optimal performance");
print("📝 Sample data inserted");
print("🚀 Database ready for M&M AI Trading System");
