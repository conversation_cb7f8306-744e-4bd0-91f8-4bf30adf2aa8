#!/usr/bin/env python3
"""
🚀 Comprehensive Financial Data Fetcher
جالب البيانات المالية الشامل

المرحلة 4: جلب البيانات المالية الحقيقية
- بيانات الأسهم العالمية
- بيانات العملات والسلع
- بيانات العملات المشفرة
- المؤشرات المالية
"""

import yfinance as yf
import pandas as pd
import os
import time
from datetime import datetime
import json

class ComprehensiveDataFetcher:
    """جالب البيانات المالية الشامل"""
    
    def __init__(self):
        """تهيئة الجالب"""
        self.data_dir = "comprehensive_data"
        self.create_directories()
        
        # رموز الأصول المالية
        self.symbols = {
            'stocks': {
                'US_Tech': ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'META', 'NVDA'],
                'US_Finance': ['JPM', 'BAC', 'WFC', 'GS', 'MS'],
                'US_Healthcare': ['JNJ', 'PFE', 'UNH', 'ABBV', 'MRK'],
                'Global': ['BABA', 'TSM', 'ASML', 'SAP', 'TM']
            },
            'forex': [
                'EURUSD=X', 'GBPUSD=X', 'USDJPY=X', 'USDCHF=X',
                'AUDUSD=X', 'USDCAD=X', 'NZDUSD=X', 'EURGBP=X'
            ],
            'commodities': [
                'GC=F',    # Gold
                'SI=F',    # Silver
                'CL=F',    # Crude Oil
                'NG=F',    # Natural Gas
                'HG=F',    # Copper
                'PL=F',    # Platinum
                'PA=F',    # Palladium
            ],
            'crypto': [
                'BTC-USD', 'ETH-USD', 'BNB-USD', 'XRP-USD',
                'ADA-USD', 'SOL-USD', 'DOGE-USD', 'DOT-USD'
            ],
            'indices': [
                '^GSPC',   # S&P 500
                '^DJI',    # Dow Jones
                '^IXIC',   # NASDAQ
                '^RUT',    # Russell 2000
                '^VIX',    # VIX
                '^FTSE',   # FTSE 100
                '^GDAXI',  # DAX
                '^N225',   # Nikkei 225
            ]
        }
        
        # إحصائيات
        self.stats = {
            'start_time': datetime.now(),
            'total_symbols': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'total_data_points': 0
        }
    
    def create_directories(self):
        """إنشاء مجلدات البيانات"""
        directories = [
            self.data_dir,
            f"{self.data_dir}/stocks",
            f"{self.data_dir}/forex",
            f"{self.data_dir}/commodities",
            f"{self.data_dir}/crypto",
            f"{self.data_dir}/indices",
            f"{self.data_dir}/reports"
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 Created directory: {directory}")
    
    def fetch_symbol_data(self, symbol, category, period="3mo"):
        """جلب بيانات رمز واحد"""
        try:
            print(f"📊 Fetching {symbol}...")
            
            # جلب البيانات
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            
            if data.empty:
                print(f"  ⚠️ No data for {symbol}")
                self.stats['failed_downloads'] += 1
                return None
            
            # إضافة معلومات إضافية
            data['Symbol'] = symbol
            data['Category'] = category
            
            # حفظ البيانات
            filename = f"{self.data_dir}/{category}/{symbol.replace('=', '_').replace('-', '_').replace('^', '')}.csv"
            data.to_csv(filename)
            
            # إحصائيات
            self.stats['successful_downloads'] += 1
            self.stats['total_data_points'] += len(data)
            
            # عرض معلومات
            latest_price = data['Close'].iloc[-1]
            price_change = data['Close'].iloc[-1] - data['Close'].iloc[-2] if len(data) > 1 else 0
            price_change_pct = (price_change / data['Close'].iloc[-2]) * 100 if len(data) > 1 else 0
            
            print(f"  💰 Latest: ${latest_price:.2f}")
            print(f"  📈 Change: {price_change:+.2f} ({price_change_pct:+.2f}%)")
            print(f"  📅 Records: {len(data)}")
            print(f"  ✅ Saved: {filename}")
            
            return data
            
        except Exception as e:
            print(f"  ❌ Error fetching {symbol}: {str(e)}")
            self.stats['failed_downloads'] += 1
            return None
    
    def fetch_category(self, category, symbols, delay=1):
        """جلب بيانات فئة كاملة"""
        print(f"\n{'='*60}")
        print(f"📂 Fetching {category.upper()} data...")
        print(f"{'='*60}")
        
        if isinstance(symbols, dict):
            # إذا كانت الرموز مجمعة في مجموعات فرعية
            for subcategory, symbol_list in symbols.items():
                print(f"\n📊 {subcategory} stocks:")
                for symbol in symbol_list:
                    self.fetch_symbol_data(symbol, f"{category}/{subcategory}")
                    self.stats['total_symbols'] += 1
                    time.sleep(delay)
        else:
            # إذا كانت قائمة بسيطة
            for symbol in symbols:
                self.fetch_symbol_data(symbol, category)
                self.stats['total_symbols'] += 1
                time.sleep(delay)
    
    def get_market_summary(self):
        """الحصول على ملخص السوق"""
        print(f"\n{'='*60}")
        print("📊 MARKET SUMMARY")
        print(f"{'='*60}")
        
        # مؤشرات رئيسية
        key_indices = ['^GSPC', '^DJI', '^IXIC', '^VIX']
        
        for symbol in key_indices:
            try:
                ticker = yf.Ticker(symbol)
                data = ticker.history(period="2d")
                
                if not data.empty:
                    current = data['Close'].iloc[-1]
                    previous = data['Close'].iloc[-2] if len(data) > 1 else current
                    change = current - previous
                    change_pct = (change / previous) * 100 if previous != 0 else 0
                    
                    name_map = {
                        '^GSPC': 'S&P 500',
                        '^DJI': 'Dow Jones',
                        '^IXIC': 'NASDAQ',
                        '^VIX': 'VIX'
                    }
                    
                    name = name_map.get(symbol, symbol)
                    print(f"📈 {name:12}: {current:8.2f} ({change:+6.2f}, {change_pct:+5.2f}%)")
                    
            except Exception as e:
                print(f"❌ Error getting {symbol}: {str(e)}")
        
        # أسعار السلع المهمة
        print(f"\n💰 COMMODITIES:")
        commodities = [('GC=F', 'Gold'), ('SI=F', 'Silver'), ('CL=F', 'Oil')]
        
        for symbol, name in commodities:
            try:
                ticker = yf.Ticker(symbol)
                data = ticker.history(period="2d")
                
                if not data.empty:
                    current = data['Close'].iloc[-1]
                    previous = data['Close'].iloc[-2] if len(data) > 1 else current
                    change = current - previous
                    change_pct = (change / previous) * 100 if previous != 0 else 0
                    
                    print(f"🥇 {name:12}: ${current:8.2f} ({change:+6.2f}, {change_pct:+5.2f}%)")
                    
            except Exception as e:
                print(f"❌ Error getting {name}: {str(e)}")
    
    def generate_report(self):
        """إنشاء تقرير شامل"""
        duration = datetime.now() - self.stats['start_time']
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'duration_seconds': duration.total_seconds(),
            'statistics': self.stats,
            'success_rate': (self.stats['successful_downloads'] / self.stats['total_symbols'] * 100) if self.stats['total_symbols'] > 0 else 0,
            'data_points_per_symbol': self.stats['total_data_points'] / self.stats['successful_downloads'] if self.stats['successful_downloads'] > 0 else 0
        }
        
        # حفظ التقرير
        report_file = f"{self.data_dir}/reports/fetch_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # عرض الإحصائيات
        print(f"\n{'='*60}")
        print("📊 FETCH STATISTICS")
        print(f"{'='*60}")
        print(f"⏱️  Duration: {duration}")
        print(f"🎯 Total symbols: {self.stats['total_symbols']}")
        print(f"✅ Successful: {self.stats['successful_downloads']}")
        print(f"❌ Failed: {self.stats['failed_downloads']}")
        print(f"📈 Success rate: {report['success_rate']:.1f}%")
        print(f"📊 Total data points: {self.stats['total_data_points']:,}")
        print(f"📄 Report saved: {report_file}")
    
    def run_comprehensive_fetch(self):
        """تشغيل جلب البيانات الشامل"""
        print("🚀 Starting Comprehensive Financial Data Fetch")
        print(f"🕐 Start time: {self.stats['start_time']}")
        
        # ملخص السوق أولاً
        self.get_market_summary()
        
        # جلب البيانات حسب الفئات
        self.fetch_category('stocks', self.symbols['stocks'], delay=0.5)
        self.fetch_category('forex', self.symbols['forex'], delay=1)
        self.fetch_category('commodities', self.symbols['commodities'], delay=1)
        self.fetch_category('crypto', self.symbols['crypto'], delay=1)
        self.fetch_category('indices', self.symbols['indices'], delay=1)
        
        # إنشاء التقرير النهائي
        self.generate_report()
        
        print(f"\n🎉 Comprehensive data fetch completed!")
        print(f"📁 Check '{self.data_dir}' directory for all data files")

def main():
    """الدالة الرئيسية"""
    fetcher = ComprehensiveDataFetcher()
    fetcher.run_comprehensive_fetch()

if __name__ == "__main__":
    main()
