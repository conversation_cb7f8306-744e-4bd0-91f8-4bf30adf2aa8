# 🧠 M&M AI Trading System - نظام التداول الذكي المتكامل

## 🌟 نظرة عامة

**M&M AI Trading System** هو نظام تداول ذكي متكامل يجمع بين أحدث تقنيات الذكاء الاصطناعي والتعلم الآلي لتوفير تجربة تداول متطورة وفعالة. النظام مصمم ليكون شاملاً ومتطوراً، يدعم جميع أنواع الأصول المالية ويوفر أدوات تحليل متقدمة.

## 🚀 المميزات الرئيسية

### 🤖 الذكاء الاصطناعي المتقدم
- **نماذج LSTM متعددة الطبقات** للتنبؤ بالأسعار
- **Bidirectional LSTM** مع آلية الانتباه
- **التعلم المعزز** مع DQN و PPO
- **نماذج مجمعة** للدقة العالية
- **معالجة اللغة الطبيعية** لتحليل الأخبار والمشاعر

### 📊 تحليل البيانات الشامل
- **50+ مؤشر فني** متقدم
- **تحليل أساسي** للشركات
- **تحليل المشاعر** من وسائل التواصل الاجتماعي
- **البيانات البديلة** (الأقمار الصناعية، Google Trends)
- **معالجة البيانات في الوقت الفعلي**

### ⚡ أداء عالي
- **محرك C++** للعمليات الحرجة
- **تسريع CUDA** للحوسبة المتوازية
- **نماذج Julia** للحوسبة العلمية
- **معالجة متوازية** للبيانات
- **تحسين الذاكرة** والأداء

### 🗄️ قواعد بيانات متعددة
- **PostgreSQL** للبيانات العلائقية
- **MongoDB** للبيانات غير المهيكلة
- **InfluxDB** للسلاسل الزمنية
- **Redis** للتخزين المؤقت
- **Vector Database** للذكاء الاصطناعي

### 🌐 واجهات متقدمة
- **REST API** شامل مع FastAPI
- **WebSocket** للبيانات الفورية
- **React Dashboard** متطور
- **توثيق تفاعلي** مع Swagger
- **نظام مصادقة** متقدم

## 🛠️ التقنيات المستخدمة

### Backend
- **Python 3.11+** - اللغة الأساسية
- **FastAPI** - إطار عمل API
- **C++** - محرك الأداء العالي
- **Julia** - الحوسبة العلمية
- **TensorFlow/PyTorch** - التعلم الآلي
- **Celery** - المهام الخلفية

### Frontend
- **React 18** - واجهة المستخدم
- **Material-UI** - مكونات UI
- **TypeScript** - لغة البرمجة
- **WebSocket** - الاتصال الفوري
- **Chart.js/D3.js** - الرسوم البيانية

### قواعد البيانات
- **PostgreSQL 15** - قاعدة البيانات الرئيسية
- **MongoDB 7.0** - البيانات غير المهيكلة
- **InfluxDB 2.7** - السلاسل الزمنية
- **Redis 7.2** - التخزين المؤقت
- **ChromaDB** - قاعدة البيانات الشعاعية

### DevOps
- **Docker** - الحاويات
- **Docker Compose** - تنسيق الخدمات
- **Nginx** - الخادم العكسي
- **Prometheus** - المراقبة
- **Grafana** - التصور

## 📁 هيكل المشروع

```
intelligent_trading_system/
├── 🧠 ai_core/                    # نواة الذكاء الاصطناعي
│   ├── deep_learning/             # التعلم العميق
│   ├── reinforcement_learning/    # التعلم المعزز
│   ├── nlp/                       # معالجة اللغة الطبيعية
│   └── ensemble/                  # النماذج المجمعة
├── 📊 data_layer/                 # طبقة البيانات
│   ├── ingestion/                 # جمع البيانات
│   ├── processing/                # معالجة البيانات
│   ├── storage/                   # تخزين البيانات
│   └── validation/                # التحقق من البيانات
├── 🔧 trading_engine/             # محرك التداول
│   ├── strategy_engine/           # محرك الاستراتيجيات
│   ├── risk_management/           # إدارة المخاطر
│   ├── portfolio_manager/         # إدارة المحافظ
│   └── execution_engine/          # محرك التنفيذ
├── 🌐 api_layer/                  # طبقة API
│   ├── rest_api/                  # REST API
│   ├── websocket/                 # WebSocket
│   ├── graphql/                   # GraphQL
│   └── authentication/            # المصادقة
├── 🗄️ databases/                  # قواعد البيانات
│   ├── postgresql/                # PostgreSQL
│   ├── mongodb/                   # MongoDB
│   ├── influxdb/                  # InfluxDB
│   └── redis/                     # Redis
├── ⚡ high_performance/           # الأداء العالي
│   ├── cpp_engine/                # محرك C++
│   ├── julia_compute/             # حوسبة Julia
│   └── cuda_acceleration/         # تسريع CUDA
├── 🎨 frontend/                   # الواجهة الأمامية
│   ├── react_dashboard/           # لوحة React
│   ├── mobile_app/                # تطبيق الجوال
│   └── desktop_app/               # تطبيق سطح المكتب
├── 📊 monitoring/                 # المراقبة
│   ├── prometheus/                # Prometheus
│   ├── grafana/                   # Grafana
│   └── logging/                   # السجلات
├── 🐳 deployment/                 # النشر
│   ├── docker/                    # Docker
│   ├── kubernetes/                # Kubernetes
│   └── cloud/                     # السحابة
└── 📚 docs/                       # التوثيق
```

## 🚀 التشغيل السريع

### 1. التشغيل المبسط (بدون Docker)

```bash
# استنساخ المشروع
git clone <repository-url>
cd intelligent_trading_system

# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل النظام
python run_its.py
```

### 2. التشغيل الكامل (مع Docker)

```bash
# تثبيت Docker و Docker Compose
# ثم تشغيل النظام الكامل
python start_complete_system.py
```

## 🌐 الوصول للنظام

بعد التشغيل، يمكنك الوصول للنظام عبر:

- **🏠 النظام الرئيسي**: http://localhost:8000
- **📚 توثيق API**: http://localhost:8000/docs
- **🔧 API التفاعلي**: http://localhost:8000/redoc
- **📊 بيانات تجريبية**: http://localhost:8000/api/v1/demo-data
- **🎯 إشارات تجريبية**: http://localhost:8000/api/v1/demo-signals

### مع Docker (النظام الكامل):
- **🏠 النظام الرئيسي**: http://localhost:80
- **📊 واجهة التداول**: http://localhost:3001
- **📈 Grafana المراقبة**: http://localhost:3000
- **📓 Jupyter Notebook**: http://localhost:8888
- **🗄️ قواعد البيانات**:
  - PostgreSQL: localhost:5432
  - MongoDB: localhost:27017
  - InfluxDB: localhost:8086
  - Redis: localhost:6379

## 🔧 التكوين

### ملف التكوين الأساسي
```json
{
  "system": {
    "name": "Intelligent Trading System",
    "version": "1.0.0",
    "environment": "demo"
  },
  "api": {
    "host": "0.0.0.0",
    "port": 8000
  },
  "database": {
    "type": "sqlite",
    "path": "data/its_demo.db"
  },
  "trading": {
    "paper_trading": true,
    "demo_mode": true,
    "initial_balance": 100000
  }
}
```

### متغيرات البيئة (.env)
```bash
# Database Passwords
POSTGRES_PASSWORD=your_secure_password
MONGO_ROOT_PASSWORD=your_secure_password
REDIS_PASSWORD=your_secure_password

# API Keys
ALPHA_VANTAGE_API_KEY=your_api_key
BINANCE_API_KEY=your_api_key
OPENAI_API_KEY=your_api_key

# Environment
ENVIRONMENT=development
DEBUG=true
```

## 📊 استخدام النظام

### 1. الحصول على بيانات السوق
```python
import requests

# الحصول على البيانات التجريبية
response = requests.get('http://localhost:8000/api/v1/demo-data')
market_data = response.json()
print(market_data)
```

### 2. الحصول على إشارات التداول
```python
# الحصول على الإشارات التجريبية
response = requests.get('http://localhost:8000/api/v1/demo-signals')
signals = response.json()
print(signals)
```

### 3. استخدام WebSocket للبيانات الفورية
```javascript
const ws = new WebSocket('ws://localhost:8000/ws');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Real-time data:', data);
};

// الاشتراك في بيانات السوق
ws.send(JSON.stringify({
    type: 'subscribe',
    topic: 'market_data'
}));
```

## 🧪 الاختبار

```bash
# تشغيل الاختبارات
pytest tests/

# تشغيل اختبارات محددة
pytest tests/test_api.py

# تشغيل مع التغطية
pytest --cov=intelligent_trading_system tests/
```

## 📈 المراقبة والأداء

### مقاييس الأداء
- **زمن الاستجابة**: < 100ms للاستعلامات البسيطة
- **الإنتاجية**: > 1000 طلب/ثانية
- **الذاكرة**: < 2GB للنظام الأساسي
- **المعالج**: < 50% في الحالة العادية

### المراقبة
- **Prometheus**: جمع المقاييس
- **Grafana**: تصور البيانات
- **السجلات**: تتبع مفصل للأحداث
- **التنبيهات**: إشعارات فورية للمشاكل

## 🔒 الأمان

- **مصادقة JWT** للمستخدمين
- **تشفير HTTPS** للاتصالات
- **تشفير قواعد البيانات** للبيانات الحساسة
- **مراجعة الأمان** المنتظمة
- **نسخ احتياطية** آمنة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

- **البريد الإلكتروني**: <EMAIL>
- **التوثيق**: https://docs.mmai-trading.com
- **المجتمع**: https://community.mmai-trading.com
- **GitHub Issues**: https://github.com/mmai-trading/issues

## 🙏 شكر وتقدير

- **TensorFlow Team** - إطار عمل التعلم الآلي
- **FastAPI Team** - إطار عمل API السريع
- **React Team** - مكتبة واجهة المستخدم
- **PostgreSQL Team** - قاعدة البيانات الموثوقة
- **جميع المساهمين** في المشروع

---

**🧠 M&M AI Trading System** - حيث يلتقي الذكاء الاصطناعي بالتداول الذكي

*تم تطويره بـ ❤️ من فريق M&M AI*
