#!/usr/bin/env python3
"""
📊 اختبار بسيط لجلب البيانات من Yahoo Finance
Simple Yahoo Finance Data Test
"""

import yfinance as yf
import pandas as pd
import os
from datetime import datetime

def test_yahoo_finance():
    """اختبار جلب البيانات من Yahoo Finance"""
    print("🚀 بدء اختبار Yahoo Finance...")
    
    # إنشاء مجلد البيانات
    if not os.path.exists("data"):
        os.makedirs("data")
        print("✅ تم إنشاء مجلد البيانات")
    
    # رموز للاختبار
    symbols = ['AAPL', 'GOOGL', 'MSFT', 'GC=F', 'BTC-USD']
    
    print(f"\n📊 جلب بيانات {len(symbols)} رموز...")
    
    for symbol in symbols:
        try:
            print(f"\n📈 جلب بيانات {symbol}...")
            
            # جلب البيانات
            ticker = yf.Ticker(symbol)
            data = ticker.history(period="1mo")
            
            if not data.empty:
                # حفظ البيانات
                filename = f"data/{symbol.replace('=', '_').replace('-', '_')}.csv"
                data.to_csv(filename)
                
                # عرض معلومات
                latest_price = data['Close'].iloc[-1]
                print(f"  💰 آخر سعر: ${latest_price:.2f}")
                print(f"  📅 عدد السجلات: {len(data)}")
                print(f"  💾 تم الحفظ في: {filename}")
                
                # عرض أول 3 صفوف
                print(f"  📋 عينة من البيانات:")
                print(data.head(3).to_string())
                
            else:
                print(f"  ❌ لا توجد بيانات لـ {symbol}")
                
        except Exception as e:
            print(f"  ❌ خطأ في جلب {symbol}: {str(e)}")
    
    print(f"\n🎉 تم الانتهاء من الاختبار!")
    print(f"📁 تحقق من مجلد 'data' لرؤية الملفات")

if __name__ == "__main__":
    test_yahoo_finance()
