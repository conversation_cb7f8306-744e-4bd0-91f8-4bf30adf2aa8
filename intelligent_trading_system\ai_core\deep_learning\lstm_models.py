"""
🧠 نماذج LSTM المتقدمة للتنبؤ بالأسعار والتحليل الزمني
Advanced LSTM Models for Price Prediction and Time Series Analysis

يحتوي على:
- LSTM متعدد الطبقات للتنبؤ بالأسعار
- Bidirectional LSTM للتحليل المتقدم
- Attention-based LSTM للتركيز على الأنماط المهمة
- Ensemble LSTM للدقة العالية
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Model, Sequential
from tensorflow.keras.layers import (
    LSTM, Dense, Dropout, Input, Bidirectional,
    Attention, MultiHeadAttention, LayerNormalization,
    Concatenate, TimeDistributed, Conv1D, MaxPooling1D
)
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import warnings
warnings.filterwarnings('ignore')

class AdvancedLSTMPredictor:
    """نموذج LSTM متقدم للتنبؤ بالأسعار"""
    
    def __init__(self, config=None):
        self.config = config or {
            'sequence_length': 60,
            'features': ['open', 'high', 'low', 'close', 'volume'],
            'target': 'close',
            'hidden_units': [128, 64, 32],
            'dropout_rate': 0.2,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 100,
            'validation_split': 0.2,
            'early_stopping_patience': 20
        }
        
        self.model = None
        self.scaler_features = MinMaxScaler()
        self.scaler_target = MinMaxScaler()
        self.history = None
        
    def create_model(self, input_shape):
        """إنشاء نموذج LSTM متقدم"""
        
        # المدخلات
        inputs = Input(shape=input_shape, name='price_sequence')
        
        # الطبقة الأولى - LSTM ثنائي الاتجاه
        x = Bidirectional(
            LSTM(self.config['hidden_units'][0], 
                 return_sequences=True, 
                 dropout=self.config['dropout_rate'],
                 recurrent_dropout=self.config['dropout_rate']),
            name='bidirectional_lstm_1'
        )(inputs)
        
        # طبقة التطبيع
        x = LayerNormalization(name='layer_norm_1')(x)
        
        # الطبقة الثانية - LSTM مع Attention
        lstm_out = LSTM(self.config['hidden_units'][1], 
                       return_sequences=True,
                       dropout=self.config['dropout_rate'],
                       name='lstm_2')(x)
        
        # آلية الانتباه (Attention)
        attention_out = MultiHeadAttention(
            num_heads=8,
            key_dim=self.config['hidden_units'][1] // 8,
            name='multi_head_attention'
        )(lstm_out, lstm_out)
        
        # دمج LSTM مع Attention
        x = Concatenate(name='concat_lstm_attention')([lstm_out, attention_out])
        x = LayerNormalization(name='layer_norm_2')(x)
        
        # الطبقة الثالثة - LSTM نهائي
        x = LSTM(self.config['hidden_units'][2], 
                dropout=self.config['dropout_rate'],
                name='final_lstm')(x)
        
        # طبقات Dense للتنبؤ
        x = Dense(64, activation='relu', name='dense_1')(x)
        x = Dropout(self.config['dropout_rate'], name='dropout_1')(x)
        
        x = Dense(32, activation='relu', name='dense_2')(x)
        x = Dropout(self.config['dropout_rate'], name='dropout_2')(x)
        
        # طبقة الإخراج
        outputs = Dense(1, activation='linear', name='price_prediction')(x)
        
        # إنشاء النموذج
        model = Model(inputs=inputs, outputs=outputs, name='AdvancedLSTM')
        
        # تجميع النموذج
        model.compile(
            optimizer=Adam(learning_rate=self.config['learning_rate']),
            loss='mse',
            metrics=['mae', 'mape']
        )
        
        return model
    
    def prepare_data(self, data, target_column='close'):
        """تحضير البيانات للتدريب"""
        
        # اختيار الميزات
        features = self.config['features']
        if isinstance(data, pd.DataFrame):
            feature_data = data[features].values
            target_data = data[target_column].values.reshape(-1, 1)
        else:
            feature_data = data
            target_data = data[:, -1].reshape(-1, 1)
        
        # تطبيع البيانات
        feature_data_scaled = self.scaler_features.fit_transform(feature_data)
        target_data_scaled = self.scaler_target.fit_transform(target_data)
        
        # إنشاء التسلسلات
        X, y = [], []
        sequence_length = self.config['sequence_length']
        
        for i in range(sequence_length, len(feature_data_scaled)):
            X.append(feature_data_scaled[i-sequence_length:i])
            y.append(target_data_scaled[i])
        
        return np.array(X), np.array(y)
    
    def train(self, data, target_column='close', save_path=None):
        """تدريب النموذج"""
        
        print("🚀 بدء تدريب نموذج LSTM المتقدم...")
        
        # تحضير البيانات
        X, y = self.prepare_data(data, target_column)
        
        print(f"📊 شكل البيانات: X={X.shape}, y={y.shape}")
        
        # إنشاء النموذج
        self.model = self.create_model((X.shape[1], X.shape[2]))
        
        print("🏗️ بنية النموذج:")
        self.model.summary()
        
        # إعداد callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=self.config['early_stopping_patience'],
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=10,
                min_lr=1e-7,
                verbose=1
            )
        ]
        
        if save_path:
            callbacks.append(
                ModelCheckpoint(
                    save_path,
                    monitor='val_loss',
                    save_best_only=True,
                    verbose=1
                )
            )
        
        # تدريب النموذج
        self.history = self.model.fit(
            X, y,
            batch_size=self.config['batch_size'],
            epochs=self.config['epochs'],
            validation_split=self.config['validation_split'],
            callbacks=callbacks,
            verbose=1
        )
        
        print("✅ تم الانتهاء من التدريب!")
        
        return self.history
    
    def predict(self, data, steps_ahead=1):
        """التنبؤ بالأسعار المستقبلية"""
        
        if self.model is None:
            raise ValueError("يجب تدريب النموذج أولاً!")
        
        # تحضير البيانات للتنبؤ
        if isinstance(data, pd.DataFrame):
            feature_data = data[self.config['features']].values
        else:
            feature_data = data
        
        # تطبيع البيانات
        feature_data_scaled = self.scaler_features.transform(feature_data)
        
        predictions = []
        current_sequence = feature_data_scaled[-self.config['sequence_length']:]
        
        for _ in range(steps_ahead):
            # التنبؤ بالخطوة التالية
            pred_scaled = self.model.predict(
                current_sequence.reshape(1, self.config['sequence_length'], -1),
                verbose=0
            )
            
            # إلغاء التطبيع
            pred = self.scaler_target.inverse_transform(pred_scaled)[0, 0]
            predictions.append(pred)
            
            # تحديث التسلسل للتنبؤ التالي
            if steps_ahead > 1:
                # إضافة التنبؤ الجديد للتسلسل
                new_row = current_sequence[-1].copy()
                new_row[-1] = pred_scaled[0, 0]  # تحديث السعر المتنبأ به
                
                current_sequence = np.vstack([current_sequence[1:], new_row])
        
        return np.array(predictions)
    
    def evaluate(self, test_data, target_column='close'):
        """تقييم أداء النموذج"""
        
        X_test, y_test = self.prepare_data(test_data, target_column)
        
        # التنبؤ
        y_pred_scaled = self.model.predict(X_test, verbose=0)
        
        # إلغاء التطبيع
        y_test_original = self.scaler_target.inverse_transform(y_test)
        y_pred_original = self.scaler_target.inverse_transform(y_pred_scaled)
        
        # حساب المقاييس
        mse = mean_squared_error(y_test_original, y_pred_original)
        mae = mean_absolute_error(y_test_original, y_pred_original)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_test_original, y_pred_original)
        
        # حساب دقة الاتجاه
        direction_accuracy = self.calculate_direction_accuracy(
            y_test_original, y_pred_original
        )
        
        metrics = {
            'mse': mse,
            'mae': mae,
            'rmse': rmse,
            'r2_score': r2,
            'direction_accuracy': direction_accuracy
        }
        
        print("📊 مقاييس الأداء:")
        for metric, value in metrics.items():
            print(f"   {metric}: {value:.4f}")
        
        return metrics
    
    def calculate_direction_accuracy(self, y_true, y_pred):
        """حساب دقة التنبؤ بالاتجاه"""
        
        if len(y_true) < 2:
            return 0.0
        
        true_directions = np.diff(y_true.flatten()) > 0
        pred_directions = np.diff(y_pred.flatten()) > 0
        
        return np.mean(true_directions == pred_directions)
    
    def save_model(self, filepath):
        """حفظ النموذج والمعايرات"""
        
        if self.model is None:
            raise ValueError("لا يوجد نموذج للحفظ!")
        
        # حفظ النموذج
        self.model.save(f"{filepath}_model.h5")
        
        # حفظ المعايرات
        joblib.dump(self.scaler_features, f"{filepath}_scaler_features.pkl")
        joblib.dump(self.scaler_target, f"{filepath}_scaler_target.pkl")
        joblib.dump(self.config, f"{filepath}_config.pkl")
        
        print(f"💾 تم حفظ النموذج في: {filepath}")
    
    def load_model(self, filepath):
        """تحميل النموذج والمعايرات"""
        
        # تحميل النموذج
        self.model = tf.keras.models.load_model(f"{filepath}_model.h5")
        
        # تحميل المعايرات
        self.scaler_features = joblib.load(f"{filepath}_scaler_features.pkl")
        self.scaler_target = joblib.load(f"{filepath}_scaler_target.pkl")
        self.config = joblib.load(f"{filepath}_config.pkl")
        
        print(f"📂 تم تحميل النموذج من: {filepath}")

class EnsembleLSTMPredictor:
    """نموذج مجمع من عدة LSTM للدقة العالية"""
    
    def __init__(self, n_models=5, config=None):
        self.n_models = n_models
        self.models = []
        self.config = config
        
    def train(self, data, target_column='close'):
        """تدريب مجموعة من النماذج"""
        
        print(f"🚀 تدريب {self.n_models} نماذج LSTM...")
        
        for i in range(self.n_models):
            print(f"\n📈 تدريب النموذج {i+1}/{self.n_models}")
            
            # إنشاء تكوين مختلف لكل نموذج
            model_config = self.config.copy() if self.config else {}
            model_config['random_state'] = i * 42
            
            # إنشاء وتدريب النموذج
            model = AdvancedLSTMPredictor(model_config)
            model.train(data, target_column)
            
            self.models.append(model)
        
        print("✅ تم الانتهاء من تدريب جميع النماذج!")
    
    def predict(self, data, steps_ahead=1):
        """التنبؤ باستخدام جميع النماذج والحصول على المتوسط"""
        
        if not self.models:
            raise ValueError("يجب تدريب النماذج أولاً!")
        
        predictions = []
        
        for model in self.models:
            pred = model.predict(data, steps_ahead)
            predictions.append(pred)
        
        # حساب المتوسط والانحراف المعياري
        predictions = np.array(predictions)
        mean_pred = np.mean(predictions, axis=0)
        std_pred = np.std(predictions, axis=0)
        
        return {
            'prediction': mean_pred,
            'confidence': 1 / (1 + std_pred),  # الثقة عكسية للانحراف المعياري
            'individual_predictions': predictions
        }

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء بيانات تجريبية
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', periods=1000, freq='D')
    
    # محاكاة بيانات السوق
    price = 100
    prices = [price]
    
    for _ in range(999):
        change = np.random.normal(0, 0.02)
        price *= (1 + change)
        prices.append(price)
    
    data = pd.DataFrame({
        'date': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, 1000)
    })
    
    # تدريب النموذج
    model = AdvancedLSTMPredictor()
    
    # تقسيم البيانات
    train_size = int(0.8 * len(data))
    train_data = data[:train_size]
    test_data = data[train_size:]
    
    # التدريب
    model.train(train_data)
    
    # التقييم
    metrics = model.evaluate(test_data)
    
    # التنبؤ
    predictions = model.predict(test_data.tail(60), steps_ahead=5)
    print(f"\n🔮 التنبؤات للأيام الخمسة القادمة: {predictions}")
