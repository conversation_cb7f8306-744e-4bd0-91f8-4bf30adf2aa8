# 📦 متطلبات مجلد data_ingestion
# Data Ingestion Requirements

# جلب البيانات المالية
yfinance>=0.2.18
alpha-vantage>=2.3.1

# معالجة البيانات
pandas>=2.0.0
numpy>=1.24.0

# جدولة المهام
schedule>=1.2.0

# طلبات HTTP
requests>=2.31.0
urllib3>=2.0.0

# متغيرات البيئة
python-dotenv>=1.0.0

# تسجيل العمليات
logging>=*******

# التاريخ والوقت
python-dateutil>=2.8.2

# JSON والملفات
jsonschema>=4.17.0

# إدارة الأخطاء
retry>=0.9.2

# تحليل البيانات الإضافية
scipy>=1.10.0
matplotlib>=3.7.0
seaborn>=0.12.0

# APIs إضافية للبيانات المالية
polygon-api-client>=1.12.0
quandl>=3.7.0
fredapi>=0.5.0

# قواعد البيانات
psycopg2-binary>=2.9.0
pymongo>=4.3.0
redis>=4.5.0
influxdb-client>=1.36.0

# تشفير وأمان
cryptography>=41.0.0
bcrypt>=4.0.0

# اختبارات
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0

# أدوات التطوير
black>=23.0.0
flake8>=6.0.0
mypy>=1.4.0

# مراقبة الأداء
psutil>=5.9.0
memory-profiler>=0.61.0
