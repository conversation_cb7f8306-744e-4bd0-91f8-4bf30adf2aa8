#!/usr/bin/env python3
"""
⚡ Quick Technical Analysis
تحليل فني سريع

المرحلة 5: تحليل فني مبسط وسريع
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime

def analyze_stock(symbol, data_path="../data_ingestion"):
    """تحليل سهم واحد"""
    print(f"📊 Analyzing {symbol}...")
    
    try:
        # تحميل البيانات
        file_path = f"{data_path}/{symbol}.csv"
        if not os.path.exists(file_path):
            file_path = f"{data_path}/{symbol.replace('=', '_').replace('-', '_')}.csv"
        
        if not os.path.exists(file_path):
            print(f"❌ File not found for {symbol}")
            return None
        
        df = pd.read_csv(file_path)
        print(f"✅ Loaded {len(df)} records")
        
        # البيانات الحالية
        latest = df.iloc[-1]
        previous = df.iloc[-2] if len(df) > 1 else latest
        
        current_price = latest['Close']
        price_change = current_price - previous['Close']
        price_change_pct = (price_change / previous['Close']) * 100
        
        # حسابات بسيطة
        sma_5 = df['Close'].tail(5).mean()
        sma_10 = df['Close'].tail(10).mean()
        sma_20 = df['Close'].tail(20).mean() if len(df) >= 20 else sma_10
        
        # حجم التداول
        avg_volume = df['Volume'].tail(10).mean()
        volume_ratio = latest['Volume'] / avg_volume if avg_volume > 0 else 1
        
        # RSI مبسط
        gains = []
        losses = []
        for i in range(1, min(15, len(df))):
            change = df['Close'].iloc[-i] - df['Close'].iloc[-i-1]
            if change > 0:
                gains.append(change)
            else:
                losses.append(abs(change))
        
        avg_gain = np.mean(gains) if gains else 0
        avg_loss = np.mean(losses) if losses else 0.01
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        # تحليل الاتجاه
        if current_price > sma_5 > sma_10:
            trend = "صاعد"
            trend_signal = "BUY"
        elif current_price < sma_5 < sma_10:
            trend = "هابط"
            trend_signal = "SELL"
        else:
            trend = "جانبي"
            trend_signal = "HOLD"
        
        # تحليل RSI
        if rsi < 30:
            rsi_signal = "BUY - مبالغ في البيع"
        elif rsi > 70:
            rsi_signal = "SELL - مبالغ في الشراء"
        else:
            rsi_signal = "NEUTRAL"
        
        # تحليل الحجم
        if volume_ratio > 1.5:
            volume_signal = "HIGH VOLUME - نشاط قوي"
        else:
            volume_signal = "NORMAL VOLUME"
        
        # النتائج
        analysis = {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'price_data': {
                'current_price': round(current_price, 2),
                'price_change': round(price_change, 2),
                'price_change_pct': round(price_change_pct, 2),
                'volume': int(latest['Volume']),
                'volume_ratio': round(volume_ratio, 2)
            },
            'technical_indicators': {
                'sma_5': round(sma_5, 2),
                'sma_10': round(sma_10, 2),
                'sma_20': round(sma_20, 2),
                'rsi': round(rsi, 1)
            },
            'signals': {
                'trend': trend_signal,
                'rsi': rsi_signal,
                'volume': volume_signal
            },
            'analysis': {
                'trend_description': trend,
                'support_level': round(min(df['Low'].tail(10)), 2),
                'resistance_level': round(max(df['High'].tail(10)), 2)
            }
        }
        
        # تحديد الإشارة الإجمالية
        buy_signals = sum(1 for signal in analysis['signals'].values() if 'BUY' in str(signal))
        sell_signals = sum(1 for signal in analysis['signals'].values() if 'SELL' in str(signal))
        
        if buy_signals > sell_signals:
            analysis['overall_signal'] = 'BUY'
            analysis['confidence'] = min(buy_signals * 40, 100)
        elif sell_signals > buy_signals:
            analysis['overall_signal'] = 'SELL'
            analysis['confidence'] = min(sell_signals * 40, 100)
        else:
            analysis['overall_signal'] = 'HOLD'
            analysis['confidence'] = 50
        
        return analysis
        
    except Exception as e:
        print(f"❌ Error analyzing {symbol}: {str(e)}")
        return None

def display_analysis(analysis):
    """عرض نتائج التحليل"""
    if not analysis:
        return
    
    print(f"\n{'='*60}")
    print(f"📊 تحليل {analysis['symbol']}")
    print(f"{'='*60}")
    
    # بيانات السعر
    price_data = analysis['price_data']
    print(f"💰 السعر الحالي: ${price_data['current_price']}")
    print(f"📈 التغيير: ${price_data['price_change']:+.2f} ({price_data['price_change_pct']:+.2f}%)")
    print(f"📊 الحجم: {price_data['volume']:,} ({price_data['volume_ratio']:.1f}x)")
    
    # المؤشرات الفنية
    indicators = analysis['technical_indicators']
    print(f"\n📈 المؤشرات الفنية:")
    print(f"  • المتوسط 5: ${indicators['sma_5']}")
    print(f"  • المتوسط 10: ${indicators['sma_10']}")
    print(f"  • المتوسط 20: ${indicators['sma_20']}")
    print(f"  • RSI: {indicators['rsi']}")
    
    # الإشارات
    signals = analysis['signals']
    print(f"\n🚨 الإشارات:")
    print(f"  • الاتجاه: {signals['trend']}")
    print(f"  • RSI: {signals['rsi']}")
    print(f"  • الحجم: {signals['volume']}")
    
    # التحليل العام
    print(f"\n📋 التحليل العام:")
    print(f"  • الاتجاه: {analysis['analysis']['trend_description']}")
    print(f"  • الدعم: ${analysis['analysis']['support_level']}")
    print(f"  • المقاومة: ${analysis['analysis']['resistance_level']}")
    
    # التوصية النهائية
    print(f"\n🎯 التوصية النهائية: {analysis['overall_signal']}")
    print(f"🎯 مستوى الثقة: {analysis['confidence']}%")

def save_analysis(analysis, output_dir="analysis_results"):
    """حفظ نتائج التحليل"""
    if not analysis:
        return
    
    try:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        filename = f"{output_dir}/{analysis['symbol']}_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        print(f"💾 تم حفظ التحليل: {filename}")
        
    except Exception as e:
        print(f"❌ خطأ في حفظ التحليل: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("⚡ محرك التحليل الفني السريع")
    print("=" * 60)
    
    # رموز للتحليل
    symbols = ['AAPL', 'GOLD']
    
    for symbol in symbols:
        try:
            # تحليل الرمز
            analysis = analyze_stock(symbol)
            
            if analysis:
                # عرض النتائج
                display_analysis(analysis)
                
                # حفظ النتائج
                save_analysis(analysis)
            else:
                print(f"❌ فشل في تحليل {symbol}")
                
        except Exception as e:
            print(f"❌ خطأ في تحليل {symbol}: {str(e)}")
    
    print(f"\n🎉 تم الانتهاء من التحليل!")

if __name__ == "__main__":
    main()
