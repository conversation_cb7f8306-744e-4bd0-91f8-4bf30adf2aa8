"""
🗄️ Advanced Database Manager for M&M AI Trading System
مدير قواعد البيانات المتقدم لنظام التداول الذكي المتكامل

يدعم:
- PostgreSQL للبيانات العلائقية
- MongoDB للبيانات غير المهيكلة
- InfluxDB للسلاسل الزمنية
- Redis للتخزين المؤقت
- Vector Database للذكاء الاصطناعي
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
import json
import pandas as pd
import numpy as np

# Database drivers
import asyncpg
import motor.motor_asyncio
from influxdb_client.client.influxdb_client_async import InfluxDBClientAsync
from influxdb_client import Point
import redis.asyncio as redis
import pymongo
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
import psycopg2
from pymongo import MongoClient

# Vector database
try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False

# Configuration
from dataclasses import dataclass
from typing import Optional

@dataclass
class DatabaseConfig:
    """تكوين قواعد البيانات"""
    
    # PostgreSQL
    postgres_host: str = "localhost"
    postgres_port: int = 5432
    postgres_db: str = "mmai_trading"
    postgres_user: str = "postgres"
    postgres_password: str = "password"
    
    # MongoDB
    mongo_host: str = "localhost"
    mongo_port: int = 27017
    mongo_db: str = "mmai_trading"
    mongo_user: Optional[str] = None
    mongo_password: Optional[str] = None
    
    # InfluxDB
    influx_url: str = "http://localhost:8086"
    influx_token: str = "your-token"
    influx_org: str = "mmai"
    influx_bucket: str = "trading_data"
    
    # Redis
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    
    # Vector Database
    vector_db_path: str = "./vector_db"
    vector_collection: str = "trading_embeddings"

class AdvancedDatabaseManager:
    """مدير قواعد البيانات المتقدم"""
    
    def __init__(self, config: DatabaseConfig = None):
        self.config = config or DatabaseConfig()
        self.logger = self._setup_logger()
        
        # Database connections
        self.postgres_pool = None
        self.mongo_client = None
        self.mongo_db = None
        self.influx_client = None
        self.redis_client = None
        self.vector_client = None
        
        # Connection status
        self.connections = {
            'postgresql': False,
            'mongodb': False,
            'influxdb': False,
            'redis': False,
            'vector_db': False
        }
        
    def _setup_logger(self):
        """إعداد نظام السجلات"""
        logger = logging.getLogger('DatabaseManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def initialize_all(self):
        """تهيئة جميع قواعد البيانات"""
        self.logger.info("🗄️ تهيئة جميع قواعد البيانات...")
        
        tasks = [
            self.initialize_postgresql(),
            self.initialize_mongodb(),
            self.initialize_influxdb(),
            self.initialize_redis(),
            self.initialize_vector_db()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            db_name = ['PostgreSQL', 'MongoDB', 'InfluxDB', 'Redis', 'Vector DB'][i]
            if isinstance(result, Exception):
                self.logger.error(f"❌ فشل في تهيئة {db_name}: {result}")
            else:
                self.logger.info(f"✅ تم تهيئة {db_name} بنجاح")
        
        return self.get_connection_status()
    
    async def initialize_postgresql(self):
        """تهيئة PostgreSQL"""
        try:
            # Create connection pool
            dsn = f"postgresql://{self.config.postgres_user}:{self.config.postgres_password}@{self.config.postgres_host}:{self.config.postgres_port}/{self.config.postgres_db}"
            
            self.postgres_pool = await asyncpg.create_pool(
                dsn,
                min_size=5,
                max_size=20,
                command_timeout=60
            )
            
            # Test connection
            async with self.postgres_pool.acquire() as conn:
                await conn.execute('SELECT 1')
            
            self.connections['postgresql'] = True
            self.logger.info("✅ PostgreSQL connection established")
            
        except Exception as e:
            self.logger.error(f"❌ PostgreSQL connection failed: {e}")
            raise
    
    async def initialize_mongodb(self):
        """تهيئة MongoDB"""
        try:
            # Create connection string
            if self.config.mongo_user and self.config.mongo_password:
                connection_string = f"mongodb://{self.config.mongo_user}:{self.config.mongo_password}@{self.config.mongo_host}:{self.config.mongo_port}/{self.config.mongo_db}"
            else:
                connection_string = f"mongodb://{self.config.mongo_host}:{self.config.mongo_port}"
            
            # Create async client
            self.mongo_client = motor.motor_asyncio.AsyncIOMotorClient(connection_string)
            self.mongo_db = self.mongo_client[self.config.mongo_db]
            
            # Test connection
            await self.mongo_client.admin.command('ping')
            
            self.connections['mongodb'] = True
            self.logger.info("✅ MongoDB connection established")
            
        except Exception as e:
            self.logger.error(f"❌ MongoDB connection failed: {e}")
            raise
    
    async def initialize_influxdb(self):
        """تهيئة InfluxDB"""
        try:
            self.influx_client = InfluxDBClientAsync(
                url=self.config.influx_url,
                token=self.config.influx_token,
                org=self.config.influx_org
            )
            
            # Test connection
            health = await self.influx_client.health()
            if health.status != "pass":
                raise Exception(f"InfluxDB health check failed: {health.status}")
            
            self.connections['influxdb'] = True
            self.logger.info("✅ InfluxDB connection established")
            
        except Exception as e:
            self.logger.error(f"❌ InfluxDB connection failed: {e}")
            raise
    
    async def initialize_redis(self):
        """تهيئة Redis"""
        try:
            self.redis_client = redis.Redis(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                password=self.config.redis_password,
                decode_responses=True
            )
            
            # Test connection
            await self.redis_client.ping()
            
            self.connections['redis'] = True
            self.logger.info("✅ Redis connection established")
            
        except Exception as e:
            self.logger.error(f"❌ Redis connection failed: {e}")
            raise
    
    async def initialize_vector_db(self):
        """تهيئة Vector Database"""
        try:
            if not CHROMADB_AVAILABLE:
                self.logger.warning("⚠️ ChromaDB not available, skipping vector database")
                return
            
            self.vector_client = chromadb.PersistentClient(
                path=self.config.vector_db_path,
                settings=Settings(anonymized_telemetry=False)
            )
            
            # Create or get collection
            try:
                self.vector_collection = self.vector_client.create_collection(
                    name=self.config.vector_collection
                )
            except:
                self.vector_collection = self.vector_client.get_collection(
                    name=self.config.vector_collection
                )
            
            self.connections['vector_db'] = True
            self.logger.info("✅ Vector Database connection established")
            
        except Exception as e:
            self.logger.error(f"❌ Vector Database connection failed: {e}")
            raise
    
    # =====================================================
    # PostgreSQL Operations
    # =====================================================
    
    async def execute_postgres_query(self, query: str, params: tuple = None):
        """تنفيذ استعلام PostgreSQL"""
        if not self.postgres_pool:
            raise Exception("PostgreSQL not initialized")
        
        async with self.postgres_pool.acquire() as conn:
            if params:
                return await conn.fetch(query, *params)
            else:
                return await conn.fetch(query)
    
    async def insert_market_data_postgres(self, data: List[Dict]):
        """إدراج بيانات السوق في PostgreSQL"""
        if not data:
            return
        
        query = """
        INSERT INTO market_data (symbol_id, timestamp, open_price, high_price, low_price, close_price, volume, bid_price, ask_price)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ON CONFLICT (symbol_id, timestamp) DO UPDATE SET
        open_price = EXCLUDED.open_price,
        high_price = EXCLUDED.high_price,
        low_price = EXCLUDED.low_price,
        close_price = EXCLUDED.close_price,
        volume = EXCLUDED.volume,
        bid_price = EXCLUDED.bid_price,
        ask_price = EXCLUDED.ask_price
        """
        
        async with self.postgres_pool.acquire() as conn:
            async with conn.transaction():
                for item in data:
                    await conn.execute(
                        query,
                        item['symbol_id'],
                        item['timestamp'],
                        item['open'],
                        item['high'],
                        item['low'],
                        item['close'],
                        item['volume'],
                        item.get('bid'),
                        item.get('ask')
                    )
    
    async def get_user_portfolio_postgres(self, user_id: str):
        """الحصول على محفظة المستخدم من PostgreSQL"""
        query = """
        SELECT 
            ta.*,
            pp.total_value,
            pp.daily_pnl,
            pp.daily_return,
            pp.cumulative_return
        FROM trading_accounts ta
        LEFT JOIN portfolio_performance pp ON ta.id = pp.account_id 
            AND pp.date = CURRENT_DATE
        WHERE ta.user_id = $1 AND ta.is_active = true
        """
        
        return await self.execute_postgres_query(query, (user_id,))
    
    # =====================================================
    # MongoDB Operations
    # =====================================================
    
    async def insert_news_sentiment_mongo(self, news_data: Dict):
        """إدراج بيانات الأخبار والمشاعر في MongoDB"""
        if not self.mongo_db:
            raise Exception("MongoDB not initialized")
        
        collection = self.mongo_db.news_sentiment
        
        # Add timestamp if not present
        if 'created_at' not in news_data:
            news_data['created_at'] = datetime.utcnow()
        
        result = await collection.insert_one(news_data)
        return str(result.inserted_id)
    
    async def get_ai_predictions_mongo(self, symbol: str, limit: int = 100):
        """الحصول على تنبؤات الذكاء الاصطناعي من MongoDB"""
        if not self.mongo_db:
            raise Exception("MongoDB not initialized")
        
        collection = self.mongo_db.ai_predictions
        
        cursor = collection.find(
            {"symbol": symbol}
        ).sort("timestamp", -1).limit(limit)
        
        return await cursor.to_list(length=limit)
    
    async def insert_trading_strategy_mongo(self, strategy_data: Dict):
        """إدراج استراتيجية تداول في MongoDB"""
        if not self.mongo_db:
            raise Exception("MongoDB not initialized")
        
        collection = self.mongo_db.trading_strategies
        
        # Add timestamps
        strategy_data['created_at'] = datetime.utcnow()
        strategy_data['updated_at'] = datetime.utcnow()
        
        result = await collection.insert_one(strategy_data)
        return str(result.inserted_id)
    
    async def get_social_sentiment_mongo(self, symbol: str, hours: int = 24):
        """الحصول على مشاعر وسائل التواصل الاجتماعي من MongoDB"""
        if not self.mongo_db:
            raise Exception("MongoDB not initialized")
        
        collection = self.mongo_db.social_sentiment
        
        since = datetime.utcnow() - timedelta(hours=hours)
        
        cursor = collection.find({
            "symbol": symbol,
            "timestamp": {"$gte": since}
        }).sort("timestamp", -1)
        
        return await cursor.to_list(length=None)
    
    # =====================================================
    # InfluxDB Operations
    # =====================================================
    
    async def write_market_data_influx(self, data: List[Dict]):
        """كتابة بيانات السوق في InfluxDB"""
        if not self.influx_client:
            raise Exception("InfluxDB not initialized")
        
        write_api = self.influx_client.write_api()
        
        points = []
        for item in data:
            point = Point("market_data") \
                .tag("symbol", item['symbol']) \
                .field("open", float(item['open'])) \
                .field("high", float(item['high'])) \
                .field("low", float(item['low'])) \
                .field("close", float(item['close'])) \
                .field("volume", int(item['volume'])) \
                .time(item['timestamp'])
            
            if 'bid' in item:
                point = point.field("bid", float(item['bid']))
            if 'ask' in item:
                point = point.field("ask", float(item['ask']))
            
            points.append(point)
        
        await write_api.write(
            bucket=self.config.influx_bucket,
            org=self.config.influx_org,
            record=points
        )
    
    async def query_market_data_influx(self, symbol: str, timeframe: str = "1h", limit: int = 1000):
        """استعلام بيانات السوق من InfluxDB"""
        if not self.influx_client:
            raise Exception("InfluxDB not initialized")
        
        query_api = self.influx_client.query_api()
        
        query = f'''
        from(bucket: "{self.config.influx_bucket}")
        |> range(start: -30d)
        |> filter(fn: (r) => r["_measurement"] == "market_data")
        |> filter(fn: (r) => r["symbol"] == "{symbol}")
        |> aggregateWindow(every: {timeframe}, fn: last, createEmpty: false)
        |> limit(n: {limit})
        '''
        
        result = await query_api.query(query, org=self.config.influx_org)
        
        # Convert to list of dictionaries
        data = []
        for table in result:
            for record in table.records:
                data.append({
                    'timestamp': record.get_time(),
                    'symbol': record.values.get('symbol'),
                    'field': record.get_field(),
                    'value': record.get_value()
                })
        
        return data
    
    # =====================================================
    # Redis Operations
    # =====================================================
    
    async def cache_market_data_redis(self, symbol: str, data: Dict, ttl: int = 300):
        """تخزين بيانات السوق في Redis مؤقتاً"""
        if not self.redis_client:
            raise Exception("Redis not initialized")
        
        key = f"market_data:{symbol}"
        await self.redis_client.setex(key, ttl, json.dumps(data, default=str))
    
    async def get_cached_market_data_redis(self, symbol: str):
        """الحصول على بيانات السوق المخزنة مؤقتاً من Redis"""
        if not self.redis_client:
            raise Exception("Redis not initialized")
        
        key = f"market_data:{symbol}"
        data = await self.redis_client.get(key)
        
        if data:
            return json.loads(data)
        return None
    
    async def cache_ai_prediction_redis(self, symbol: str, prediction: Dict, ttl: int = 3600):
        """تخزين تنبؤ الذكاء الاصطناعي في Redis"""
        if not self.redis_client:
            raise Exception("Redis not initialized")
        
        key = f"ai_prediction:{symbol}"
        await self.redis_client.setex(key, ttl, json.dumps(prediction, default=str))
    
    async def get_trading_signals_redis(self, symbol: str = None):
        """الحصول على إشارات التداول من Redis"""
        if not self.redis_client:
            raise Exception("Redis not initialized")
        
        if symbol:
            pattern = f"trading_signal:{symbol}:*"
        else:
            pattern = "trading_signal:*"
        
        keys = await self.redis_client.keys(pattern)
        signals = []
        
        for key in keys:
            data = await self.redis_client.get(key)
            if data:
                signals.append(json.loads(data))
        
        return signals
    
    # =====================================================
    # Vector Database Operations
    # =====================================================
    
    async def add_embeddings_vector_db(self, embeddings: List[List[float]], 
                                     documents: List[str], 
                                     metadatas: List[Dict],
                                     ids: List[str]):
        """إضافة embeddings إلى Vector Database"""
        if not self.vector_collection:
            raise Exception("Vector Database not initialized")
        
        self.vector_collection.add(
            embeddings=embeddings,
            documents=documents,
            metadatas=metadatas,
            ids=ids
        )
    
    async def search_similar_vector_db(self, query_embedding: List[float], 
                                     n_results: int = 10):
        """البحث عن embeddings مشابهة في Vector Database"""
        if not self.vector_collection:
            raise Exception("Vector Database not initialized")
        
        results = self.vector_collection.query(
            query_embeddings=[query_embedding],
            n_results=n_results
        )
        
        return results
    
    # =====================================================
    # Utility Methods
    # =====================================================
    
    def get_connection_status(self):
        """الحصول على حالة الاتصالات"""
        return self.connections
    
    async def health_check(self):
        """فحص صحة جميع قواعد البيانات"""
        health_status = {}
        
        # PostgreSQL
        try:
            if self.postgres_pool:
                async with self.postgres_pool.acquire() as conn:
                    await conn.execute('SELECT 1')
                health_status['postgresql'] = 'healthy'
            else:
                health_status['postgresql'] = 'not_connected'
        except Exception as e:
            health_status['postgresql'] = f'error: {str(e)}'
        
        # MongoDB
        try:
            if self.mongo_client:
                await self.mongo_client.admin.command('ping')
                health_status['mongodb'] = 'healthy'
            else:
                health_status['mongodb'] = 'not_connected'
        except Exception as e:
            health_status['mongodb'] = f'error: {str(e)}'
        
        # InfluxDB
        try:
            if self.influx_client:
                health = await self.influx_client.health()
                health_status['influxdb'] = health.status
            else:
                health_status['influxdb'] = 'not_connected'
        except Exception as e:
            health_status['influxdb'] = f'error: {str(e)}'
        
        # Redis
        try:
            if self.redis_client:
                await self.redis_client.ping()
                health_status['redis'] = 'healthy'
            else:
                health_status['redis'] = 'not_connected'
        except Exception as e:
            health_status['redis'] = f'error: {str(e)}'
        
        # Vector DB
        try:
            if self.vector_client:
                health_status['vector_db'] = 'healthy'
            else:
                health_status['vector_db'] = 'not_connected'
        except Exception as e:
            health_status['vector_db'] = f'error: {str(e)}'
        
        return health_status
    
    async def close_all_connections(self):
        """إغلاق جميع الاتصالات"""
        self.logger.info("🔌 إغلاق جميع اتصالات قواعد البيانات...")
        
        # Close PostgreSQL
        if self.postgres_pool:
            await self.postgres_pool.close()
        
        # Close MongoDB
        if self.mongo_client:
            self.mongo_client.close()
        
        # Close InfluxDB
        if self.influx_client:
            await self.influx_client.close()
        
        # Close Redis
        if self.redis_client:
            await self.redis_client.close()
        
        self.logger.info("✅ تم إغلاق جميع الاتصالات")

# Create singleton instance
database_manager = AdvancedDatabaseManager()

# Export for use in other modules
__all__ = ['AdvancedDatabaseManager', 'DatabaseConfig', 'database_manager']
