"""
🎮 بيئة التداول للتعلم المعزز
Trading Environment for Reinforcement Learning

بيئة محاكاة متقدمة للتداول تدعم:
- تداول متعدد الأصول
- تكاليف المعاملات الواقعية
- إدارة المخاطر المتقدمة
- مقاييس الأداء الشاملة
"""

import numpy as np
import pandas as pd
import gym
from gym import spaces
from typing import Dict, List, Tuple, Optional, Any
import matplotlib.pyplot as plt
from collections import deque
import warnings
warnings.filterwarnings('ignore')

class AdvancedTradingEnvironment(gym.Env):
    """بيئة تداول متقدمة للتعلم المعزز"""
    
    metadata = {'render.modes': ['human', 'rgb_array']}
    
    def __init__(self, 
                 data: pd.DataFrame,
                 initial_balance: float = 100000,
                 transaction_cost: float = 0.001,
                 max_position: float = 1.0,
                 lookback_window: int = 30,
                 features: List[str] = None,
                 reward_function: str = 'sharpe',
                 risk_free_rate: float = 0.02):
        
        super(AdvancedTradingEnvironment, self).__init__()
        
        # إعداد البيانات
        self.data = data.copy()
        self.features = features or ['open', 'high', 'low', 'close', 'volume']
        self.lookback_window = lookback_window
        
        # إعداد التداول
        self.initial_balance = initial_balance
        self.transaction_cost = transaction_cost
        self.max_position = max_position
        self.reward_function = reward_function
        self.risk_free_rate = risk_free_rate
        
        # حالة البيئة
        self.current_step = 0
        self.balance = initial_balance
        self.position = 0.0  # -1 إلى 1 (بيع إلى شراء)
        self.portfolio_value = initial_balance
        self.total_trades = 0
        self.winning_trades = 0
        
        # تاريخ الأداء
        self.portfolio_history = []
        self.position_history = []
        self.trade_history = []
        self.reward_history = []
        
        # مساحات الحالة والعمل
        self._setup_spaces()
        
        # مؤشرات الأداء
        self.performance_metrics = {}
        
    def _setup_spaces(self):
        """إعداد مساحات الحالة والعمل"""
        
        # مساحة الحالة: بيانات السوق + معلومات المحفظة
        market_features = len(self.features)
        portfolio_features = 4  # balance, position, portfolio_value, unrealized_pnl
        
        state_dim = self.lookback_window * market_features + portfolio_features
        
        self.observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(state_dim,),
            dtype=np.float32
        )
        
        # مساحة العمل: [-1, 1] للموضع المستهدف
        self.action_space = spaces.Box(
            low=-1.0,
            high=1.0,
            shape=(1,),
            dtype=np.float32
        )
    
    def reset(self) -> np.ndarray:
        """إعادة تعيين البيئة"""
        
        self.current_step = self.lookback_window
        self.balance = self.initial_balance
        self.position = 0.0
        self.portfolio_value = self.initial_balance
        self.total_trades = 0
        self.winning_trades = 0
        
        # مسح التاريخ
        self.portfolio_history = [self.initial_balance] * self.lookback_window
        self.position_history = [0.0] * self.lookback_window
        self.trade_history = []
        self.reward_history = []
        
        return self._get_observation()
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """تنفيذ خطوة في البيئة"""
        
        # استخراج العمل
        target_position = np.clip(action[0], -1.0, 1.0)
        
        # تنفيذ التداول
        trade_info = self._execute_trade(target_position)
        
        # حساب المكافأة
        reward = self._calculate_reward()
        
        # تحديث الحالة
        self.current_step += 1
        
        # فحص انتهاء الحلقة
        done = self.current_step >= len(self.data) - 1
        
        # معلومات إضافية
        info = {
            'portfolio_value': self.portfolio_value,
            'position': self.position,
            'balance': self.balance,
            'trade_info': trade_info,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': self.winning_trades / max(1, self.total_trades)
        }
        
        # حفظ التاريخ
        self.portfolio_history.append(self.portfolio_value)
        self.position_history.append(self.position)
        self.reward_history.append(reward)
        
        # حساب مقاييس الأداء عند الانتهاء
        if done:
            self.performance_metrics = self._calculate_performance_metrics()
            info['performance_metrics'] = self.performance_metrics
        
        return self._get_observation(), reward, done, info
    
    def _execute_trade(self, target_position: float) -> Dict:
        """تنفيذ التداول"""
        
        current_price = self.data.iloc[self.current_step]['close']
        position_change = target_position - self.position
        
        trade_info = {
            'position_change': position_change,
            'price': current_price,
            'cost': 0.0,
            'executed': False
        }
        
        # تنفيذ التداول إذا كان هناك تغيير في الموضع
        if abs(position_change) > 0.001:  # حد أدنى للتداول
            
            # حساب تكلفة المعاملة
            trade_value = abs(position_change) * self.portfolio_value
            transaction_cost = trade_value * self.transaction_cost
            
            # تحديث الرصيد والموضع
            self.balance -= transaction_cost
            self.position = target_position
            self.total_trades += 1
            
            trade_info.update({
                'cost': transaction_cost,
                'executed': True,
                'trade_value': trade_value
            })
            
            # حفظ معلومات التداول
            self.trade_history.append({
                'step': self.current_step,
                'price': current_price,
                'position_change': position_change,
                'new_position': self.position,
                'cost': transaction_cost,
                'portfolio_value': self.portfolio_value
            })
        
        # تحديث قيمة المحفظة
        self._update_portfolio_value()
        
        return trade_info
    
    def _update_portfolio_value(self):
        """تحديث قيمة المحفظة"""
        
        current_price = self.data.iloc[self.current_step]['close']
        
        if self.current_step > 0:
            previous_price = self.data.iloc[self.current_step - 1]['close']
            price_change = (current_price - previous_price) / previous_price
            
            # تحديث قيمة المحفظة بناءً على الموضع
            position_pnl = self.position * self.portfolio_value * price_change
            self.portfolio_value = self.balance + (self.portfolio_value * (1 + self.position * price_change))
            
            # تحديث إحصائيات التداول الرابح
            if len(self.trade_history) > 0:
                last_trade = self.trade_history[-1]
                if self.current_step > last_trade['step']:
                    trade_pnl = (current_price - last_trade['price']) * last_trade['position_change']
                    if trade_pnl > 0:
                        self.winning_trades += 1
    
    def _calculate_reward(self) -> float:
        """حساب المكافأة"""
        
        if self.reward_function == 'returns':
            return self._returns_reward()
        elif self.reward_function == 'sharpe':
            return self._sharpe_reward()
        elif self.reward_function == 'sortino':
            return self._sortino_reward()
        elif self.reward_function == 'calmar':
            return self._calmar_reward()
        else:
            return self._custom_reward()
    
    def _returns_reward(self) -> float:
        """مكافأة بناءً على العوائد"""
        if len(self.portfolio_history) < 2:
            return 0.0
        
        return (self.portfolio_history[-1] - self.portfolio_history[-2]) / self.portfolio_history[-2]
    
    def _sharpe_reward(self) -> float:
        """مكافأة بناءً على نسبة شارب"""
        if len(self.portfolio_history) < 30:
            return self._returns_reward()
        
        returns = np.diff(self.portfolio_history[-30:]) / self.portfolio_history[-31:-1]
        excess_returns = returns - self.risk_free_rate / 252
        
        if np.std(excess_returns) == 0:
            return 0.0
        
        return np.mean(excess_returns) / np.std(excess_returns)
    
    def _sortino_reward(self) -> float:
        """مكافأة بناءً على نسبة سورتينو"""
        if len(self.portfolio_history) < 30:
            return self._returns_reward()
        
        returns = np.diff(self.portfolio_history[-30:]) / self.portfolio_history[-31:-1]
        excess_returns = returns - self.risk_free_rate / 252
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0 or np.std(downside_returns) == 0:
            return np.mean(excess_returns)
        
        return np.mean(excess_returns) / np.std(downside_returns)
    
    def _calmar_reward(self) -> float:
        """مكافأة بناءً على نسبة كالمار"""
        if len(self.portfolio_history) < 30:
            return self._returns_reward()
        
        values = np.array(self.portfolio_history[-30:])
        returns = np.diff(values) / values[:-1]
        
        # حساب أقصى انخفاض
        cumulative = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = np.min(drawdown)
        
        if max_drawdown == 0:
            return np.mean(returns)
        
        annual_return = np.mean(returns) * 252
        return annual_return / abs(max_drawdown)
    
    def _custom_reward(self) -> float:
        """مكافأة مخصصة متقدمة"""
        
        # مكونات المكافأة
        returns_component = self._returns_reward()
        
        # مكافأة للاستقرار (تقليل التقلبات)
        if len(self.portfolio_history) >= 10:
            recent_returns = np.diff(self.portfolio_history[-10:]) / self.portfolio_history[-11:-1]
            stability_bonus = -np.std(recent_returns)
        else:
            stability_bonus = 0
        
        # عقوبة للتداول المفرط
        trading_penalty = -0.001 * (self.total_trades / max(1, self.current_step))
        
        # مكافأة لمعدل الفوز
        win_rate_bonus = 0.01 * (self.winning_trades / max(1, self.total_trades))
        
        return returns_component + 0.1 * stability_bonus + trading_penalty + win_rate_bonus
    
    def _get_observation(self) -> np.ndarray:
        """الحصول على الحالة الحالية"""
        
        # بيانات السوق للنافذة الزمنية
        start_idx = max(0, self.current_step - self.lookback_window)
        end_idx = self.current_step
        
        market_data = self.data.iloc[start_idx:end_idx][self.features].values
        
        # تطبيع البيانات
        if len(market_data) > 1:
            market_data = (market_data - np.mean(market_data, axis=0)) / (np.std(market_data, axis=0) + 1e-8)
        
        # إضافة padding إذا لزم الأمر
        if len(market_data) < self.lookback_window:
            padding = np.zeros((self.lookback_window - len(market_data), len(self.features)))
            market_data = np.vstack([padding, market_data])
        
        # تسطيح بيانات السوق
        market_features = market_data.flatten()
        
        # معلومات المحفظة
        portfolio_features = np.array([
            self.balance / self.initial_balance,  # رصيد نسبي
            self.position,  # الموضع الحالي
            self.portfolio_value / self.initial_balance,  # قيمة المحفظة النسبية
            (self.portfolio_value - self.initial_balance) / self.initial_balance  # الربح/الخسارة النسبية
        ])
        
        # دمج جميع الميزات
        observation = np.concatenate([market_features, portfolio_features])
        
        return observation.astype(np.float32)
    
    def _calculate_performance_metrics(self) -> Dict:
        """حساب مقاييس الأداء الشاملة"""
        
        portfolio_values = np.array(self.portfolio_history)
        returns = np.diff(portfolio_values) / portfolio_values[:-1]
        
        # العوائد الأساسية
        total_return = (portfolio_values[-1] - portfolio_values[0]) / portfolio_values[0]
        annual_return = total_return * (252 / len(returns))
        
        # المخاطر
        volatility = np.std(returns) * np.sqrt(252)
        
        # أقصى انخفاض
        cumulative = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = np.min(drawdown)
        
        # نسب الأداء
        excess_returns = returns - self.risk_free_rate / 252
        sharpe_ratio = np.mean(excess_returns) / np.std(returns) if np.std(returns) > 0 else 0
        
        downside_returns = returns[returns < 0]
        sortino_ratio = np.mean(excess_returns) / np.std(downside_returns) if len(downside_returns) > 0 else 0
        
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # إحصائيات التداول
        win_rate = self.winning_trades / max(1, self.total_trades)
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': win_rate,
            'final_portfolio_value': portfolio_values[-1]
        }
    
    def render(self, mode='human'):
        """عرض حالة البيئة"""
        
        if mode == 'human':
            print(f"الخطوة: {self.current_step}")
            print(f"قيمة المحفظة: ${self.portfolio_value:,.2f}")
            print(f"الموضع: {self.position:.3f}")
            print(f"إجمالي الصفقات: {self.total_trades}")
            print(f"معدل الفوز: {self.winning_trades/max(1, self.total_trades):.2%}")
            print("-" * 40)
    
    def plot_performance(self):
        """رسم أداء المحفظة"""
        
        fig, axes = plt.subplots(3, 1, figsize=(12, 10))
        
        # قيمة المحفظة
        axes[0].plot(self.portfolio_history, label='قيمة المحفظة')
        axes[0].axhline(y=self.initial_balance, color='r', linestyle='--', label='الرصيد الأولي')
        axes[0].set_title('تطور قيمة المحفظة')
        axes[0].set_ylabel('القيمة ($)')
        axes[0].legend()
        axes[0].grid(True)
        
        # المواضع
        axes[1].plot(self.position_history, label='الموضع', color='orange')
        axes[1].axhline(y=0, color='k', linestyle='-', alpha=0.3)
        axes[1].set_title('تاريخ المواضع')
        axes[1].set_ylabel('الموضع')
        axes[1].legend()
        axes[1].grid(True)
        
        # المكافآت
        if self.reward_history:
            axes[2].plot(self.reward_history, label='المكافآت', color='green')
            axes[2].axhline(y=0, color='k', linestyle='-', alpha=0.3)
            axes[2].set_title('تاريخ المكافآت')
            axes[2].set_xlabel('الخطوة')
            axes[2].set_ylabel('المكافأة')
            axes[2].legend()
            axes[2].grid(True)
        
        plt.tight_layout()
        plt.show()

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء بيانات تجريبية
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', periods=1000, freq='D')
    
    price = 100
    prices = [price]
    
    for _ in range(999):
        change = np.random.normal(0, 0.02)
        price *= (1 + change)
        prices.append(price)
    
    data = pd.DataFrame({
        'date': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, 1000)
    })
    
    # إنشاء البيئة
    env = AdvancedTradingEnvironment(data)
    
    # اختبار البيئة
    obs = env.reset()
    print(f"شكل الحالة: {obs.shape}")
    
    # تشغيل حلقة تجريبية
    for i in range(100):
        action = env.action_space.sample()  # عمل عشوائي
        obs, reward, done, info = env.step(action)
        
        if i % 20 == 0:
            env.render()
        
        if done:
            break
    
    # عرض مقاييس الأداء
    print("\n📊 مقاييس الأداء النهائية:")
    for metric, value in env.performance_metrics.items():
        print(f"   {metric}: {value}")
    
    # رسم الأداء
    env.plot_performance()
