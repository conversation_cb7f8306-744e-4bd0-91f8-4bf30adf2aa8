/* ===== إعدادات عامة ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00d4ff;
    --secondary-color: #0099cc;
    --accent-color: #ff6b35;
    --dark-bg: #0a0e1a;
    --darker-bg: #050810;
    --text-light: #ffffff;
    --text-secondary: #b0b0b0;
    --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    --gradient-secondary: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
    --water-blue: #1e3a8a;
    --water-light: #3b82f6;
    --glow-color: rgba(0, 212, 255, 0.5);
}

body {
    font-family: 'Cairo', sans-serif;
    background: var(--dark-bg);
    color: var(--text-light);
    overflow-x: hidden;
    line-height: 1.6;
}

/* ===== خلفية الجسيمات المائية ===== */
#particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: radial-gradient(ellipse at center, var(--water-blue) 0%, var(--darker-bg) 70%);
}

#particles-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="water" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%2300d4ff" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23water)"/></svg>');
    animation: waterFlow 20s linear infinite;
}

@keyframes waterFlow {
    0% { transform: translateY(0) translateX(0); }
    25% { transform: translateY(-10px) translateX(5px); }
    50% { transform: translateY(-5px) translateX(-3px); }
    75% { transform: translateY(-15px) translateX(8px); }
    100% { transform: translateY(0) translateX(0); }
}

/* ===== شريط التنقل ===== */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 14, 26, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo-container {
    position: relative;
    display: flex;
    align-items: center;
}

.logo-text {
    font-size: 1.8rem;
    font-weight: 900;
    position: relative;
    z-index: 2;
}

.logo-ai {
    color: var(--primary-color);
    text-shadow: 0 0 20px var(--glow-color);
    position: relative;
}

.logo-ai::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: logoGlow 3s ease-in-out infinite alternate;
}

.logo-trading {
    color: var(--text-light);
    margin-right: 0.5rem;
    font-weight: 600;
}

.logo-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, var(--glow-color) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
    z-index: 1;
}

@keyframes logoGlow {
    0% { filter: brightness(1) saturate(1); }
    100% { filter: brightness(1.3) saturate(1.5); }
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.5; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 1rem;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::before {
    width: 100%;
}

.nav-link:hover {
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--glow-color);
}

/* ===== القسم الرئيسي ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    padding: 8rem 2rem 4rem;
    overflow: hidden;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    width: 100%;
}

.hero-text {
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.title-line {
    display: block;
    color: var(--text-secondary);
    font-size: 2.5rem;
    font-weight: 400;
}

.title-highlight {
    display: block;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px var(--glow-color);
    animation: titleGlow 4s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { filter: brightness(1); }
    100% { filter: brightness(1.5); }
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 212, 255, 0.5);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

/* ===== الشعار الرئيسي المائي ===== */
.main-logo {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.logo-ai-large {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
    position: relative;
}

.letter-a, .letter-i {
    position: relative;
    width: 120px;
    height: 150px;
}

.letter-a .line {
    position: absolute;
    background: var(--gradient-primary);
    border-radius: 3px;
    box-shadow: 0 0 20px var(--glow-color);
}

.letter-a .line-1 {
    width: 8px;
    height: 120px;
    left: 20px;
    top: 30px;
    transform: rotate(-15deg);
    animation: lineGlow 3s ease-in-out infinite alternate;
}

.letter-a .line-2 {
    width: 8px;
    height: 120px;
    right: 20px;
    top: 30px;
    transform: rotate(15deg);
    animation: lineGlow 3s ease-in-out infinite alternate 0.5s;
}

.letter-a .line-3 {
    width: 60px;
    height: 8px;
    left: 30px;
    top: 90px;
    animation: lineGlow 3s ease-in-out infinite alternate 1s;
}

.letter-i .line-vertical {
    width: 8px;
    height: 120px;
    left: 50%;
    top: 30px;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    border-radius: 3px;
    box-shadow: 0 0 20px var(--glow-color);
    animation: lineGlow 3s ease-in-out infinite alternate 1.5s;
}

.letter-i::before {
    content: '';
    position: absolute;
    width: 15px;
    height: 15px;
    background: var(--gradient-primary);
    border-radius: 50%;
    left: 50%;
    top: 10px;
    transform: translateX(-50%);
    box-shadow: 0 0 15px var(--glow-color);
    animation: dotPulse 2s ease-in-out infinite;
}

@keyframes lineGlow {
    0% { 
        box-shadow: 0 0 20px var(--glow-color);
        filter: brightness(1);
    }
    100% { 
        box-shadow: 0 0 40px var(--glow-color), 0 0 60px var(--primary-color);
        filter: brightness(1.5);
    }
}

@keyframes dotPulse {
    0%, 100% { transform: translateX(-50%) scale(1); }
    50% { transform: translateX(-50%) scale(1.3); }
}

.dots-pattern {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 30%, var(--primary-color) 2px, transparent 2px),
        radial-gradient(circle at 80% 70%, var(--primary-color) 1px, transparent 1px),
        radial-gradient(circle at 40% 80%, var(--primary-color) 1.5px, transparent 1.5px);
    background-size: 30px 30px, 25px 25px, 35px 35px;
    opacity: 0.6;
    animation: dotsFloat 8s ease-in-out infinite;
}

@keyframes dotsFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(2deg); }
    50% { transform: translateY(-5px) rotate(-1deg); }
    75% { transform: translateY(-15px) rotate(3deg); }
}

.logo-trading-large {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-light);
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
    letter-spacing: 0.2em;
    animation: tradingGlow 4s ease-in-out infinite alternate;
}

@keyframes tradingGlow {
    0% { 
        text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
        transform: scale(1);
    }
    100% { 
        text-shadow: 0 0 50px rgba(0, 212, 255, 0.8), 0 0 70px rgba(0, 212, 255, 0.6);
        transform: scale(1.05);
    }
}

.logo-effects {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    pointer-events: none;
}

.water-ripple {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0;
    animation: ripple 4s ease-out infinite;
}

.water-ripple::before,
.water-ripple::after {
    content: '';
    position: absolute;
    width: 120%;
    height: 120%;
    border: 1px solid var(--primary-color);
    border-radius: 50%;
    top: -10%;
    left: -10%;
    opacity: 0;
    animation: ripple 4s ease-out infinite 1s;
}

.water-ripple::after {
    animation-delay: 2s;
}

@keyframes ripple {
    0% {
        transform: scale(0.5);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

.glow-effect {
    position: absolute;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, var(--glow-color) 0%, transparent 70%);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: glowPulse 3s ease-in-out infinite;
}

@keyframes glowPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.6; }
    50% { transform: translate(-50%, -50%) scale(1.5); opacity: 0.9; }
}

/* ===== الموجات المائية ===== */
.water-waves {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(0, 212, 255, 0.1) 25%, 
        rgba(0, 212, 255, 0.3) 50%, 
        rgba(0, 212, 255, 0.1) 75%, 
        transparent 100%);
    border-radius: 50% 50% 0 0;
}

.wave-1 {
    animation: waveMove 8s ease-in-out infinite;
    opacity: 0.8;
}

.wave-2 {
    animation: waveMove 6s ease-in-out infinite reverse;
    opacity: 0.6;
    height: 80px;
}

.wave-3 {
    animation: waveMove 10s ease-in-out infinite;
    opacity: 0.4;
    height: 60px;
}

@keyframes waveMove {
    0%, 100% { transform: translateX(-50%) translateY(0); }
    50% { transform: translateX(-50%) translateY(-20px); }
}

/* ===== قسم المميزات ===== */
.features {
    padding: 8rem 2rem;
    background: linear-gradient(135deg, var(--darker-bg) 0%, var(--dark-bg) 100%);
    position: relative;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    text-align: center;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 4rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    position: relative;
    z-index: 2;
}

.feature-icon::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 50%;
    filter: blur(20px);
    opacity: 0.5;
    z-index: -1;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== قسم الروبوت ===== */
.robot-section {
    padding: 8rem 2rem;
    background: var(--dark-bg);
    position: relative;
}

.robot-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

/* تصميم الروبوت */
.robot {
    position: relative;
    width: 300px;
    height: 400px;
    margin: 0 auto;
    animation: robotFloat 6s ease-in-out infinite;
}

@keyframes robotFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(1deg); }
    50% { transform: translateY(-5px) rotate(0deg); }
    75% { transform: translateY(-15px) rotate(-1deg); }
}

.robot-head {
    width: 120px;
    height: 120px;
    background: linear-gradient(145deg, #2a3f5f, #1a2332);
    border-radius: 20px;
    margin: 0 auto;
    position: relative;
    border: 3px solid var(--primary-color);
    box-shadow:
        0 0 30px var(--glow-color),
        inset 0 0 20px rgba(0, 212, 255, 0.1);
}

.robot-eyes {
    display: flex;
    justify-content: space-between;
    padding: 25px 20px 0;
}

.eye {
    width: 25px;
    height: 25px;
    background: var(--primary-color);
    border-radius: 50%;
    position: relative;
    box-shadow: 0 0 15px var(--glow-color);
    animation: eyeBlink 4s ease-in-out infinite;
}

.pupil {
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: eyeMove 3s ease-in-out infinite;
}

@keyframes eyeBlink {
    0%, 90%, 100% { transform: scaleY(1); }
    95% { transform: scaleY(0.1); }
}

@keyframes eyeMove {
    0%, 100% { transform: translate(-50%, -50%); }
    25% { transform: translate(-30%, -50%); }
    50% { transform: translate(-50%, -30%); }
    75% { transform: translate(-70%, -50%); }
}

.robot-mouth {
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 20px;
    border: 2px solid var(--primary-color);
    border-top: none;
    border-radius: 0 0 20px 20px;
    animation: mouthTalk 2s ease-in-out infinite;
}

@keyframes mouthTalk {
    0%, 100% { height: 20px; }
    50% { height: 10px; }
}

.robot-antenna {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
}

.antenna-base {
    width: 4px;
    height: 20px;
    background: var(--primary-color);
    margin: 0 auto;
    box-shadow: 0 0 10px var(--glow-color);
}

.antenna-tip {
    width: 12px;
    height: 12px;
    background: var(--accent-color);
    border-radius: 50%;
    margin: 0 auto;
    box-shadow: 0 0 15px var(--accent-color);
    animation: antennaPulse 1.5s ease-in-out infinite;
}

@keyframes antennaPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.3); }
}

.robot-body {
    width: 150px;
    height: 180px;
    background: linear-gradient(145deg, #2a3f5f, #1a2332);
    border-radius: 15px;
    margin: 20px auto;
    border: 2px solid var(--primary-color);
    position: relative;
    box-shadow:
        0 0 25px var(--glow-color),
        inset 0 0 15px rgba(0, 212, 255, 0.1);
}

.body-panel {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 120px;
    background: rgba(0, 212, 255, 0.1);
    border-radius: 10px;
    border: 1px solid var(--primary-color);
}

.panel-light {
    width: 60px;
    height: 8px;
    background: var(--gradient-primary);
    border-radius: 4px;
    margin: 15px auto;
    animation: panelGlow 2s ease-in-out infinite alternate;
}

@keyframes panelGlow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

.panel-buttons {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
}

.button {
    width: 15px;
    height: 15px;
    background: var(--accent-color);
    border-radius: 50%;
    box-shadow: 0 0 10px var(--accent-color);
    animation: buttonBlink 3s ease-in-out infinite;
}

.button:nth-child(2) { animation-delay: 1s; }
.button:nth-child(3) { animation-delay: 2s; }

@keyframes buttonBlink {
    0%, 80%, 100% { opacity: 0.3; }
    90% { opacity: 1; }
}

.robot-arms {
    position: absolute;
    top: 140px;
    width: 100%;
}

.arm {
    width: 20px;
    height: 80px;
    background: linear-gradient(145deg, #2a3f5f, #1a2332);
    border-radius: 10px;
    border: 2px solid var(--primary-color);
    position: absolute;
}

.left-arm {
    left: -30px;
    animation: leftArmMove 4s ease-in-out infinite;
}

.right-arm {
    right: -30px;
    animation: rightArmMove 4s ease-in-out infinite;
}

@keyframes leftArmMove {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-15deg); }
    75% { transform: rotate(10deg); }
}

@keyframes rightArmMove {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(15deg); }
    75% { transform: rotate(-10deg); }
}

/* فقاعات التفكير */
.thinking-bubbles {
    position: absolute;
    top: -80px;
    right: -50px;
    opacity: 0;
    animation: thinkingAppear 8s ease-in-out infinite;
}

@keyframes thinkingAppear {
    0%, 70%, 100% { opacity: 0; transform: translateY(20px); }
    75%, 95% { opacity: 1; transform: translateY(0); }
}

.bubble {
    position: absolute;
    font-size: 1.5rem;
    animation: bubbleFloat 2s ease-in-out infinite;
}

.bubble-1 {
    top: 0;
    right: 0;
    animation-delay: 0s;
}

.bubble-2 {
    top: -20px;
    right: 30px;
    animation-delay: 0.5s;
}

.bubble-3 {
    top: -40px;
    right: 60px;
    animation-delay: 1s;
}

@keyframes bubbleFloat {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-10px) scale(1.1); }
}

/* ===== منطقة المحادثة ===== */
.chat-area {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 20px;
    overflow: hidden;
    height: 500px;
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: var(--gradient-primary);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h3 {
    color: white;
    font-size: 1.3rem;
    font-weight: 600;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-size: 0.9rem;
}

.status-dot {
    width: 10px;
    height: 10px;
    background: #4ade80;
    border-radius: 50%;
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chat-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    animation: messageSlide 0.5s ease-out;
}

@keyframes messageSlide {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-content {
    background: var(--gradient-primary);
    color: white;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: var(--gradient-primary);
}

.message-content {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 20px;
    max-width: 70%;
    line-height: 1.5;
    border: 1px solid rgba(0, 212, 255, 0.2);
}

.chat-input {
    padding: 1.5rem;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    gap: 1rem;
    align-items: center;
}

.chat-input input {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 25px;
    padding: 1rem 1.5rem;
    color: var(--text-light);
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
}

.chat-input input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.chat-input input::placeholder {
    color: var(--text-secondary);
}

.chat-input button {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-input button:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
}

/* ===== التذييل ===== */
.footer {
    background: var(--darker-bg);
    padding: 4rem 2rem 2rem;
    border-top: 1px solid rgba(0, 212, 255, 0.2);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    align-items: center;
    margin-bottom: 2rem;
}

.footer-logo .logo-container {
    justify-content: flex-start;
}

.footer-links {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-social {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-social a:hover {
    background: var(--gradient-primary);
    color: white;
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
}

/* ===== الاستجابة للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
        flex-direction: column;
        gap: 4px;
        cursor: pointer;
    }

    .hamburger span {
        width: 25px;
        height: 3px;
        background: var(--primary-color);
        border-radius: 2px;
        transition: all 0.3s ease;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .title-line {
        font-size: 2rem;
    }

    .robot-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .robot {
        width: 250px;
        height: 350px;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .footer-social {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 6rem 1rem 2rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .title-line {
        font-size: 1.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
    }

    .main-logo {
        transform: scale(0.8);
    }

    .robot {
        transform: scale(0.8);
    }

    .chat-area {
        height: 400px;
    }
}

/* ===== تأثيرات إضافية ===== */
.robot.thinking .thinking-bubbles {
    animation: thinkingActive 3s ease-in-out infinite;
}

@keyframes thinkingActive {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.robot.thinking .robot-head {
    animation: headThinking 2s ease-in-out infinite;
}

@keyframes headThinking {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-2deg); }
    75% { transform: rotate(2deg); }
}

/* تأثير الكتابة */
.typing-indicator {
    display: flex;
    gap: 4px;
    padding: 1rem 1.5rem;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: typingDot 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typingDot {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-10px); }
}
