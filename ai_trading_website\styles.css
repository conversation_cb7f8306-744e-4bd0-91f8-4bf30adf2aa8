/* ===== إعدادات عامة ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00d4ff;
    --secondary-color: #0099cc;
    --accent-color: #ff6b35;
    --dark-bg: #0a0e1a;
    --darker-bg: #050810;
    --text-light: #ffffff;
    --text-secondary: #b0b0b0;
    --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    --gradient-secondary: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
    --water-blue: #1e3a8a;
    --water-light: #3b82f6;
    --glow-color: rgba(0, 212, 255, 0.5);
}

body {
    font-family: 'Cairo', sans-serif;
    background: var(--dark-bg);
    color: var(--text-light);
    overflow-x: hidden;
    line-height: 1.6;
}

/* ===== خلفية الجسيمات المائية ===== */
#particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: radial-gradient(ellipse at center, var(--water-blue) 0%, var(--darker-bg) 70%);
}

#particles-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="water" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%2300d4ff" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23water)"/></svg>');
    animation: waterFlow 20s linear infinite;
}

@keyframes waterFlow {
    0% { transform: translateY(0) translateX(0); }
    25% { transform: translateY(-10px) translateX(5px); }
    50% { transform: translateY(-5px) translateX(-3px); }
    75% { transform: translateY(-15px) translateX(8px); }
    100% { transform: translateY(0) translateX(0); }
}

/* ===== شريط التنقل ===== */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 14, 26, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo-container {
    position: relative;
    display: flex;
    align-items: center;
}

.logo-text {
    font-size: 1.8rem;
    font-weight: 900;
    position: relative;
    z-index: 2;
}

.logo-ai {
    color: var(--primary-color);
    text-shadow: 0 0 20px var(--glow-color);
    position: relative;
}

.logo-ai::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: logoGlow 3s ease-in-out infinite alternate;
}

.logo-trading {
    color: var(--text-light);
    margin-right: 0.5rem;
    font-weight: 600;
}

.logo-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, var(--glow-color) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
    z-index: 1;
}

@keyframes logoGlow {
    0% { filter: brightness(1) saturate(1); }
    100% { filter: brightness(1.3) saturate(1.5); }
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.5; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 1rem;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::before {
    width: 100%;
}

.nav-link:hover {
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--glow-color);
}

/* ===== القسم الرئيسي ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    padding: 8rem 2rem 4rem;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 10% 20%, var(--primary-color) 2px, transparent 2px),
        radial-gradient(circle at 80% 80%, var(--primary-color) 1px, transparent 1px),
        radial-gradient(circle at 40% 40%, var(--primary-color) 1.5px, transparent 1.5px),
        radial-gradient(circle at 90% 10%, var(--primary-color) 1px, transparent 1px),
        radial-gradient(circle at 20% 90%, var(--primary-color) 2px, transparent 2px),
        radial-gradient(circle at 60% 30%, var(--primary-color) 1px, transparent 1px),
        radial-gradient(circle at 30% 70%, var(--primary-color) 1.5px, transparent 1.5px),
        radial-gradient(circle at 70% 60%, var(--primary-color) 1px, transparent 1px);
    background-size:
        100px 100px,
        150px 150px,
        120px 120px,
        80px 80px,
        110px 110px,
        90px 90px,
        130px 130px,
        70px 70px;
    opacity: 0.6;
    animation: backgroundDots 20s ease-in-out infinite;
    z-index: 1;
}

@keyframes backgroundDots {
    0%, 100% {
        transform: translateX(0) translateY(0);
        opacity: 0.6;
    }
    25% {
        transform: translateX(20px) translateY(-10px);
        opacity: 0.8;
    }
    50% {
        transform: translateX(-10px) translateY(15px);
        opacity: 0.4;
    }
    75% {
        transform: translateX(15px) translateY(-20px);
        opacity: 0.7;
    }
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    width: 100%;
}

.hero-text {
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.title-line {
    display: block;
    color: var(--text-secondary);
    font-size: 2.5rem;
    font-weight: 400;
}

.title-highlight {
    display: block;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px var(--glow-color);
    animation: titleGlow 4s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { filter: brightness(1); }
    100% { filter: brightness(1.5); }
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 1.2rem 2.5rem;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 150px;
    text-align: center;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.4);
    border: 2px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 212, 255, 0.6);
    filter: brightness(1.1);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: 0 5px 20px rgba(0, 212, 255, 0.2);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.4);
}

/* ===== الشعار الرئيسي المائي ===== */
.main-logo {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.logo-circle {
    position: relative;
    width: 500px;
    height: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: radial-gradient(circle at center, rgba(0, 212, 255, 0.15) 0%, rgba(0, 212, 255, 0.08) 50%, transparent 100%);
    animation: logoFloat 6s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(1deg); }
    50% { transform: translateY(-5px) rotate(0deg); }
    75% { transform: translateY(-15px) rotate(-1deg); }
}

.circle-border {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0.8;
    animation: rotateBorder 20s linear infinite;
}

.circle-border-2 {
    position: absolute;
    width: 120%;
    height: 120%;
    border: 1px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0.4;
    animation: rotateBorder 30s linear infinite reverse;
}

.circle-border-3 {
    position: absolute;
    width: 140%;
    height: 140%;
    border: 1px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0.2;
    animation: rotateBorder 40s linear infinite;
}

@keyframes rotateBorder {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.logo-ai-large {
    font-size: 8rem;
    font-weight: 900;
    color: var(--primary-color);
    text-shadow:
        0 0 30px var(--glow-color),
        0 0 60px var(--glow-color),
        0 0 90px var(--glow-color);
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 3;
    animation: aiGlow 3s ease-in-out infinite alternate;
    letter-spacing: 0.1em;
}

@keyframes aiGlow {
    0% {
        text-shadow:
            0 0 20px var(--glow-color),
            0 0 40px var(--glow-color),
            0 0 60px var(--glow-color);
        transform: scale(1);
    }
    100% {
        text-shadow:
            0 0 30px var(--glow-color),
            0 0 60px var(--glow-color),
            0 0 90px var(--glow-color),
            0 0 120px var(--primary-color);
        transform: scale(1.05);
    }
}

.logo-trading-large {
    font-size: 2.8rem;
    font-weight: 700;
    color: var(--text-light);
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
    letter-spacing: 0.4em;
    position: relative;
    z-index: 3;
    animation: tradingGlow 4s ease-in-out infinite alternate;
}

@keyframes tradingGlow {
    0% {
        text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
        transform: scale(1);
    }
    100% {
        text-shadow: 0 0 50px rgba(0, 212, 255, 0.8), 0 0 70px rgba(0, 212, 255, 0.6);
        transform: scale(1.02);
    }
}

.dots-grid {
    position: absolute;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 20px;
    padding: 40px;
    z-index: 1;
}

.dot {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 0 0 10px var(--glow-color);
    animation: dotPulse 2s ease-in-out infinite;
    opacity: 0.7;
}

.dot:nth-child(1) { animation-delay: 0s; }
.dot:nth-child(2) { animation-delay: 0.2s; }
.dot:nth-child(3) { animation-delay: 0.4s; }
.dot:nth-child(4) { animation-delay: 0.6s; }
.dot:nth-child(5) { animation-delay: 0.8s; }
.dot:nth-child(6) { animation-delay: 1s; }
.dot:nth-child(7) { animation-delay: 1.2s; }
.dot:nth-child(8) { animation-delay: 1.4s; }
.dot:nth-child(9) { animation-delay: 1.6s; }
.dot:nth-child(10) { animation-delay: 1.8s; }
.dot:nth-child(11) { animation-delay: 2s; }
.dot:nth-child(12) { animation-delay: 2.2s; }

@keyframes dotPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.7;
        box-shadow: 0 0 10px var(--glow-color);
    }
    50% {
        transform: scale(1.5);
        opacity: 1;
        box-shadow: 0 0 20px var(--glow-color), 0 0 30px var(--primary-color);
    }
}

.logo-effects {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    pointer-events: none;
}

.water-ripple {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0;
    animation: ripple 4s ease-out infinite;
}

.water-ripple::before,
.water-ripple::after {
    content: '';
    position: absolute;
    width: 120%;
    height: 120%;
    border: 1px solid var(--primary-color);
    border-radius: 50%;
    top: -10%;
    left: -10%;
    opacity: 0;
    animation: ripple 4s ease-out infinite 1s;
}

.water-ripple::after {
    animation-delay: 2s;
}

@keyframes ripple {
    0% {
        transform: scale(0.5);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

.glow-effect {
    position: absolute;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, var(--glow-color) 0%, transparent 70%);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: glowPulse 3s ease-in-out infinite;
}

@keyframes glowPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.6; }
    50% { transform: translate(-50%, -50%) scale(1.5); opacity: 0.9; }
}

/* ===== الموجات المائية ===== */
.water-waves {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(0, 212, 255, 0.1) 25%, 
        rgba(0, 212, 255, 0.3) 50%, 
        rgba(0, 212, 255, 0.1) 75%, 
        transparent 100%);
    border-radius: 50% 50% 0 0;
}

.wave-1 {
    animation: waveMove 8s ease-in-out infinite;
    opacity: 0.8;
}

.wave-2 {
    animation: waveMove 6s ease-in-out infinite reverse;
    opacity: 0.6;
    height: 80px;
}

.wave-3 {
    animation: waveMove 10s ease-in-out infinite;
    opacity: 0.4;
    height: 60px;
}

@keyframes waveMove {
    0%, 100% { transform: translateX(-50%) translateY(0); }
    50% { transform: translateX(-50%) translateY(-20px); }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    text-align: center;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 4rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== قسم الروبوت ===== */
.robot-section {
    padding: 4rem 2rem 8rem;
    background: var(--dark-bg);
    position: relative;
}

.robot-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

/* تصميم الروبوت */
.robot {
    position: relative;
    width: 350px;
    height: 450px;
    margin: 0 auto;
    animation: robotFloat 8s ease-in-out infinite;
    filter: drop-shadow(0 20px 40px rgba(0, 212, 255, 0.3));
}

@keyframes robotFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(-15px) rotate(0.5deg); }
    50% { transform: translateY(-8px) rotate(0deg); }
    75% { transform: translateY(-20px) rotate(-0.5deg); }
}

.robot-head {
    width: 140px;
    height: 140px;
    background: linear-gradient(145deg,
        rgba(26, 35, 50, 0.95),
        rgba(42, 63, 95, 0.95));
    backdrop-filter: blur(20px);
    border-radius: 25px;
    margin: 0 auto;
    position: relative;
    border: 3px solid var(--primary-color);
    box-shadow:
        0 0 40px var(--glow-color),
        0 0 80px rgba(0, 212, 255, 0.4),
        inset 0 0 30px rgba(0, 212, 255, 0.15);
    animation: headGlow 4s ease-in-out infinite alternate;
}

@keyframes headGlow {
    0% {
        box-shadow:
            0 0 40px var(--glow-color),
            0 0 80px rgba(0, 212, 255, 0.4),
            inset 0 0 30px rgba(0, 212, 255, 0.15);
    }
    100% {
        box-shadow:
            0 0 60px var(--glow-color),
            0 0 120px rgba(0, 212, 255, 0.6),
            inset 0 0 40px rgba(0, 212, 255, 0.25);
    }
}

.robot-eyes {
    display: flex;
    justify-content: space-between;
    padding: 30px 25px 0;
}

.eye {
    width: 30px;
    height: 30px;
    background: radial-gradient(circle at 30% 30%, var(--primary-color), #0088cc);
    border-radius: 50%;
    position: relative;
    box-shadow:
        0 0 20px var(--glow-color),
        0 0 40px rgba(0, 212, 255, 0.5),
        inset 0 0 10px rgba(255, 255, 255, 0.2);
    animation: eyeBlink 5s ease-in-out infinite;
    border: 2px solid rgba(0, 212, 255, 0.8);
}

.pupil {
    width: 14px;
    height: 14px;
    background: radial-gradient(circle at 30% 30%, #ffffff, #e0e0e0);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: eyeMove 4s ease-in-out infinite;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

@keyframes eyeBlink {
    0%, 90%, 100% { transform: scaleY(1); }
    95% { transform: scaleY(0.1); }
}

@keyframes eyeMove {
    0%, 100% { transform: translate(-50%, -50%); }
    25% { transform: translate(-30%, -50%); }
    50% { transform: translate(-50%, -30%); }
    75% { transform: translate(-70%, -50%); }
}

.robot-mouth {
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 20px;
    border: 2px solid var(--primary-color);
    border-top: none;
    border-radius: 0 0 20px 20px;
    animation: mouthTalk 2s ease-in-out infinite;
}

@keyframes mouthTalk {
    0%, 100% { height: 20px; }
    50% { height: 10px; }
}

.robot-antenna {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
}

.antenna-base {
    width: 6px;
    height: 25px;
    background: linear-gradient(180deg, var(--primary-color), #00ffff);
    margin: 0 auto;
    border-radius: 3px;
    box-shadow:
        0 0 15px var(--glow-color),
        0 0 30px rgba(0, 212, 255, 0.4);
}

.antenna-tip {
    width: 15px;
    height: 15px;
    background: radial-gradient(circle at 30% 30%, #ff6b6b, #ee5a52);
    border-radius: 50%;
    margin: 0 auto;
    box-shadow:
        0 0 20px #ff6b6b,
        0 0 40px rgba(255, 107, 107, 0.5);
    animation: antennaPulse 1.5s ease-in-out infinite;
    border: 2px solid rgba(255, 107, 107, 0.8);
}

@keyframes antennaPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.3); }
}

.robot-body {
    width: 170px;
    height: 200px;
    background: linear-gradient(145deg,
        rgba(26, 35, 50, 0.95),
        rgba(42, 63, 95, 0.95));
    backdrop-filter: blur(20px);
    border-radius: 20px;
    margin: 25px auto;
    border: 3px solid var(--primary-color);
    position: relative;
    box-shadow:
        0 0 35px var(--glow-color),
        0 0 70px rgba(0, 212, 255, 0.3),
        inset 0 0 25px rgba(0, 212, 255, 0.15);
    animation: bodyPulse 6s ease-in-out infinite alternate;
}

@keyframes bodyPulse {
    0% {
        box-shadow:
            0 0 35px var(--glow-color),
            0 0 70px rgba(0, 212, 255, 0.3),
            inset 0 0 25px rgba(0, 212, 255, 0.15);
    }
    100% {
        box-shadow:
            0 0 50px var(--glow-color),
            0 0 100px rgba(0, 212, 255, 0.5),
            inset 0 0 35px rgba(0, 212, 255, 0.25);
    }
}

.body-panel {
    position: absolute;
    top: 25px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 140px;
    background: linear-gradient(145deg,
        rgba(0, 212, 255, 0.15),
        rgba(0, 212, 255, 0.08));
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: 2px solid var(--primary-color);
    box-shadow:
        inset 0 0 20px rgba(0, 212, 255, 0.2),
        0 0 15px rgba(0, 212, 255, 0.3);
}

.panel-light {
    width: 80px;
    height: 10px;
    background: linear-gradient(90deg, var(--primary-color), #00ffff, var(--primary-color));
    border-radius: 5px;
    margin: 20px auto;
    animation: panelGlow 2s ease-in-out infinite alternate;
    box-shadow:
        0 0 15px var(--glow-color),
        0 0 30px rgba(0, 212, 255, 0.5);
    position: relative;
}

.panel-light::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    border-radius: 5px;
    animation: lightScan 3s ease-in-out infinite;
}

@keyframes lightScan {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
    100% { transform: translateX(100%); }
}

@keyframes panelGlow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

.panel-buttons {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
}

.button {
    width: 15px;
    height: 15px;
    background: var(--accent-color);
    border-radius: 50%;
    box-shadow: 0 0 10px var(--accent-color);
    animation: buttonBlink 3s ease-in-out infinite;
}

.button:nth-child(2) { animation-delay: 1s; }
.button:nth-child(3) { animation-delay: 2s; }

@keyframes buttonBlink {
    0%, 80%, 100% { opacity: 0.3; }
    90% { opacity: 1; }
}

.robot-arms {
    position: absolute;
    top: 140px;
    width: 100%;
}

.arm {
    width: 25px;
    height: 90px;
    background: linear-gradient(145deg,
        rgba(26, 35, 50, 0.95),
        rgba(42, 63, 95, 0.95));
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 2px solid var(--primary-color);
    position: absolute;
    box-shadow:
        0 0 20px var(--glow-color),
        0 0 40px rgba(0, 212, 255, 0.2);
}

.left-arm {
    left: -30px;
    animation: leftArmMove 4s ease-in-out infinite;
}

.right-arm {
    right: -30px;
    animation: rightArmMove 4s ease-in-out infinite;
}

@keyframes leftArmMove {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-15deg); }
    75% { transform: rotate(10deg); }
}

@keyframes rightArmMove {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(15deg); }
    75% { transform: rotate(-10deg); }
}

/* فقاعات التفكير */
.thinking-bubbles {
    position: absolute;
    top: -80px;
    right: -50px;
    opacity: 0;
    animation: thinkingAppear 8s ease-in-out infinite;
}

@keyframes thinkingAppear {
    0%, 70%, 100% { opacity: 0; transform: translateY(20px); }
    75%, 95% { opacity: 1; transform: translateY(0); }
}

.bubble {
    position: absolute;
    font-size: 1.5rem;
    animation: bubbleFloat 2s ease-in-out infinite;
}

.bubble-1 {
    top: 0;
    right: 0;
    animation-delay: 0s;
}

.bubble-2 {
    top: -20px;
    right: 30px;
    animation-delay: 0.5s;
}

.bubble-3 {
    top: -40px;
    right: 60px;
    animation-delay: 1s;
}

@keyframes bubbleFloat {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-10px) scale(1.1); }
}

/* ===== منطقة المحادثة ===== */
.chat-area {
    background: linear-gradient(145deg,
        rgba(26, 35, 50, 0.95),
        rgba(42, 63, 95, 0.95));
    backdrop-filter: blur(30px);
    border: 2px solid var(--primary-color);
    border-radius: 25px;
    overflow: hidden;
    height: 550px;
    display: flex;
    flex-direction: column;
    box-shadow:
        0 0 40px var(--glow-color),
        0 0 80px rgba(0, 212, 255, 0.3),
        inset 0 0 30px rgba(0, 212, 255, 0.1);
    animation: chatGlow 8s ease-in-out infinite alternate;
}

@keyframes chatGlow {
    0% {
        box-shadow:
            0 0 40px var(--glow-color),
            0 0 80px rgba(0, 212, 255, 0.3),
            inset 0 0 30px rgba(0, 212, 255, 0.1);
    }
    100% {
        box-shadow:
            0 0 60px var(--glow-color),
            0 0 120px rgba(0, 212, 255, 0.5),
            inset 0 0 40px rgba(0, 212, 255, 0.2);
    }
}

.chat-header {
    background: linear-gradient(135deg, var(--primary-color), #0088cc);
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.chat-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: headerShine 4s ease-in-out infinite;
}

@keyframes headerShine {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

.chat-header h3 {
    color: white;
    font-size: 1.4rem;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-size: 0.9rem;
}

.status-dot {
    width: 10px;
    height: 10px;
    background: #4ade80;
    border-radius: 50%;
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chat-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    animation: messageSlide 0.5s ease-out;
}

@keyframes messageSlide {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-content {
    background: linear-gradient(145deg, #ff6b6b, #ee5a52);
    color: white;
    border-color: rgba(255, 107, 107, 0.3);
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.1),
        0 0 20px rgba(255, 107, 107, 0.2);
}

.message-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(145deg, var(--primary-color), #0088cc);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    flex-shrink: 0;
    border: 2px solid rgba(0, 212, 255, 0.5);
    box-shadow:
        0 0 15px var(--glow-color),
        0 0 30px rgba(0, 212, 255, 0.3);
    animation: avatarPulse 3s ease-in-out infinite;
}

@keyframes avatarPulse {
    0%, 100% {
        box-shadow:
            0 0 15px var(--glow-color),
            0 0 30px rgba(0, 212, 255, 0.3);
    }
    50% {
        box-shadow:
            0 0 25px var(--glow-color),
            0 0 50px rgba(0, 212, 255, 0.5);
    }
}

.user-message .message-avatar {
    background: linear-gradient(145deg, #ff6b6b, #ee5a52);
    border-color: rgba(255, 107, 107, 0.5);
    box-shadow:
        0 0 15px rgba(255, 107, 107, 0.6),
        0 0 30px rgba(255, 107, 107, 0.3);
}

.message-content {
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.15),
        rgba(255, 255, 255, 0.08));
    backdrop-filter: blur(10px);
    padding: 1.2rem 1.8rem;
    border-radius: 25px;
    max-width: 75%;
    line-height: 1.6;
    border: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.1),
        0 0 20px rgba(0, 212, 255, 0.1);
    position: relative;
    font-size: 1rem;
    color: var(--text-light);
}

.message-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.05), transparent);
    border-radius: 25px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.message:hover .message-content::before {
    opacity: 1;
}

.chat-input {
    padding: 2rem;
    background: linear-gradient(145deg,
        rgba(0, 0, 0, 0.3),
        rgba(26, 35, 50, 0.4));
    backdrop-filter: blur(10px);
    display: flex;
    gap: 1.2rem;
    align-items: center;
    border-top: 1px solid rgba(0, 212, 255, 0.2);
}

.chat-input input {
    flex: 1;
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.15),
        rgba(255, 255, 255, 0.08));
    backdrop-filter: blur(10px);
    border: 2px solid rgba(0, 212, 255, 0.3);
    border-radius: 30px;
    padding: 1.2rem 2rem;
    color: var(--text-light);
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
    box-shadow:
        inset 0 2px 10px rgba(0, 0, 0, 0.1),
        0 0 20px rgba(0, 212, 255, 0.1);
}

.chat-input input:focus {
    border-color: var(--primary-color);
    box-shadow:
        inset 0 2px 10px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(0, 212, 255, 0.4),
        0 0 50px rgba(0, 212, 255, 0.2);
    transform: translateY(-2px);
}

.chat-input input::placeholder {
    color: var(--text-secondary);
    font-style: italic;
}

.chat-input button {
    width: 55px;
    height: 55px;
    background: linear-gradient(145deg, var(--primary-color), #0088cc);
    border: 2px solid rgba(0, 212, 255, 0.5);
    border-radius: 50%;
    color: white;
    font-size: 1.3rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 5px 15px rgba(0, 212, 255, 0.3),
        0 0 20px rgba(0, 212, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.chat-input button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.chat-input button:hover::before {
    left: 100%;
}

.chat-input button:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow:
        0 8px 25px rgba(0, 212, 255, 0.5),
        0 0 40px rgba(0, 212, 255, 0.4);
}

.chat-input button:active {
    transform: scale(0.95) translateY(0);
}



/* ===== الاستجابة للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
        flex-direction: column;
        gap: 4px;
        cursor: pointer;
    }

    .hamburger span {
        width: 25px;
        height: 3px;
        background: var(--primary-color);
        border-radius: 2px;
        transition: all 0.3s ease;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .title-line {
        font-size: 2rem;
    }

    .robot-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .robot {
        width: 250px;
        height: 350px;
    }


}

@media (max-width: 480px) {
    .hero {
        padding: 6rem 1rem 2rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .title-line {
        font-size: 1.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
    }

    .main-logo {
        transform: scale(0.8);
    }

    .robot {
        transform: scale(0.8);
    }

    .chat-area {
        height: 400px;
    }
}

/* ===== تأثيرات إضافية ===== */
.robot.thinking .thinking-bubbles {
    animation: thinkingActive 3s ease-in-out infinite;
}

@keyframes thinkingActive {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.robot.thinking .robot-head {
    animation: headThinking 2s ease-in-out infinite;
}

@keyframes headThinking {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-2deg); }
    75% { transform: rotate(2deg); }
}

/* تأثير الكتابة */
.typing-indicator {
    display: flex;
    gap: 4px;
    padding: 1rem 1.5rem;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: typingDot 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typingDot {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-10px); }
}
