{"data_sources": {"yahoo_finance": {"enabled": true, "rate_limit": {"requests_per_minute": 60, "delay_between_requests": 1}, "symbols": {"stocks": ["AAPL", "GOOGL", "MSFT", "AMZN", "TSLA", "META", "NVDA", "NFLX", "BABA", "V", "JPM", "JNJ", "WMT", "PG", "UNH", "HD", "MA", "DIS", "PYPL", "ADBE"], "forex": ["EURUSD=X", "GBPUSD=X", "USDJPY=X", "USDCHF=X", "AUDUSD=X", "USDCAD=X", "NZDUSD=X", "EURGBP=X", "EURJPY=X", "GBPJPY=X"], "commodities": ["GC=F", "SI=F", "CL=F", "NG=F", "HG=F", "PL=F", "PA=F", "ZC=F", "ZS=F", "ZW=F"], "crypto": ["BTC-USD", "ETH-USD", "BNB-USD", "XRP-USD", "ADA-USD", "SOL-USD", "DOGE-USD", "DOT-USD", "AVAX-USD", "SHIB-USD"], "indices": ["^GSPC", "^DJI", "^IXIC", "^RUT", "^VIX", "^FTSE", "^GDAXI", "^FCHI", "^N225", "^HSI"]}, "periods": {"intraday": ["1d", "5d"], "short_term": ["1mo", "3mo"], "medium_term": ["6mo", "1y"], "long_term": ["2y", "5y", "10y", "max"]}, "intervals": {"minute": ["1m", "2m", "5m", "15m", "30m", "60m", "90m"], "hourly": ["1h"], "daily": ["1d"], "weekly": ["5d", "1wk"], "monthly": ["1mo", "3mo"]}}, "alpha_vantage": {"enabled": false, "api_key_required": true, "rate_limit": {"requests_per_minute": 5, "requests_per_day": 500, "delay_between_requests": 12}, "functions": {"time_series": ["TIME_SERIES_INTRADAY", "TIME_SERIES_DAILY", "TIME_SERIES_DAILY_ADJUSTED", "TIME_SERIES_WEEKLY", "TIME_SERIES_MONTHLY"], "forex": ["FX_INTRADAY", "FX_DAILY", "FX_WEEKLY", "FX_MONTHLY"], "crypto": ["DIGITAL_CURRENCY_INTRADAY", "DIGITAL_CURRENCY_DAILY", "DIGITAL_CURRENCY_WEEKLY", "DIGITAL_CURRENCY_MONTHLY"], "fundamentals": ["OVERVIEW", "INCOME_STATEMENT", "BALANCE_SHEET", "CASH_FLOW", "EARNINGS"], "technical_indicators": ["SMA", "EMA", "WMA", "DEMA", "TEMA", "TRIMA", "KAMA", "MAMA", "T3", "RSI", "STOCH", "STOCHF", "MACD", "MACDEXT", "STOCHRSI", "WILLR", "ADX", "ADXR", "APO", "PPO", "MOM", "BOP", "CCI", "CMO", "ROC", "ROCR", "AROON", "AROONOSC", "MFI", "TRIX", "ULTOSC", "DX", "MINUS_DI", "PLUS_DI", "MINUS_DM", "PLUS_DM", "BBANDS", "MIDPOINT", "MIDPRICE", "SAR", "TRANGE", "ATR", "NATR", "AD", "ADOSC", "OBV"]}}, "polygon": {"enabled": false, "api_key_required": true, "rate_limit": {"requests_per_minute": 5, "delay_between_requests": 12}}, "quandl": {"enabled": false, "api_key_required": true, "rate_limit": {"requests_per_day": 50, "delay_between_requests": 1}}}, "data_storage": {"local_storage": {"enabled": true, "base_directory": "data", "formats": ["csv", "json", "parquet"], "compression": {"enabled": true, "format": "gzip"}, "backup": {"enabled": true, "frequency": "daily", "retention_days": 30}}, "database_storage": {"postgresql": {"enabled": true, "connection": {"host": "localhost", "port": 5432, "database": "ai_trading_db", "table_prefix": "market_data_"}}, "mongodb": {"enabled": true, "connection": {"host": "localhost", "port": 27017, "database": "ai_trading_mongo", "collection_prefix": "market_data_"}}, "influxdb": {"enabled": true, "connection": {"host": "localhost", "port": 8086, "database": "market_data", "measurement_prefix": "price_"}}, "redis": {"enabled": true, "connection": {"host": "localhost", "port": 6379, "key_prefix": "market:", "ttl": 3600}}}}, "data_processing": {"validation": {"enabled": true, "rules": {"price_range_check": true, "volume_validation": true, "timestamp_validation": true, "duplicate_detection": true, "outlier_detection": true}, "thresholds": {"max_price_change_percent": 50, "min_volume": 0, "max_gap_hours": 72}}, "cleaning": {"enabled": true, "operations": {"remove_duplicates": true, "fill_missing_values": true, "smooth_outliers": true, "normalize_timestamps": true}, "missing_value_strategy": "forward_fill"}, "enrichment": {"enabled": true, "calculations": {"technical_indicators": true, "price_changes": true, "volatility": true, "moving_averages": true}}}, "scheduling": {"market_hours": {"enabled": true, "timezone": "US/Eastern", "trading_days": ["monday", "tuesday", "wednesday", "thursday", "friday"], "market_open": "09:30", "market_close": "16:00", "update_intervals": ["09:30", "12:00", "15:30"]}, "after_hours": {"enabled": true, "update_time": "18:00", "full_update": true}, "weekend": {"enabled": true, "update_day": "sunday", "update_time": "02:00", "historical_update": true}}, "monitoring": {"logging": {"level": "INFO", "file": "data_ingestion.log", "max_size_mb": 100, "backup_count": 5}, "metrics": {"enabled": true, "track_success_rate": true, "track_response_time": true, "track_data_quality": true, "alert_thresholds": {"success_rate_min": 0.95, "response_time_max": 30, "data_quality_min": 0.98}}, "alerts": {"enabled": true, "email_notifications": false, "webhook_notifications": false, "log_alerts": true}}, "security": {"api_keys": {"encryption": true, "environment_variables": true, "rotation_enabled": false}, "data_protection": {"encryption_at_rest": false, "encryption_in_transit": true, "access_logging": true}}}