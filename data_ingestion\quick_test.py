#!/usr/bin/env python3
"""
⚡ اختبار سريع لجلب البيانات
Quick Data Fetch Test
"""

print("🚀 بدء الاختبار السريع...")

try:
    import yfinance as yf
    print("✅ تم استيراد yfinance بنجاح")
    
    # اختبار جلب بيانات AAPL
    print("📊 جلب بيانات AAPL...")
    aapl = yf.Ticker("AAPL")
    data = aapl.history(period="5d")
    
    if not data.empty:
        print(f"✅ تم جلب {len(data)} سجل")
        print(f"💰 آخر سعر: ${data['Close'].iloc[-1]:.2f}")
        
        # حفظ البيانات
        data.to_csv("AAPL_test.csv")
        print("💾 تم حفظ البيانات في AAPL_test.csv")
        
        print("\n📋 أول 3 صفوف:")
        print(data.head(3))
        
    else:
        print("❌ لم يتم جلب أي بيانات")
        
except ImportError:
    print("❌ خطأ في استيراد yfinance")
except Exception as e:
    print(f"❌ خطأ: {str(e)}")

print("\n🎉 انتهى الاختبار!")
