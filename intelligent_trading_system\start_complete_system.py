#!/usr/bin/env python3
"""
🚀 M&M AI Trading System - Complete System Launcher
مشغل النظام الكامل لنظام التداول الذكي المتكامل

يقوم بتشغيل جميع مكونات النظام:
- قواعد البيانات المتعددة
- محرك C++ عالي الأداء
- خدمات Python للذكاء الاصطناعي
- واجهة React المتقدمة
- نظام المراقبة والتحليل
"""

import os
import sys
import subprocess
import time
import signal
import threading
import json
import requests
import psutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
import logging
from dataclasses import dataclass
import asyncio
import docker
import yaml

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system_launcher.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SystemLauncher")

@dataclass
class ServiceConfig:
    """تكوين الخدمة"""
    name: str
    port: int
    health_endpoint: str
    startup_time: int = 30
    required: bool = True
    dependencies: List[str] = None

class CompleteSystemLauncher:
    """مشغل النظام الكامل"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.processes = {}
        self.services_status = {}
        self.running = False
        self.docker_client = None
        
        # تكوين الخدمات
        self.services = {
            'postgres': ServiceConfig(
                name='PostgreSQL Database',
                port=5432,
                health_endpoint='',
                startup_time=20,
                required=True
            ),
            'mongodb': ServiceConfig(
                name='MongoDB Database',
                port=27017,
                health_endpoint='',
                startup_time=15,
                required=True
            ),
            'influxdb': ServiceConfig(
                name='InfluxDB Time Series',
                port=8086,
                health_endpoint='http://localhost:8086/health',
                startup_time=25,
                required=True
            ),
            'redis': ServiceConfig(
                name='Redis Cache',
                port=6379,
                health_endpoint='',
                startup_time=10,
                required=True
            ),
            'cpp_engine': ServiceConfig(
                name='C++ High-Performance Engine',
                port=8001,
                health_endpoint='http://localhost:8001/health',
                startup_time=30,
                required=True,
                dependencies=['postgres', 'redis']
            ),
            'python_ai': ServiceConfig(
                name='Python AI/ML Services',
                port=8000,
                health_endpoint='http://localhost:8000/health',
                startup_time=45,
                required=True,
                dependencies=['postgres', 'mongodb', 'influxdb', 'redis', 'cpp_engine']
            ),
            'frontend': ServiceConfig(
                name='React Frontend',
                port=3001,
                health_endpoint='http://localhost:3001',
                startup_time=20,
                required=True,
                dependencies=['python_ai']
            ),
            'nginx': ServiceConfig(
                name='Nginx Reverse Proxy',
                port=80,
                health_endpoint='http://localhost:80/health',
                startup_time=10,
                required=False,
                dependencies=['frontend', 'python_ai', 'cpp_engine']
            ),
            'grafana': ServiceConfig(
                name='Grafana Monitoring',
                port=3000,
                health_endpoint='http://localhost:3000/api/health',
                startup_time=20,
                required=False
            ),
            'jupyter': ServiceConfig(
                name='Jupyter Notebook',
                port=8888,
                health_endpoint='http://localhost:8888',
                startup_time=15,
                required=False
            )
        }
        
        # إعداد معالج الإشارات
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
    def _signal_handler(self, signum, frame):
        """معالج إشارات الإغلاق"""
        logger.info("🛑 تم استلام إشارة الإغلاق...")
        self.shutdown_system()
        sys.exit(0)
    
    def check_prerequisites(self):
        """فحص المتطلبات الأساسية"""
        logger.info("🔍 فحص المتطلبات الأساسية...")
        
        prerequisites = {
            'docker': 'Docker',
            'docker-compose': 'Docker Compose',
            'python': 'Python 3.8+',
            'node': 'Node.js',
            'npm': 'NPM'
        }
        
        missing = []
        
        for cmd, name in prerequisites.items():
            if not self._check_command_exists(cmd):
                missing.append(name)
                logger.error(f"❌ {name} غير مثبت")
            else:
                logger.info(f"✅ {name} متوفر")
        
        if missing:
            logger.error(f"❌ المتطلبات المفقودة: {', '.join(missing)}")
            return False
        
        # فحص Docker
        try:
            self.docker_client = docker.from_env()
            self.docker_client.ping()
            logger.info("✅ Docker يعمل بنجاح")
        except Exception as e:
            logger.error(f"❌ خطأ في Docker: {e}")
            return False
        
        return True
    
    def _check_command_exists(self, command):
        """فحص وجود أمر في النظام"""
        try:
            subprocess.run([command, '--version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def setup_environment(self):
        """إعداد البيئة"""
        logger.info("⚙️ إعداد البيئة...")
        
        # إنشاء ملف .env إذا لم يكن موجوداً
        env_file = self.project_root / '.env'
        if not env_file.exists():
            self._create_env_file(env_file)
        
        # إنشاء المجلدات المطلوبة
        required_dirs = [
            'data', 'logs', 'models', 'cache', 'notebooks',
            'logs/nginx', 'deployment/nginx/ssl'
        ]
        
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"📁 تم إنشاء المجلد: {dir_name}")
        
        # إعداد ملفات التكوين
        self._setup_config_files()
        
        logger.info("✅ تم إعداد البيئة بنجاح")
    
    def _create_env_file(self, env_file):
        """إنشاء ملف .env"""
        env_content = """# M&M AI Trading System Environment Variables

# Database Passwords
POSTGRES_PASSWORD=mmai_secure_postgres_123
MONGO_ROOT_USER=admin
MONGO_ROOT_PASSWORD=mmai_secure_mongo_123
REDIS_PASSWORD=mmai_secure_redis_123

# InfluxDB Configuration
INFLUX_USER=admin
INFLUX_PASSWORD=mmai_secure_influx_123
INFLUX_TOKEN=mmai_super_secret_influx_token_12345

# RabbitMQ Configuration
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=mmai_secure_rabbitmq_123

# Grafana Configuration
GRAFANA_USER=admin
GRAFANA_PASSWORD=mmai_secure_grafana_123

# Jupyter Configuration
JUPYTER_TOKEN=mmai_jupyter_token_123

# API Keys (replace with real keys)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key

# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key

# Environment
ENVIRONMENT=development
DEBUG=true
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        logger.info("📝 تم إنشاء ملف .env")
    
    def _setup_config_files(self):
        """إعداد ملفات التكوين"""
        
        # إعداد Nginx
        nginx_config_dir = self.project_root / 'deployment' / 'nginx'
        nginx_config_dir.mkdir(parents=True, exist_ok=True)
        
        nginx_config = """
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server python_ai:8000;
    }
    
    upstream cpp_engine {
        server cpp_engine:8001;
    }
    
    upstream frontend {
        server frontend:80;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location /health {
            return 200 'OK';
            add_header Content-Type text/plain;
        }
        
        location /api/ {
            proxy_pass http://backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /cpp/ {
            proxy_pass http://cpp_engine/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /ws {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
        }
        
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
"""
        
        with open(nginx_config_dir / 'nginx.conf', 'w') as f:
            f.write(nginx_config)
        
        # إعداد Prometheus
        prometheus_config_dir = self.project_root / 'monitoring' / 'prometheus'
        prometheus_config_dir.mkdir(parents=True, exist_ok=True)
        
        prometheus_config = """
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'python-ai'
    static_configs:
      - targets: ['python_ai:8000']
  
  - job_name: 'cpp-engine'
    static_configs:
      - targets: ['cpp_engine:8001']
  
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
"""
        
        with open(prometheus_config_dir / 'prometheus.yml', 'w') as f:
            f.write(prometheus_config)
    
    def build_services(self):
        """بناء الخدمات"""
        logger.info("🔨 بناء خدمات النظام...")
        
        try:
            # بناء الصور باستخدام Docker Compose
            result = subprocess.run([
                'docker-compose', 'build', '--parallel'
            ], cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"❌ فشل في بناء الخدمات: {result.stderr}")
                return False
            
            logger.info("✅ تم بناء جميع الخدمات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في بناء الخدمات: {e}")
            return False
    
    def start_services(self):
        """بدء الخدمات"""
        logger.info("🚀 بدء خدمات النظام...")
        
        try:
            # بدء الخدمات باستخدام Docker Compose
            process = subprocess.Popen([
                'docker-compose', 'up', '-d'
            ], cwd=self.project_root)
            
            self.processes['docker-compose'] = process
            
            # انتظار بدء الخدمات
            logger.info("⏳ انتظار بدء الخدمات...")
            time.sleep(10)
            
            # فحص حالة الخدمات
            return self._wait_for_services()
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء الخدمات: {e}")
            return False
    
    def _wait_for_services(self):
        """انتظار بدء جميع الخدمات"""
        logger.info("🔍 فحص حالة الخدمات...")
        
        # ترتيب الخدمات حسب التبعيات
        service_order = self._get_service_startup_order()
        
        for service_name in service_order:
            service = self.services[service_name]
            logger.info(f"⏳ انتظار بدء {service.name}...")
            
            if not self._wait_for_service(service_name):
                if service.required:
                    logger.error(f"❌ فشل في بدء الخدمة المطلوبة: {service.name}")
                    return False
                else:
                    logger.warning(f"⚠️ فشل في بدء الخدمة الاختيارية: {service.name}")
            else:
                logger.info(f"✅ {service.name} يعمل بنجاح")
                self.services_status[service_name] = 'running'
        
        return True
    
    def _get_service_startup_order(self):
        """الحصول على ترتيب بدء الخدمات"""
        ordered = []
        remaining = set(self.services.keys())
        
        while remaining:
            # العثور على خدمات بدون تبعيات أو تبعياتها جاهزة
            ready = []
            for service_name in remaining:
                service = self.services[service_name]
                if not service.dependencies or all(dep in ordered for dep in service.dependencies):
                    ready.append(service_name)
            
            if not ready:
                # إضافة الخدمات المتبقية (قد تكون هناك تبعيات دائرية)
                ready = list(remaining)
            
            ordered.extend(ready)
            remaining -= set(ready)
        
        return ordered
    
    def _wait_for_service(self, service_name):
        """انتظار بدء خدمة معينة"""
        service = self.services[service_name]
        max_wait = service.startup_time
        
        for attempt in range(max_wait):
            if self._check_service_health(service_name):
                return True
            
            time.sleep(1)
            
            if attempt % 5 == 0:
                logger.info(f"⏳ لا يزال ينتظر {service.name}... ({attempt}/{max_wait})")
        
        return False
    
    def _check_service_health(self, service_name):
        """فحص صحة الخدمة"""
        service = self.services[service_name]
        
        # فحص المنفذ أولاً
        if not self._check_port(service.port):
            return False
        
        # فحص نقطة الصحة إذا كانت متوفرة
        if service.health_endpoint:
            try:
                response = requests.get(service.health_endpoint, timeout=5)
                return response.status_code == 200
            except:
                return False
        
        return True
    
    def _check_port(self, port):
        """فحص إذا كان المنفذ مفتوحاً"""
        import socket
        
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                return result == 0
        except:
            return False
    
    def monitor_system(self):
        """مراقبة النظام"""
        logger.info("📊 بدء مراقبة النظام...")
        
        self.running = True
        
        while self.running:
            try:
                # فحص حالة الخدمات
                self._check_all_services()
                
                # عرض إحصائيات النظام
                self._display_system_stats()
                
                time.sleep(30)  # فحص كل 30 ثانية
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"❌ خطأ في مراقبة النظام: {e}")
                time.sleep(10)
    
    def _check_all_services(self):
        """فحص جميع الخدمات"""
        for service_name in self.services:
            is_healthy = self._check_service_health(service_name)
            
            if is_healthy:
                if self.services_status.get(service_name) != 'running':
                    logger.info(f"✅ {self.services[service_name].name} عاد للعمل")
                    self.services_status[service_name] = 'running'
            else:
                if self.services_status.get(service_name) == 'running':
                    logger.warning(f"⚠️ {self.services[service_name].name} لا يستجيب")
                    self.services_status[service_name] = 'down'
    
    def _display_system_stats(self):
        """عرض إحصائيات النظام"""
        # إحصائيات النظام
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # عدد الخدمات العاملة
        running_services = sum(1 for status in self.services_status.values() if status == 'running')
        total_services = len(self.services)
        
        logger.info(f"📊 إحصائيات النظام:")
        logger.info(f"   💻 CPU: {cpu_percent}%")
        logger.info(f"   🧠 الذاكرة: {memory.percent}% ({memory.used // (1024**3)}GB / {memory.total // (1024**3)}GB)")
        logger.info(f"   💾 القرص: {disk.percent}% ({disk.used // (1024**3)}GB / {disk.total // (1024**3)}GB)")
        logger.info(f"   🔧 الخدمات: {running_services}/{total_services} تعمل")
    
    def open_browser_interfaces(self):
        """فتح واجهات المتصفح"""
        logger.info("🌐 فتح واجهات النظام...")
        
        interfaces = {
            'النظام الرئيسي': 'http://localhost:80',
            'واجهة التداول': 'http://localhost:3001',
            'API التوثيق': 'http://localhost:8000/docs',
            'Grafana المراقبة': 'http://localhost:3000',
            'Jupyter Notebook': 'http://localhost:8888',
        }
        
        import webbrowser
        
        for name, url in interfaces.items():
            try:
                webbrowser.open(url)
                logger.info(f"🌐 تم فتح {name}: {url}")
                time.sleep(1)  # تأخير قصير بين فتح المواقع
            except Exception as e:
                logger.warning(f"⚠️ فشل في فتح {name}: {e}")
    
    def shutdown_system(self):
        """إغلاق النظام"""
        logger.info("🔄 إغلاق النظام...")
        
        self.running = False
        
        try:
            # إيقاف Docker Compose
            subprocess.run([
                'docker-compose', 'down'
            ], cwd=self.project_root, timeout=60)
            
            logger.info("✅ تم إغلاق جميع الخدمات")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إغلاق النظام: {e}")
    
    def run_complete_system(self):
        """تشغيل النظام الكامل"""
        try:
            print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                    🧠 M&M AI Trading System 🧠                              ║
║                      نظام التداول الذكي المتكامل                           ║
║                                                                              ║
║  🚀 نظام تداول متطور يدمج الذكاء الاصطناعي والتعلم الآلي                  ║
║  💎 يدعم جميع التقنيات الحديثة والقواعد البيانات المتقدمة                 ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
            """)
            
            # فحص المتطلبات
            if not self.check_prerequisites():
                return False
            
            # إعداد البيئة
            self.setup_environment()
            
            # بناء الخدمات
            if not self.build_services():
                return False
            
            # بدء الخدمات
            if not self.start_services():
                return False
            
            logger.info("🎉 تم تشغيل النظام بنجاح!")
            
            # عرض معلومات الوصول
            self._display_access_info()
            
            # فتح واجهات المتصفح
            self.open_browser_interfaces()
            
            # بدء المراقبة
            self.monitor_system()
            
            return True
            
        except KeyboardInterrupt:
            logger.info("🛑 تم إيقاف النظام بواسطة المستخدم")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل النظام: {e}")
            return False
        finally:
            self.shutdown_system()
    
    def _display_access_info(self):
        """عرض معلومات الوصول"""
        logger.info("🌐 معلومات الوصول للنظام:")
        logger.info("   🏠 النظام الرئيسي: http://localhost:80")
        logger.info("   📊 واجهة التداول: http://localhost:3001")
        logger.info("   🔧 API التوثيق: http://localhost:8000/docs")
        logger.info("   📈 Grafana المراقبة: http://localhost:3000 (admin/mmai_secure_grafana_123)")
        logger.info("   📓 Jupyter Notebook: http://localhost:8888 (token: mmai_jupyter_token_123)")
        logger.info("   🗄️ قواعد البيانات:")
        logger.info("      - PostgreSQL: localhost:5432 (postgres/mmai_secure_postgres_123)")
        logger.info("      - MongoDB: localhost:27017 (admin/mmai_secure_mongo_123)")
        logger.info("      - InfluxDB: localhost:8086 (admin/mmai_secure_influx_123)")
        logger.info("      - Redis: localhost:6379 (password: mmai_secure_redis_123)")

def main():
    """الدالة الرئيسية"""
    launcher = CompleteSystemLauncher()
    
    try:
        success = launcher.run_complete_system()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"❌ خطأ غير متوقع: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
