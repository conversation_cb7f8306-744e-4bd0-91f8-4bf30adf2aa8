-- =====================================================
-- M&M AI Trading System - Advanced PostgreSQL Schema
-- نظام التداول الذكي المتكامل - مخطط PostgreSQL المتقدم
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create custom types
CREATE TYPE order_type AS ENUM ('MARKET', 'LIMIT', 'STOP', 'STOP_LIMIT');
CREATE TYPE order_side AS ENUM ('BUY', 'SELL');
CREATE TYPE order_status AS ENUM ('PENDING', 'PARTIALLY_FILLED', 'FILLED', 'CANCELLED', 'REJECTED');
CREATE TYPE trading_signal AS ENUM ('BUY', 'SELL', 'HOLD');
CREATE TYPE account_type AS ENUM ('DEMO', 'LIVE', 'PAPER');
CREATE TYPE strategy_type AS ENUM ('TECHNICAL', 'FUNDAMENTAL', 'AI', 'HYBRID');

-- =====================================================
-- Core Tables
-- =====================================================

-- Users and Authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    country VARCHAR(2),
    timezone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(5) DEFAULT 'en',
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    risk_tolerance VARCHAR(20) DEFAULT 'medium',
    investment_experience VARCHAR(20) DEFAULT 'beginner',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0
);

-- User Sessions
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Trading Accounts
CREATE TABLE trading_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    account_name VARCHAR(100) NOT NULL,
    account_type account_type NOT NULL DEFAULT 'DEMO',
    broker_name VARCHAR(100),
    account_number VARCHAR(100),
    initial_balance DECIMAL(20, 8) NOT NULL DEFAULT 100000.00,
    current_balance DECIMAL(20, 8) NOT NULL DEFAULT 100000.00,
    equity DECIMAL(20, 8) NOT NULL DEFAULT 100000.00,
    margin_used DECIMAL(20, 8) DEFAULT 0.00,
    margin_available DECIMAL(20, 8) DEFAULT 100000.00,
    currency VARCHAR(3) DEFAULT 'USD',
    leverage INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Symbols and Instruments
CREATE TABLE symbols (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    asset_class VARCHAR(50) NOT NULL, -- 'FOREX', 'CRYPTO', 'STOCKS', 'COMMODITIES', 'INDICES'
    base_currency VARCHAR(10),
    quote_currency VARCHAR(10),
    exchange VARCHAR(100),
    sector VARCHAR(100),
    industry VARCHAR(100),
    market_cap BIGINT,
    tick_size DECIMAL(20, 8),
    min_quantity DECIMAL(20, 8),
    max_quantity DECIMAL(20, 8),
    trading_hours JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- Market Data Tables
-- =====================================================

-- Real-time Market Data (Partitioned by date)
CREATE TABLE market_data (
    id BIGSERIAL,
    symbol_id UUID NOT NULL REFERENCES symbols(id),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    open_price DECIMAL(20, 8) NOT NULL,
    high_price DECIMAL(20, 8) NOT NULL,
    low_price DECIMAL(20, 8) NOT NULL,
    close_price DECIMAL(20, 8) NOT NULL,
    volume BIGINT NOT NULL DEFAULT 0,
    bid_price DECIMAL(20, 8),
    ask_price DECIMAL(20, 8),
    bid_size BIGINT,
    ask_size BIGINT,
    spread DECIMAL(20, 8),
    vwap DECIMAL(20, 8), -- Volume Weighted Average Price
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (timestamp);

-- Create partitions for market data (monthly partitions)
CREATE TABLE market_data_2024_01 PARTITION OF market_data
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
CREATE TABLE market_data_2024_02 PARTITION OF market_data
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');
-- Add more partitions as needed

-- Technical Indicators Cache
CREATE TABLE technical_indicators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol_id UUID NOT NULL REFERENCES symbols(id),
    indicator_name VARCHAR(50) NOT NULL,
    timeframe VARCHAR(10) NOT NULL, -- '1m', '5m', '15m', '1h', '4h', '1d'
    period INTEGER NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    value DECIMAL(20, 8) NOT NULL,
    additional_values JSONB, -- For indicators with multiple values (MACD, Bollinger Bands)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Economic Calendar
CREATE TABLE economic_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_name VARCHAR(255) NOT NULL,
    country VARCHAR(2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    importance VARCHAR(10) NOT NULL, -- 'LOW', 'MEDIUM', 'HIGH'
    event_time TIMESTAMP WITH TIME ZONE NOT NULL,
    previous_value DECIMAL(20, 8),
    forecast_value DECIMAL(20, 8),
    actual_value DECIMAL(20, 8),
    impact_description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- Trading Tables
-- =====================================================

-- Orders
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES trading_accounts(id) ON DELETE CASCADE,
    symbol_id UUID NOT NULL REFERENCES symbols(id),
    order_type order_type NOT NULL,
    side order_side NOT NULL,
    status order_status NOT NULL DEFAULT 'PENDING',
    quantity DECIMAL(20, 8) NOT NULL,
    filled_quantity DECIMAL(20, 8) DEFAULT 0.00,
    price DECIMAL(20, 8), -- NULL for market orders
    stop_price DECIMAL(20, 8), -- For stop orders
    time_in_force VARCHAR(10) DEFAULT 'GTC', -- 'GTC', 'IOC', 'FOK', 'DAY'
    client_order_id VARCHAR(100),
    broker_order_id VARCHAR(100),
    commission DECIMAL(20, 8) DEFAULT 0.00,
    commission_currency VARCHAR(3) DEFAULT 'USD',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    filled_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT
);

-- Trades/Executions
CREATE TABLE trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES trading_accounts(id) ON DELETE CASCADE,
    symbol_id UUID NOT NULL REFERENCES symbols(id),
    buy_order_id UUID REFERENCES orders(id),
    sell_order_id UUID REFERENCES orders(id),
    side order_side NOT NULL,
    quantity DECIMAL(20, 8) NOT NULL,
    price DECIMAL(20, 8) NOT NULL,
    commission DECIMAL(20, 8) DEFAULT 0.00,
    commission_currency VARCHAR(3) DEFAULT 'USD',
    trade_value DECIMAL(20, 8) NOT NULL,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    broker_trade_id VARCHAR(100),
    is_closing BOOLEAN DEFAULT false
);

-- Positions
CREATE TABLE positions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES trading_accounts(id) ON DELETE CASCADE,
    symbol_id UUID NOT NULL REFERENCES symbols(id),
    side order_side NOT NULL,
    quantity DECIMAL(20, 8) NOT NULL,
    average_price DECIMAL(20, 8) NOT NULL,
    current_price DECIMAL(20, 8),
    unrealized_pnl DECIMAL(20, 8) DEFAULT 0.00,
    realized_pnl DECIMAL(20, 8) DEFAULT 0.00,
    commission_paid DECIMAL(20, 8) DEFAULT 0.00,
    swap_charges DECIMAL(20, 8) DEFAULT 0.00,
    opened_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    closed_at TIMESTAMP WITH TIME ZONE,
    is_open BOOLEAN DEFAULT true
);

-- =====================================================
-- AI and Strategy Tables
-- =====================================================

-- Trading Strategies
CREATE TABLE trading_strategies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    strategy_type strategy_type NOT NULL,
    parameters JSONB NOT NULL DEFAULT '{}',
    symbols TEXT[], -- Array of symbols this strategy applies to
    timeframes TEXT[], -- Array of timeframes
    risk_per_trade DECIMAL(5, 4) DEFAULT 0.02, -- 2%
    max_positions INTEGER DEFAULT 5,
    is_active BOOLEAN DEFAULT false,
    is_backtested BOOLEAN DEFAULT false,
    backtest_results JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- AI Model Predictions
CREATE TABLE ai_predictions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol_id UUID NOT NULL REFERENCES symbols(id),
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(20) NOT NULL,
    prediction_type VARCHAR(50) NOT NULL, -- 'PRICE', 'DIRECTION', 'VOLATILITY'
    timeframe VARCHAR(10) NOT NULL,
    prediction_horizon INTEGER NOT NULL, -- Hours ahead
    predicted_value DECIMAL(20, 8),
    confidence DECIMAL(5, 4) NOT NULL, -- 0.0 to 1.0
    actual_value DECIMAL(20, 8), -- Filled when actual data is available
    accuracy DECIMAL(5, 4), -- Calculated accuracy
    features_used JSONB, -- Input features used for prediction
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    prediction_for TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Trading Signals
CREATE TABLE trading_signals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    strategy_id UUID REFERENCES trading_strategies(id) ON DELETE CASCADE,
    symbol_id UUID NOT NULL REFERENCES symbols(id),
    signal trading_signal NOT NULL,
    confidence DECIMAL(5, 4) NOT NULL,
    entry_price DECIMAL(20, 8),
    target_price DECIMAL(20, 8),
    stop_loss DECIMAL(20, 8),
    risk_reward_ratio DECIMAL(10, 4),
    reasoning TEXT,
    technical_indicators JSONB,
    fundamental_data JSONB,
    ai_analysis JSONB,
    is_executed BOOLEAN DEFAULT false,
    executed_at TIMESTAMP WITH TIME ZONE,
    result VARCHAR(20), -- 'WIN', 'LOSS', 'BREAKEVEN'
    pnl DECIMAL(20, 8),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- Performance and Analytics Tables
-- =====================================================

-- Portfolio Performance (Daily snapshots)
CREATE TABLE portfolio_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES trading_accounts(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    total_value DECIMAL(20, 8) NOT NULL,
    cash_balance DECIMAL(20, 8) NOT NULL,
    equity DECIMAL(20, 8) NOT NULL,
    margin_used DECIMAL(20, 8) DEFAULT 0.00,
    daily_pnl DECIMAL(20, 8) DEFAULT 0.00,
    daily_return DECIMAL(10, 6) DEFAULT 0.00,
    cumulative_return DECIMAL(10, 6) DEFAULT 0.00,
    drawdown DECIMAL(10, 6) DEFAULT 0.00,
    max_drawdown DECIMAL(10, 6) DEFAULT 0.00,
    volatility DECIMAL(10, 6) DEFAULT 0.00,
    sharpe_ratio DECIMAL(10, 6) DEFAULT 0.00,
    sortino_ratio DECIMAL(10, 6) DEFAULT 0.00,
    win_rate DECIMAL(5, 4) DEFAULT 0.00,
    profit_factor DECIMAL(10, 4) DEFAULT 0.00,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    losing_trades INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(account_id, date)
);

-- Risk Metrics
CREATE TABLE risk_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES trading_accounts(id) ON DELETE CASCADE,
    symbol_id UUID REFERENCES symbols(id),
    metric_date DATE NOT NULL,
    var_95 DECIMAL(20, 8), -- Value at Risk 95%
    var_99 DECIMAL(20, 8), -- Value at Risk 99%
    expected_shortfall DECIMAL(20, 8),
    beta DECIMAL(10, 6),
    correlation DECIMAL(5, 4),
    volatility DECIMAL(10, 6),
    skewness DECIMAL(10, 6),
    kurtosis DECIMAL(10, 6),
    maximum_drawdown DECIMAL(10, 6),
    calmar_ratio DECIMAL(10, 6),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- News and Sentiment Tables
-- =====================================================

-- News Articles
CREATE TABLE news_articles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    content TEXT,
    summary TEXT,
    source VARCHAR(100) NOT NULL,
    author VARCHAR(255),
    published_at TIMESTAMP WITH TIME ZONE NOT NULL,
    url TEXT,
    language VARCHAR(5) DEFAULT 'en',
    category VARCHAR(100),
    symbols TEXT[], -- Related symbols
    sentiment_score DECIMAL(5, 4), -- -1.0 to 1.0
    sentiment_label VARCHAR(20), -- 'POSITIVE', 'NEGATIVE', 'NEUTRAL'
    importance_score DECIMAL(5, 4), -- 0.0 to 1.0
    keywords TEXT[],
    entities JSONB, -- Named entities extracted
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Social Media Sentiment
CREATE TABLE social_sentiment (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol_id UUID NOT NULL REFERENCES symbols(id),
    platform VARCHAR(50) NOT NULL, -- 'twitter', 'reddit', 'stocktwits'
    sentiment_score DECIMAL(5, 4) NOT NULL,
    volume INTEGER NOT NULL, -- Number of mentions
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    trending_score DECIMAL(5, 4),
    keywords TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- System and Audit Tables
-- =====================================================

-- System Logs
CREATE TABLE system_logs (
    id BIGSERIAL PRIMARY KEY,
    level VARCHAR(10) NOT NULL, -- 'DEBUG', 'INFO', 'WARNING', 'ERROR'
    component VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    details JSONB,
    user_id UUID REFERENCES users(id),
    session_id UUID REFERENCES user_sessions(id),
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Audit Trail
CREATE TABLE audit_trail (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(10) NOT NULL, -- 'INSERT', 'UPDATE', 'DELETE'
    old_values JSONB,
    new_values JSONB,
    user_id UUID REFERENCES users(id),
    session_id UUID REFERENCES user_sessions(id),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- Indexes for Performance
-- =====================================================

-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_active ON users(is_active);

-- Market data indexes
CREATE INDEX idx_market_data_symbol_timestamp ON market_data(symbol_id, timestamp DESC);
CREATE INDEX idx_market_data_timestamp ON market_data(timestamp DESC);

-- Technical indicators indexes
CREATE INDEX idx_technical_indicators_symbol_name_timeframe ON technical_indicators(symbol_id, indicator_name, timeframe, timestamp DESC);

-- Orders indexes
CREATE INDEX idx_orders_account_symbol ON orders(account_id, symbol_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at DESC);

-- Trades indexes
CREATE INDEX idx_trades_account_symbol ON trades(account_id, symbol_id);
CREATE INDEX idx_trades_executed_at ON trades(executed_at DESC);

-- Positions indexes
CREATE INDEX idx_positions_account_symbol ON positions(account_id, symbol_id);
CREATE INDEX idx_positions_open ON positions(is_open);

-- Trading signals indexes
CREATE INDEX idx_trading_signals_symbol_created ON trading_signals(symbol_id, created_at DESC);
CREATE INDEX idx_trading_signals_strategy ON trading_signals(strategy_id);

-- Performance indexes
CREATE INDEX idx_portfolio_performance_account_date ON portfolio_performance(account_id, date DESC);

-- News indexes
CREATE INDEX idx_news_published_at ON news_articles(published_at DESC);
CREATE INDEX idx_news_symbols ON news_articles USING GIN(symbols);
CREATE INDEX idx_news_sentiment ON news_articles(sentiment_score);

-- System logs indexes
CREATE INDEX idx_system_logs_level_created ON system_logs(level, created_at DESC);
CREATE INDEX idx_system_logs_component ON system_logs(component);

-- =====================================================
-- Functions and Triggers
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at trigger to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trading_accounts_updated_at BEFORE UPDATE ON trading_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_symbols_updated_at BEFORE UPDATE ON symbols
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_positions_updated_at BEFORE UPDATE ON positions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trading_strategies_updated_at BEFORE UPDATE ON trading_strategies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Views for Common Queries
-- =====================================================

-- Active positions with current P&L
CREATE VIEW active_positions_view AS
SELECT 
    p.*,
    s.symbol,
    s.name as symbol_name,
    ta.account_name,
    u.username,
    (p.current_price - p.average_price) * p.quantity * 
    CASE WHEN p.side = 'BUY' THEN 1 ELSE -1 END as unrealized_pnl_calculated
FROM positions p
JOIN symbols s ON p.symbol_id = s.id
JOIN trading_accounts ta ON p.account_id = ta.id
JOIN users u ON ta.user_id = u.id
WHERE p.is_open = true;

-- Latest market data view
CREATE VIEW latest_market_data_view AS
SELECT DISTINCT ON (symbol_id)
    md.*,
    s.symbol,
    s.name as symbol_name
FROM market_data md
JOIN symbols s ON md.symbol_id = s.id
ORDER BY symbol_id, timestamp DESC;

-- Trading performance summary
CREATE VIEW trading_performance_summary AS
SELECT 
    ta.id as account_id,
    ta.account_name,
    u.username,
    COUNT(t.id) as total_trades,
    SUM(CASE WHEN t.side = 'BUY' THEN t.trade_value ELSE -t.trade_value END) as total_volume,
    AVG(t.commission) as avg_commission,
    SUM(t.commission) as total_commission
FROM trading_accounts ta
JOIN users u ON ta.user_id = u.id
LEFT JOIN trades t ON ta.id = t.account_id
GROUP BY ta.id, ta.account_name, u.username;

-- =====================================================
-- Initial Data
-- =====================================================

-- Insert default symbols
INSERT INTO symbols (symbol, name, asset_class, base_currency, quote_currency, exchange) VALUES
('EURUSD', 'Euro/US Dollar', 'FOREX', 'EUR', 'USD', 'FOREX'),
('GBPUSD', 'British Pound/US Dollar', 'FOREX', 'GBP', 'USD', 'FOREX'),
('USDJPY', 'US Dollar/Japanese Yen', 'FOREX', 'USD', 'JPY', 'FOREX'),
('XAUUSD', 'Gold/US Dollar', 'COMMODITIES', 'XAU', 'USD', 'FOREX'),
('BTCUSD', 'Bitcoin/US Dollar', 'CRYPTO', 'BTC', 'USD', 'CRYPTO'),
('ETHUSD', 'Ethereum/US Dollar', 'CRYPTO', 'ETH', 'USD', 'CRYPTO'),
('AAPL', 'Apple Inc.', 'STOCKS', NULL, 'USD', 'NASDAQ'),
('GOOGL', 'Alphabet Inc.', 'STOCKS', NULL, 'USD', 'NASDAQ'),
('MSFT', 'Microsoft Corporation', 'STOCKS', NULL, 'USD', 'NASDAQ'),
('TSLA', 'Tesla Inc.', 'STOCKS', NULL, 'USD', 'NASDAQ');

-- Grant permissions (adjust as needed)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO trading_app;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO trading_app;
