#!/usr/bin/env python3
"""
🗄️ إعداد قواعد البيانات لمنصة التداول الذكية
Database Setup for AI Trading Platform

المرحلة 2: إعداد قواعد البيانات
- PostgreSQL: بيانات المستخدمين والإشارات
- MongoDB: المحادثات والبيانات غير المنظمة  
- Redis: كاش البيانات الفورية
- InfluxDB: بيانات السوق الزمنية
"""

import os
import sys
import subprocess
import json
import time
import logging
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_setup.log'),
        logging.StreamHandler()
    ]
)

class DatabaseSetup:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.docker_compose_file = self.project_root / "docker-compose.yml"
        self.env_file = self.project_root / ".env"
        
    def check_docker(self):
        """فحص توفر Docker"""
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                logging.info(f"✅ Docker متوفر: {result.stdout.strip()}")
                return True
            else:
                logging.error("❌ Docker غير متوفر")
                return False
        except FileNotFoundError:
            logging.error("❌ Docker غير مثبت")
            return False
    
    def create_env_file(self):
        """إنشاء ملف متغيرات البيئة"""
        env_content = """# 🔐 متغيرات البيئة لقواعد البيانات
# Database Environment Variables

# PostgreSQL
POSTGRES_DB=ai_trading_db
POSTGRES_USER=trading_admin
POSTGRES_PASSWORD=TradingSecure2024!
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# MongoDB
MONGO_INITDB_ROOT_USERNAME=mongo_admin
MONGO_INITDB_ROOT_PASSWORD=MongoSecure2024!
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DB=ai_trading_mongo

# Redis
REDIS_PASSWORD=RedisSecure2024!
REDIS_HOST=localhost
REDIS_PORT=6379

# InfluxDB
INFLUXDB_DB=market_data
INFLUXDB_ADMIN_USER=influx_admin
INFLUXDB_ADMIN_PASSWORD=InfluxSecure2024!
INFLUXDB_HOST=localhost
INFLUXDB_PORT=8086

# Application Settings
APP_SECRET_KEY=your-super-secret-key-here
JWT_SECRET=jwt-secret-key-here
ENVIRONMENT=development
"""
        
        with open(self.env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        logging.info("✅ تم إنشاء ملف .env")
    
    def create_docker_compose(self):
        """إنشاء ملف Docker Compose"""
        compose_content = """version: '3.8'

services:
  # 🐘 PostgreSQL - بيانات المستخدمين والإشارات
  postgres:
    image: postgres:15-alpine
    container_name: ai_trading_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - trading_network
    restart: unless-stopped

  # 🍃 MongoDB - المحادثات والبيانات غير المنظمة
  mongodb:
    image: mongo:7.0
    container_name: ai_trading_mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD}
    ports:
      - "${MONGO_PORT}:27017"
    volumes:
      - mongodb_data:/data/db
      - ./database/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - trading_network
    restart: unless-stopped

  # 🔴 Redis - كاش البيانات الفورية
  redis:
    image: redis:7-alpine
    container_name: ai_trading_redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
    networks:
      - trading_network
    restart: unless-stopped

  # 📊 InfluxDB - بيانات السوق الزمنية
  influxdb:
    image: influxdb:2.7-alpine
    container_name: ai_trading_influxdb
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: ${INFLUXDB_ADMIN_USER}
      DOCKER_INFLUXDB_INIT_PASSWORD: ${INFLUXDB_ADMIN_PASSWORD}
      DOCKER_INFLUXDB_INIT_ORG: ai-trading
      DOCKER_INFLUXDB_INIT_BUCKET: market-data
    ports:
      - "${INFLUXDB_PORT}:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
    networks:
      - trading_network
    restart: unless-stopped

  # 🌐 Adminer - إدارة قواعد البيانات
  adminer:
    image: adminer:latest
    container_name: ai_trading_adminer
    ports:
      - "8080:8080"
    networks:
      - trading_network
    restart: unless-stopped
    depends_on:
      - postgres
      - mongodb

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
  influxdb_data:

networks:
  trading_network:
    driver: bridge
"""
        
        with open(self.docker_compose_file, 'w', encoding='utf-8') as f:
            f.write(compose_content)
        
        logging.info("✅ تم إنشاء ملف docker-compose.yml")
    
    def create_database_structure(self):
        """إنشاء هيكل مجلدات قواعد البيانات"""
        db_dirs = [
            "database",
            "database/postgres",
            "database/postgres/init",
            "database/mongodb", 
            "database/mongodb/init",
            "database/schemas",
            "database/migrations"
        ]
        
        for dir_path in db_dirs:
            full_path = self.project_root / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
        
        logging.info("✅ تم إنشاء هيكل مجلدات قواعد البيانات")
    
    def run(self):
        """تشغيل عملية الإعداد"""
        logging.info("🚀 بدء إعداد قواعد البيانات...")
        
        # فحص Docker
        if not self.check_docker():
            logging.error("❌ يجب تثبيت Docker أولاً")
            logging.info("📥 تحميل Docker من: https://www.docker.com/products/docker-desktop")
            return False
        
        # إنشاء الملفات والمجلدات
        self.create_env_file()
        self.create_docker_compose()
        self.create_database_structure()
        
        logging.info("✅ تم إعداد قواعد البيانات بنجاح!")
        logging.info("🔧 الخطوات التالية:")
        logging.info("   1. تشغيل: docker-compose up -d")
        logging.info("   2. الوصول لـ Adminer: http://localhost:8080")
        logging.info("   3. إنشاء الجداول والمجموعات")
        
        return True

if __name__ == "__main__":
    setup = DatabaseSetup()
    success = setup.run()
    
    if success:
        print("\n" + "="*60)
        print("🎉 تم إعداد قواعد البيانات بنجاح!")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("❌ فشل في إعداد قواعد البيانات")
        print("="*60)
        sys.exit(1)
