"""
🌐 واجهة برمجة التطبيقات الرئيسية لنظام التداول الذكي
Main API for Intelligent Trading System

يوفر واجهات RESTful شاملة لجميع وظائف النظام:
- إدارة البيانات المالية
- تنفيذ استراتيجيات التداول
- مراقبة الأداء
- إدارة المحافظ
- تحليلات الذكاء الاصطناعي
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import asyncio
import json
import logging
import pandas as pd
import numpy as np
from contextlib import asynccontextmanager
import uvicorn
from dataclasses import asdict
import warnings
warnings.filterwarnings('ignore')

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ITS_API")

# نماذج البيانات
class MarketDataRequest(BaseModel):
    symbols: List[str] = Field(..., description="قائمة الرموز المالية")
    timeframe: str = Field("1d", description="الإطار الزمني")
    start_date: Optional[datetime] = Field(None, description="تاريخ البداية")
    end_date: Optional[datetime] = Field(None, description="تاريخ النهاية")
    data_source: str = Field("yahoo", description="مصدر البيانات")

class TradingSignalRequest(BaseModel):
    symbol: str = Field(..., description="الرمز المالي")
    strategy: str = Field("ai_ensemble", description="نوع الاستراتيجية")
    timeframe: str = Field("1h", description="الإطار الزمني")
    lookback_period: int = Field(100, description="فترة النظر للخلف")

class PortfolioRequest(BaseModel):
    name: str = Field(..., description="اسم المحفظة")
    initial_balance: float = Field(100000, description="الرصيد الأولي")
    risk_tolerance: str = Field("medium", description="تحمل المخاطر")
    investment_horizon: str = Field("medium_term", description="أفق الاستثمار")

class TradeExecutionRequest(BaseModel):
    symbol: str = Field(..., description="الرمز المالي")
    action: str = Field(..., description="نوع العملية")
    quantity: float = Field(..., description="الكمية")
    order_type: str = Field("market", description="نوع الأمر")
    price: Optional[float] = Field(None, description="السعر المحدد")

class BacktestRequest(BaseModel):
    strategy: str = Field(..., description="الاستراتيجية")
    symbols: List[str] = Field(..., description="الرموز المالية")
    start_date: datetime = Field(..., description="تاريخ البداية")
    end_date: datetime = Field(..., description="تاريخ النهاية")
    initial_capital: float = Field(100000, description="رأس المال الأولي")
    parameters: Dict[str, Any] = Field({}, description="معاملات الاستراتيجية")

# إدارة الاتصالات
class ConnectionManager:
    """إدارة اتصالات WebSocket"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.subscriptions: Dict[str, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"اتصال WebSocket جديد. إجمالي الاتصالات: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        # إزالة من الاشتراكات
        for topic, connections in self.subscriptions.items():
            if websocket in connections:
                connections.remove(websocket)
        
        logger.info(f"تم قطع اتصال WebSocket. الاتصالات المتبقية: {len(self.active_connections)}")
    
    async def subscribe(self, websocket: WebSocket, topic: str):
        if topic not in self.subscriptions:
            self.subscriptions[topic] = []
        
        if websocket not in self.subscriptions[topic]:
            self.subscriptions[topic].append(websocket)
        
        logger.info(f"اشتراك في {topic}. المشتركون: {len(self.subscriptions[topic])}")
    
    async def broadcast_to_topic(self, topic: str, message: dict):
        if topic in self.subscriptions:
            disconnected = []
            
            for connection in self.subscriptions[topic]:
                try:
                    await connection.send_text(json.dumps(message))
                except:
                    disconnected.append(connection)
            
            # إزالة الاتصالات المنقطعة
            for conn in disconnected:
                self.disconnect(conn)

# إنشاء مدير الاتصالات
manager = ConnectionManager()

# إعداد التطبيق
@asynccontextmanager
async def lifespan(app: FastAPI):
    # بدء التطبيق
    logger.info("🚀 بدء تشغيل نظام التداول الذكي المتكامل")
    
    # تهيئة المكونات
    await initialize_system()
    
    # بدء المهام الخلفية
    asyncio.create_task(market_data_streamer())
    asyncio.create_task(signal_generator())
    
    yield
    
    # إغلاق التطبيق
    logger.info("🔄 إغلاق نظام التداول الذكي المتكامل")
    await cleanup_system()

app = FastAPI(
    title="نظام التداول الذكي المتكامل",
    description="Intelligent Trading System - نظام تداول متقدم بالذكاء الاصطناعي",
    version="1.0.0",
    lifespan=lifespan
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إعداد الأمان
security = HTTPBearer()

# متغيرات النظام
system_status = {
    "initialized": False,
    "data_collector": None,
    "ai_strategies": {},
    "portfolio_manager": None,
    "risk_manager": None
}

# وظائف التهيئة
async def initialize_system():
    """تهيئة مكونات النظام"""
    try:
        # تهيئة جامع البيانات
        from ..data_layer.ingestion.market_data_collector import AdvancedMarketDataCollector, MarketDataConfig
        
        config = MarketDataConfig(
            update_interval=60,
            data_validation=True,
            cache_enabled=True
        )
        
        system_status["data_collector"] = AdvancedMarketDataCollector(config)
        
        # تهيئة استراتيجيات الذكاء الاصطناعي
        from ..trading_engine.strategy_engine.ai_strategies import (
            LSTMPredictionStrategy,
            ReinforcementLearningStrategy,
            EnsembleAIStrategy
        )
        
        system_status["ai_strategies"] = {
            "lstm": LSTMPredictionStrategy(),
            "rl": ReinforcementLearningStrategy(),
            "ensemble": None  # سيتم إنشاؤه لاحقاً
        }
        
        system_status["initialized"] = True
        logger.info("✅ تم تهيئة النظام بنجاح")
        
    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة النظام: {e}")
        raise

async def cleanup_system():
    """تنظيف موارد النظام"""
    try:
        if system_status["data_collector"]:
            system_status["data_collector"].close()
        
        logger.info("✅ تم تنظيف موارد النظام")
        
    except Exception as e:
        logger.error(f"❌ خطأ في تنظيف النظام: {e}")

# المهام الخلفية
async def market_data_streamer():
    """تدفق البيانات المالية"""
    while True:
        try:
            if system_status["initialized"]:
                # محاكاة بيانات السوق
                market_data = {
                    "timestamp": datetime.now().isoformat(),
                    "type": "market_data",
                    "data": {
                        "AAPL": {"price": 150.0 + np.random.normal(0, 2), "volume": 1000000},
                        "GOOGL": {"price": 2800.0 + np.random.normal(0, 50), "volume": 500000},
                        "BTC-USD": {"price": 45000.0 + np.random.normal(0, 1000), "volume": 100}
                    }
                }
                
                await manager.broadcast_to_topic("market_data", market_data)
            
            await asyncio.sleep(5)  # تحديث كل 5 ثوان
            
        except Exception as e:
            logger.error(f"خطأ في تدفق البيانات: {e}")
            await asyncio.sleep(10)

async def signal_generator():
    """مولد الإشارات"""
    while True:
        try:
            if system_status["initialized"]:
                # محاكاة إشارات التداول
                signals = {
                    "timestamp": datetime.now().isoformat(),
                    "type": "trading_signals",
                    "signals": [
                        {
                            "symbol": "AAPL",
                            "action": np.random.choice(["BUY", "SELL", "HOLD"]),
                            "confidence": np.random.uniform(0.5, 1.0),
                            "strategy": "AI_Ensemble"
                        }
                    ]
                }
                
                await manager.broadcast_to_topic("trading_signals", signals)
            
            await asyncio.sleep(30)  # إشارات كل 30 ثانية
            
        except Exception as e:
            logger.error(f"خطأ في توليد الإشارات: {e}")
            await asyncio.sleep(60)

# المسارات الأساسية
@app.get("/")
async def root():
    """الصفحة الرئيسية"""
    return {
        "message": "مرحباً بك في نظام التداول الذكي المتكامل",
        "version": "1.0.0",
        "status": "running",
        "features": [
            "تحليل البيانات المالية بالذكاء الاصطناعي",
            "استراتيجيات التداول المتقدمة",
            "إدارة المخاطر الذكية",
            "تحليل الأداء الشامل"
        ]
    }

@app.get("/health")
async def health_check():
    """فحص صحة النظام"""
    return {
        "status": "healthy" if system_status["initialized"] else "initializing",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "data_collector": system_status["data_collector"] is not None,
            "ai_strategies": len(system_status["ai_strategies"]) > 0,
            "websocket_connections": len(manager.active_connections)
        }
    }

# مسارات البيانات المالية
@app.post("/api/v1/market-data")
async def get_market_data(request: MarketDataRequest):
    """الحصول على البيانات المالية"""
    try:
        if not system_status["initialized"]:
            raise HTTPException(status_code=503, detail="النظام غير مهيأ")
        
        collector = system_status["data_collector"]
        
        # جمع البيانات
        data = await collector.collect_all_data(
            symbols=request.symbols,
            data_sources=[request.data_source]
        )
        
        # تحويل البيانات للاستجابة
        response_data = {}
        for source, symbols_data in data.items():
            response_data[source] = {}
            for symbol, df in symbols_data.items():
                if not df.empty:
                    response_data[source][symbol] = {
                        "data": df.tail(100).to_dict('records'),
                        "latest_price": float(df['close'].iloc[-1]),
                        "change": float(df['close'].iloc[-1] - df['close'].iloc[-2]),
                        "change_percent": float((df['close'].iloc[-1] - df['close'].iloc[-2]) / df['close'].iloc[-2] * 100)
                    }
        
        return {
            "status": "success",
            "data": response_data,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"خطأ في جلب البيانات المالية: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# مسارات إشارات التداول
@app.post("/api/v1/trading-signals")
async def generate_trading_signal(request: TradingSignalRequest):
    """توليد إشارة تداول"""
    try:
        if not system_status["initialized"]:
            raise HTTPException(status_code=503, detail="النظام غير مهيأ")
        
        # محاكاة بيانات للاختبار
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', periods=request.lookback_period, freq='H')
        
        price = 100
        prices = [price]
        
        for _ in range(request.lookback_period - 1):
            change = np.random.normal(0, 0.02)
            price *= (1 + change)
            prices.append(price)
        
        data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, request.lookback_period),
            'symbol': request.symbol
        })
        
        # اختيار الاستراتيجية
        strategy = system_status["ai_strategies"].get(request.strategy, 
                                                    system_status["ai_strategies"]["lstm"])
        
        # توليد الإشارة
        if not strategy.is_trained:
            # تدريب سريع للاختبار
            strategy.train(data)
        
        signal = strategy.generate_signal(data)
        
        return {
            "status": "success",
            "signal": {
                "symbol": signal.symbol,
                "action": signal.action,
                "confidence": signal.confidence,
                "entry_price": signal.entry_price,
                "target_price": signal.target_price,
                "stop_loss": signal.stop_loss,
                "reasoning": signal.reasoning,
                "timestamp": signal.timestamp.isoformat()
            },
            "strategy_used": request.strategy
        }
        
    except Exception as e:
        logger.error(f"خطأ في توليد إشارة التداول: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# مسارات إدارة المحافظ
@app.post("/api/v1/portfolio")
async def create_portfolio(request: PortfolioRequest):
    """إنشاء محفظة جديدة"""
    try:
        portfolio_data = {
            "id": f"portfolio_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "name": request.name,
            "initial_balance": request.initial_balance,
            "current_balance": request.initial_balance,
            "risk_tolerance": request.risk_tolerance,
            "investment_horizon": request.investment_horizon,
            "created_at": datetime.now().isoformat(),
            "positions": [],
            "performance": {
                "total_return": 0.0,
                "daily_return": 0.0,
                "volatility": 0.0,
                "sharpe_ratio": 0.0,
                "max_drawdown": 0.0
            }
        }
        
        return {
            "status": "success",
            "portfolio": portfolio_data
        }
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء المحفظة: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# مسارات الاختبار التاريخي
@app.post("/api/v1/backtest")
async def run_backtest(request: BacktestRequest):
    """تشغيل اختبار تاريخي"""
    try:
        # محاكاة نتائج الاختبار التاريخي
        days = (request.end_date - request.start_date).days
        
        # محاكاة عوائد يومية
        daily_returns = np.random.normal(0.001, 0.02, days)
        cumulative_returns = np.cumprod(1 + daily_returns)
        
        final_value = request.initial_capital * cumulative_returns[-1]
        total_return = (final_value - request.initial_capital) / request.initial_capital
        
        # حساب مقاييس الأداء
        volatility = np.std(daily_returns) * np.sqrt(252)
        sharpe_ratio = np.mean(daily_returns) / np.std(daily_returns) * np.sqrt(252)
        
        # أقصى انخفاض
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = np.min(drawdown)
        
        results = {
            "strategy": request.strategy,
            "period": {
                "start_date": request.start_date.isoformat(),
                "end_date": request.end_date.isoformat(),
                "duration_days": days
            },
            "performance": {
                "initial_capital": request.initial_capital,
                "final_value": final_value,
                "total_return": total_return,
                "annual_return": total_return * (365 / days),
                "volatility": volatility,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
                "win_rate": np.random.uniform(0.45, 0.65),
                "profit_factor": np.random.uniform(1.1, 2.5)
            },
            "trades": {
                "total_trades": np.random.randint(50, 200),
                "winning_trades": np.random.randint(25, 120),
                "losing_trades": np.random.randint(20, 80),
                "average_win": np.random.uniform(0.02, 0.05),
                "average_loss": np.random.uniform(-0.03, -0.01)
            }
        }
        
        return {
            "status": "success",
            "results": results,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"خطأ في الاختبار التاريخي: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket للبيانات الفورية
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            # استقبال رسائل من العميل
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # معالجة الاشتراكات
            if message.get("type") == "subscribe":
                topic = message.get("topic")
                if topic:
                    await manager.subscribe(websocket, topic)
                    await websocket.send_text(json.dumps({
                        "type": "subscription_confirmed",
                        "topic": topic
                    }))
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"خطأ في WebSocket: {e}")
        manager.disconnect(websocket)

# تشغيل الخادم
if __name__ == "__main__":
    uvicorn.run(
        "main_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
