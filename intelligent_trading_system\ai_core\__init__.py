"""
🧠 نواة الذكاء الاصطناعي لنظام التداول الذكي المتكامل
AI Core for Intelligent Trading System (ITS)

هذه الوحدة تحتوي على جميع نماذج وخوارزميات الذكاء الاصطناعي
المستخدمة في النظام، بما في ذلك:

- التعلم العميق (Deep Learning)
- التعلم المعزز (Reinforcement Learning) 
- معالجة اللغة الطبيعية (NLP)
- الرؤية الحاسوبية (Computer Vision)
"""

__version__ = "1.0.0"
__author__ = "M&M AI Trading System"

# استيراد المكونات الرئيسية
from .deep_learning import *
from .reinforcement_learning import *
from .nlp_sentiment import *
from .computer_vision import *

# تكوين الذكاء الاصطناعي العام
AI_CONFIG = {
    "model_precision": "float32",
    "device": "auto",  # auto, cpu, cuda
    "random_seed": 42,
    "model_save_path": "models/",
    "tensorboard_logs": "logs/tensorboard/",
    "checkpoint_frequency": 1000,
    "early_stopping_patience": 50
}

# إعدادات التعلم العميق
DEEP_LEARNING_CONFIG = {
    "lstm": {
        "sequence_length": 60,
        "hidden_units": [128, 64, 32],
        "dropout_rate": 0.2,
        "learning_rate": 0.001,
        "batch_size": 32,
        "epochs": 100
    },
    "cnn": {
        "input_shape": (224, 224, 3),
        "filters": [32, 64, 128, 256],
        "kernel_size": (3, 3),
        "pool_size": (2, 2),
        "dropout_rate": 0.25
    },
    "transformer": {
        "d_model": 512,
        "num_heads": 8,
        "num_layers": 6,
        "dff": 2048,
        "maximum_position_encoding": 10000,
        "dropout_rate": 0.1
    }
}

# إعدادات التعلم المعزز
REINFORCEMENT_LEARNING_CONFIG = {
    "environment": {
        "initial_balance": 100000,
        "transaction_cost": 0.001,
        "max_position": 1.0,
        "lookback_window": 30
    },
    "dqn": {
        "memory_size": 10000,
        "batch_size": 32,
        "learning_rate": 0.001,
        "gamma": 0.95,
        "epsilon_start": 1.0,
        "epsilon_end": 0.01,
        "epsilon_decay": 0.995,
        "target_update": 100
    },
    "ppo": {
        "learning_rate": 3e-4,
        "n_steps": 2048,
        "batch_size": 64,
        "n_epochs": 10,
        "gamma": 0.99,
        "gae_lambda": 0.95,
        "clip_range": 0.2,
        "ent_coef": 0.0,
        "vf_coef": 0.5
    }
}

# إعدادات معالجة اللغة الطبيعية
NLP_CONFIG = {
    "sentiment_analysis": {
        "model_name": "bert-base-uncased",
        "max_length": 512,
        "batch_size": 16,
        "learning_rate": 2e-5,
        "num_epochs": 3
    },
    "news_analysis": {
        "sources": ["reuters", "bloomberg", "cnbc", "marketwatch"],
        "update_frequency": 300,  # seconds
        "sentiment_threshold": 0.6,
        "relevance_threshold": 0.7
    },
    "social_media": {
        "platforms": ["twitter", "reddit", "stocktwits"],
        "keywords": ["trading", "stocks", "forex", "crypto"],
        "sentiment_window": 3600  # seconds
    }
}

# إعدادات الرؤية الحاسوبية
COMPUTER_VISION_CONFIG = {
    "chart_analysis": {
        "image_size": (224, 224),
        "pattern_types": [
            "head_and_shoulders",
            "double_top",
            "double_bottom",
            "triangle",
            "flag",
            "pennant",
            "cup_and_handle"
        ],
        "confidence_threshold": 0.8
    },
    "satellite_analysis": {
        "image_size": (512, 512),
        "analysis_types": [
            "agricultural_yield",
            "oil_storage",
            "retail_traffic",
            "shipping_activity"
        ],
        "update_frequency": 86400  # daily
    }
}

def initialize_ai_core():
    """تهيئة نواة الذكاء الاصطناعي"""
    import os
    import random
    import numpy as np
    
    # إعداد البذرة العشوائية للتكرار
    random.seed(AI_CONFIG["random_seed"])
    np.random.seed(AI_CONFIG["random_seed"])
    
    # إنشاء المجلدات المطلوبة
    os.makedirs(AI_CONFIG["model_save_path"], exist_ok=True)
    os.makedirs(AI_CONFIG["tensorboard_logs"], exist_ok=True)
    
    # تحديد الجهاز المستخدم
    device = AI_CONFIG["device"]
    if device == "auto":
        try:
            import torch
            device = "cuda" if torch.cuda.is_available() else "cpu"
        except ImportError:
            try:
                import tensorflow as tf
                device = "GPU" if tf.config.list_physical_devices('GPU') else "CPU"
            except ImportError:
                device = "cpu"
    
    AI_CONFIG["device"] = device
    
    print(f"🧠 تم تهيئة نواة الذكاء الاصطناعي")
    print(f"📱 الجهاز المستخدم: {device}")
    print(f"🎯 البذرة العشوائية: {AI_CONFIG['random_seed']}")
    
    return True

def get_model_info():
    """الحصول على معلومات النماذج المتاحة"""
    return {
        "deep_learning_models": [
            "LSTM Price Predictor",
            "CNN Pattern Recognition", 
            "Transformer Market Analysis",
            "Autoencoder Anomaly Detection",
            "Ensemble Trading Model"
        ],
        "reinforcement_learning_agents": [
            "DQN Trading Agent",
            "PPO Portfolio Manager",
            "A3C Multi-Asset Trader",
            "Multi-Agent Trading System"
        ],
        "nlp_models": [
            "News Sentiment Analyzer",
            "Social Media Monitor",
            "Financial Text Processor",
            "BERT Market Classifier"
        ],
        "computer_vision_models": [
            "Chart Pattern Detector",
            "Satellite Image Analyzer",
            "Technical Indicator Visualizer"
        ]
    }

# تصدير الوظائف والتكوينات الرئيسية
__all__ = [
    "AI_CONFIG",
    "DEEP_LEARNING_CONFIG", 
    "REINFORCEMENT_LEARNING_CONFIG",
    "NLP_CONFIG",
    "COMPUTER_VISION_CONFIG",
    "initialize_ai_core",
    "get_model_info"
]
