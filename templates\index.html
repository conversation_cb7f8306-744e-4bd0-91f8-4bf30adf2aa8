<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مؤشر الأسواق المالية - التشارت المباشر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        body {
            font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .price-card {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .price-positive {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }
        
        .price-negative {
            background: linear-gradient(135deg, #f44336, #da190b);
            color: white;
        }
        
        .price-neutral {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
        }
        
        .symbol {
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .price {
            font-size: 1.3rem;
            font-weight: bold;
        }
        
        .change {
            font-size: 0.9rem;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 1rem 0;
        }
        
        .category-title {
            color: white;
            text-align: center;
            margin: 2rem 0 1rem 0;
            font-size: 1.8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .live-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
            margin-left: 10px;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .status-bar {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem;
            text-align: center;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="text-center text-white">
                <i class="fas fa-chart-line"></i>
                مؤشر الأسواق المالية المباشر
                <span class="live-indicator"></span>
            </h1>
        </div>
    </div>

    <div class="container">
        <!-- العملات -->
        <h2 class="category-title">
            <i class="fas fa-coins"></i>
            العملات الأجنبية
        </h2>
        <div class="row" id="currencies-container">
            <!-- سيتم ملؤها بـ JavaScript -->
        </div>

        <!-- المؤشرات -->
        <h2 class="category-title">
            <i class="fas fa-chart-bar"></i>
            المؤشرات العالمية
        </h2>
        <div class="row" id="indices-container">
            <!-- سيتم ملؤها بـ JavaScript -->
        </div>

        <!-- السلع -->
        <h2 class="category-title">
            <i class="fas fa-oil-can"></i>
            السلع والمعادن
        </h2>
        <div class="row" id="commodities-container">
            <!-- سيتم ملؤها بـ JavaScript -->
        </div>

        <!-- العملات الرقمية -->
        <h2 class="category-title">
            <i class="fab fa-bitcoin"></i>
            العملات الرقمية
        </h2>
        <div class="row" id="crypto-container">
            <!-- سيتم ملؤها بـ JavaScript -->
        </div>

        <!-- التشارت الرئيسي -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-center">
                    <i class="fas fa-chart-line"></i>
                    التشارت المباشر
                </h3>
                <div class="text-center">
                    <select id="chart-selector" class="form-select" style="width: auto; display: inline-block;">
                        <option value="">اختر الرمز للعرض</option>
                    </select>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="main-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="status-bar">
        <span id="last-update">آخر تحديث: --</span>
        <span style="margin: 0 20px;">|</span>
        <span id="connection-status">متصل</span>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let mainChart = null;
        let updateInterval = null;

        // إعداد التشارت الرئيسي
        function initMainChart() {
            const ctx = document.getElementById('main-chart').getContext('2d');
            mainChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'السعر',
                        data: [],
                        borderColor: '#4CAF50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'الوقت'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: 'السعر'
                            }
                        }
                    },
                    animation: {
                        duration: 1000
                    }
                }
            });
        }

        // تحديث البيانات
        function updateData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    updatePriceCards(data);
                    updateLastUpdateTime();
                    document.getElementById('connection-status').textContent = 'متصل';
                })
                .catch(error => {
                    console.error('خطأ في جلب البيانات:', error);
                    document.getElementById('connection-status').textContent = 'غير متصل';
                });
        }

        // تحديث كروت الأسعار
        function updatePriceCards(data) {
            const categories = {
                'currencies': 'currencies-container',
                'indices': 'indices-container',
                'commodities': 'commodities-container',
                'crypto': 'crypto-container'
            };

            Object.keys(categories).forEach(category => {
                const container = document.getElementById(categories[category]);
                container.innerHTML = '';

                Object.keys(data[category]).forEach(symbol => {
                    const item = data[category][symbol];
                    const changeClass = item.change >= 0 ? 'price-positive' : 'price-negative';
                    const changeIcon = item.change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

                    const cardHtml = `
                        <div class="col-md-4 col-lg-3">
                            <div class="price-card ${changeClass}" onclick="selectSymbol('${category}', '${symbol}')">
                                <div class="symbol">${symbol}</div>
                                <div class="price">${item.price.toFixed(2)}</div>
                                <div class="change">
                                    <i class="fas ${changeIcon}"></i>
                                    ${item.change.toFixed(4)} (${item.change_percent.toFixed(2)}%)
                                </div>
                            </div>
                        </div>
                    `;
                    container.innerHTML += cardHtml;
                });
            });

            // تحديث قائمة التشارت
            updateChartSelector(data);
        }

        // تحديث قائمة اختيار التشارت
        function updateChartSelector(data) {
            const selector = document.getElementById('chart-selector');
            const currentValue = selector.value;
            selector.innerHTML = '<option value="">اختر الرمز للعرض</option>';

            Object.keys(data).forEach(category => {
                Object.keys(data[category]).forEach(symbol => {
                    const option = document.createElement('option');
                    option.value = `${category}:${symbol}`;
                    option.textContent = symbol;
                    selector.appendChild(option);
                });
            });

            if (currentValue) {
                selector.value = currentValue;
            }
        }

        // اختيار رمز للتشارت
        function selectSymbol(category, symbol) {
            document.getElementById('chart-selector').value = `${category}:${symbol}`;
            updateChart(category, symbol);
        }

        // تحديث التشارت
        function updateChart(category, symbol) {
            fetch(`/api/chart/${category}/${symbol}`)
                .then(response => response.json())
                .then(data => {
                    mainChart.data.labels = data.timestamps;
                    mainChart.data.datasets[0].data = data.prices;
                    mainChart.data.datasets[0].label = symbol;
                    mainChart.update();
                })
                .catch(error => {
                    console.error('خطأ في جلب بيانات التشارت:', error);
                });
        }

        // تحديث وقت آخر تحديث
        function updateLastUpdateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            document.getElementById('last-update').textContent = `آخر تحديث: ${timeString}`;
        }

        // معالج تغيير اختيار التشارت
        document.getElementById('chart-selector').addEventListener('change', function() {
            const value = this.value;
            if (value) {
                const [category, symbol] = value.split(':');
                updateChart(category, symbol);
            }
        });

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            initMainChart();
            updateData();
            
            // تحديث البيانات كل 3 ثوانٍ
            updateInterval = setInterval(updateData, 3000);
        });

        // تنظيف عند إغلاق الصفحة
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>
