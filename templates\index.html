<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 AI Trading Pro - نظام التداول الذكي المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            direction: rtl;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
            z-index: 1;
        }

        .main-container {
            display: flex;
            height: 100vh;
            position: relative;
            z-index: 2;
        }

        .sidebar {
            width: 350px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        .sidebar-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .sidebar-title {
            font-size: 1.1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: background 0.2s;
        }

        .sidebar-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .symbols-container {
            flex: 1;
            overflow-y: auto;
            padding: 1rem 0;
        }

        .symbol-category {
            margin-bottom: 1rem;
        }

        .category-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            font-size: 0.9rem;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .category-header:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .category-header.collapsed .fa-chevron-down {
            transform: rotate(-90deg);
        }

        .symbol-item {
            padding: 0.75rem 1.5rem;
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .symbol-item:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateX(5px);
        }

        .symbol-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-left: 4px solid #ffd700;
        }

        .symbol-name {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .symbol-description {
            font-size: 0.75rem;
            opacity: 0.8;
            margin-top: 0.2rem;
        }

        .symbol-price {
            font-weight: 700;
            font-size: 0.9rem;
        }

        .symbol-change {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            margin-top: 0.2rem;
        }

        .symbol-change.positive {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }

        .symbol-change.negative {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            position: relative;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chat-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            pointer-events: none;
        }

        .market-ticker {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: rgba(0, 0, 0, 0.2);
            overflow: hidden;
            display: flex;
            align-items: center;
        }

        .ticker-content {
            display: flex;
            animation: scroll 30s linear infinite;
            white-space: nowrap;
        }

        .ticker-item {
            margin-right: 2rem;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        @keyframes scroll {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .ai-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .header-info h1 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }

        .header-info p {
            font-size: 0.85rem;
            opacity: 0.9;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 0.5rem;
        }

        .header-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.5rem;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .header-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: #f7f7f8;
            scroll-behavior: smooth;
        }

        .message-group {
            margin-bottom: 1.5rem;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .message-group.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .user-avatar {
            background: #007bff;
            color: white;
        }

        .ai-avatar-msg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message-content {
            max-width: 70%;
            background: white;
            padding: 1rem 1.25rem;
            border-radius: 18px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .user .message-content {
            background: #007bff;
            color: white;
        }

        .message-text {
            line-height: 1.5;
            margin: 0;
        }

        .message-meta {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
            font-size: 0.75rem;
            opacity: 0.7;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.25rem;
            background: white;
            border-radius: 18px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .typing-dots {
            display: flex;
            gap: 0.25rem;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.5;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }
        
        .chat-input-container {
            padding: 1rem 1.5rem;
            background: white;
            border-top: 1px solid #e5e5e5;
        }

        .suggestions-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .suggestion-chip {
            background: #f0f0f0;
            border: 1px solid #e0e0e0;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
        }

        .suggestion-chip:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .input-wrapper {
            display: flex;
            align-items: flex-end;
            gap: 0.75rem;
            background: #f7f7f8;
            border-radius: 24px;
            padding: 0.75rem 1rem;
            border: 2px solid transparent;
            transition: border-color 0.2s;
        }

        .input-wrapper:focus-within {
            border-color: #667eea;
        }

        .chat-input {
            flex: 1;
            border: none;
            background: transparent;
            resize: none;
            outline: none;
            font-size: 1rem;
            line-height: 1.4;
            max-height: 120px;
            min-height: 24px;
        }

        .send-button {
            background: #667eea;
            border: none;
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .send-button:hover {
            background: #5a6fd8;
            transform: scale(1.05);
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .analysis-card {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin: 0.5rem 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e5e5;
        }

        .analysis-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
            font-weight: 600;
            color: #333;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 0.75rem;
            margin: 0.75rem 0;
        }

        .metric-item {
            text-align: center;
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metric-value {
            font-size: 1.25rem;
            font-weight: bold;
            color: #333;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .recommendation-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .buy-badge {
            background: #d4edda;
            color: #155724;
        }

        .sell-badge {
            background: #f8d7da;
            color: #721c24;
        }

        .hold-badge {
            background: #fff3cd;
            color: #856404;
        }

        .info-section {
            margin: 1rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .info-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .book-reference {
            font-size: 0.8rem;
            color: #666;
            font-style: italic;
            margin-top: 0.5rem;
        }

        .welcome-message {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .welcome-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 1rem 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .feature-list li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
        }

        .scrollbar-hide {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }

        /* تصميم التحليل المتقدم */
        .advanced-analysis {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
            border: 1px solid #dee2e6;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .advanced-analysis .analysis-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #dee2e6;
        }

        .advanced-analysis h3 {
            color: #495057;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .price-display {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }

        .current-price {
            font-size: 1.5rem;
            font-weight: 900;
            color: #495057;
        }

        .price-change {
            font-size: 1rem;
            font-weight: 600;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            margin-top: 0.25rem;
        }

        .price-change.positive {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }

        .price-change.negative {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .analysis-card {
            background: white;
            border-radius: 12px;
            padding: 1.25rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }

        .analysis-card h4 {
            color: #495057;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .indicator-grid, .risk-grid {
            display: grid;
            gap: 1rem;
        }

        .indicator-item, .risk-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .indicator-label {
            font-weight: 600;
            color: #495057;
        }

        .indicator-value {
            font-weight: 700;
            color: #212529;
        }

        .indicator-signal {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .indicator-signal.bullish {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }

        .indicator-signal.bearish {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }

        .indicator-signal.neutral {
            background: rgba(108, 117, 125, 0.2);
            color: #6c757d;
        }

        .recommendation-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .recommendation-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .recommendation-badge.شراء {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .recommendation-badge.بيع {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
        }

        .recommendation-badge.انتظار {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }

        .recommendation-reason {
            font-size: 1rem;
            color: #495057;
            margin-bottom: 1rem;
        }

        .expert-quote {
            font-style: italic;
            color: #6c757d;
            border-left: 4px solid #667eea;
            padding-left: 1rem;
            margin: 0;
        }

        /* تصميم نظرة عامة على السوق */
        .market-overview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 1rem 0;
        }

        .market-overview h3 {
            text-align: center;
            margin-bottom: 2rem;
            font-weight: 700;
        }

        .market-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .market-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .market-card h4 {
            margin-bottom: 1rem;
            font-weight: 600;
            text-align: center;
        }

        .market-stats {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-item span:first-child {
            font-weight: 600;
        }

        .stat-item span:last-child {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.25rem;
        }

        .stat-item small.positive {
            color: #28a745;
        }

        .stat-item small.negative {
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: 50vh;
                position: fixed;
                top: 0;
                z-index: 1000;
                transform: translateY(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateY(0);
            }

            .chat-container {
                height: 100vh;
                border-radius: 0;
            }

            .message-content {
                max-width: 85%;
            }

            .suggestions-container {
                overflow-x: auto;
                flex-wrap: nowrap;
                padding-bottom: 0.5rem;
            }

            .analysis-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">
                    <i class="fas fa-chart-line"></i>
                    الأسواق المالية
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="symbols-container" id="symbols-container">
                <!-- سيتم ملؤها بالجافاسكريبت -->
            </div>
        </div>

        <!-- Main Chat Container -->
        <div class="chat-container">
            <!-- Header -->
            <div class="chat-header">
                <div class="header-title">
                    <button class="sidebar-toggle" onclick="toggleSidebar()" style="margin-left: 1rem;">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="ai-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="header-info">
                        <h1>🚀 AI Trading Pro</h1>
                        <p>نظام التداول الذكي المتقدم - مدعوم بالذكاء الاصطناعي</p>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="header-btn" onclick="showMarketOverview()" title="نظرة عامة على السوق">
                        <i class="fas fa-globe"></i>
                    </button>
                    <button class="header-btn" onclick="showTechnicalAnalysis()" title="التحليل الفني">
                        <i class="fas fa-chart-area"></i>
                    </button>
                    <button class="header-btn" onclick="clearChat()" title="مسح المحادثة">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>

                <!-- Market Ticker -->
                <div class="market-ticker">
                    <div class="ticker-content" id="market-ticker">
                        <!-- سيتم ملؤها بالجافاسكريبت -->
                    </div>
                </div>
            </div>

        <!-- Messages Area -->
        <div class="chat-messages scrollbar-hide" id="chat-messages">
            <!-- Welcome Message -->
            <div class="welcome-message" id="welcome-message">
                <div class="welcome-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h3>مرحباً بك في مستشار التداول الذكي!</h3>
                <p>أنا مساعدك الذكي المدعوم بمعرفة أشهر كتب التداول العالمية</p>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <i class="fas fa-book"></i>
                        المصادر المعرفية
                    </div>
                    <ul class="feature-list">
                        <li>Technical Analysis - John Murphy</li>
                        <li>The Intelligent Investor - Benjamin Graham</li>
                        <li>Market Wizards - Jack Schwager</li>
                        <li>Trading in the Zone - Mark Douglas</li>
                        <li>Japanese Candlestick Charting - Steve Nison</li>
                    </ul>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <i class="fas fa-lightbulb"></i>
                        ما يمكنني مساعدتك فيه
                    </div>
                    <ul class="feature-list">
                        <li>التحليل الفني والمؤشرات</li>
                        <li>إدارة المخاطر ووقف الخسارة</li>
                        <li>علم نفس التداول</li>
                        <li>استراتيجيات التداول</li>
                        <li>تحليل الرموز المالية</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Input Area -->
        <div class="chat-input-container">
            <!-- Quick Suggestions -->
            <div class="suggestions-container" id="suggestions-container">
                <div class="suggestion-chip" onclick="askQuickQuestion('ما هو مؤشر RSI؟')">
                    <i class="fas fa-chart-line"></i> ما هو RSI؟
                </div>
                <div class="suggestion-chip" onclick="askQuickQuestion('كيف أحسب وقف الخسارة؟')">
                    <i class="fas fa-shield-alt"></i> وقف الخسارة
                </div>
                <div class="suggestion-chip" onclick="askQuickQuestion('ما هو نموذج الرأس والكتفين؟')">
                    <i class="fas fa-shapes"></i> الرأس والكتفين
                </div>
                <div class="suggestion-chip" onclick="askQuickQuestion('كيف أتحكم في عواطف التداول؟')">
                    <i class="fas fa-brain"></i> علم النفس
                </div>
                <div class="suggestion-chip" onclick="askQuickQuestion('حلل رمز EURUSD')">
                    <i class="fas fa-search"></i> تحليل رمز
                </div>
            </div>

            <!-- Input Box -->
            <div class="input-wrapper">
                <textarea
                    class="chat-input"
                    id="user-question"
                    placeholder="اسأل عن أي شيء متعلق بالتداول..."
                    rows="1"
                    onkeydown="handleKeyDown(event)"
                    oninput="autoResize(this)"
                ></textarea>
                <button class="send-button" id="send-button" onclick="askAI()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let symbols = [];
        let currentSymbol = null;
        let updateInterval = null;
        let sidebarCollapsed = false;
        let marketData = {};
        let chart = null;
        let technicalChart = null;
        let isAnalysisMode = false;

        // تحميل قائمة الرموز
        async function loadSymbols() {
            try {
                const response = await fetch('/api/symbols/all');
                symbols = await response.json();

                // تحميل الرموز مع التصنيفات للعرض
                const categorizedResponse = await fetch('/api/symbols');
                const categorizedSymbols = await categorizedResponse.json();

                updateSymbolsList(categorizedSymbols);
            } catch (error) {
                console.error('خطأ في تحميل الرموز:', error);
            }
        }

        // تحديث قائمة الرموز مع التصنيفات المتقدمة
        function updateSymbolsList(categorizedSymbols) {
            const container = document.getElementById('symbols-container');
            container.innerHTML = '';

            // إضافة رموز حسب الفئات
            Object.keys(categorizedSymbols).forEach(category => {
                // إنشاء فئة الرموز
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'symbol-category';

                // إضافة عنوان الفئة
                const categoryHeader = document.createElement('div');
                categoryHeader.className = 'category-header';
                categoryHeader.onclick = () => toggleCategory(category);

                const categoryNames = {
                    'forex': '💱 العملات الأجنبية',
                    'metals': '🥇 المعادن الثمينة',
                    'commodities': '🛢️ السلع',
                    'us_stocks': '📈 الأسهم الأمريكية',
                    'indices': '📊 المؤشرات العالمية',
                    'crypto': '₿ العملات الرقمية'
                };

                categoryHeader.innerHTML = `
                    <span>${categoryNames[category] || category}</span>
                    <i class="fas fa-chevron-down"></i>
                `;

                categoryDiv.appendChild(categoryHeader);

                // إضافة الرموز
                const symbolsContainer = document.createElement('div');
                symbolsContainer.className = 'symbols-list';
                symbolsContainer.id = `category-${category}`;

                Object.keys(categorizedSymbols[category]).forEach(symbol => {
                    const symbolDiv = document.createElement('div');
                    symbolDiv.className = 'symbol-item';
                    symbolDiv.onclick = () => selectSymbol(symbol);
                    symbolDiv.setAttribute('data-symbol', symbol);

                    symbolDiv.innerHTML = `
                        <div>
                            <div class="symbol-name">${symbol}</div>
                            <div class="symbol-description">${categorizedSymbols[category][symbol]}</div>
                        </div>
                        <div style="text-align: left;">
                            <div class="symbol-price" id="price-${symbol}">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                            <div class="symbol-change" id="change-${symbol}">--</div>
                        </div>
                    `;

                    symbolsContainer.appendChild(symbolDiv);
                });

                categoryDiv.appendChild(symbolsContainer);
                container.appendChild(categoryDiv);
            });
        }

        // تبديل عرض الفئة
        function toggleCategory(category) {
            const categoryElement = document.getElementById(`category-${category}`);
            const header = categoryElement.previousElementSibling;

            if (categoryElement.style.display === 'none') {
                categoryElement.style.display = 'block';
                header.classList.remove('collapsed');
            } else {
                categoryElement.style.display = 'none';
                header.classList.add('collapsed');
            }
        }

        // تبديل اللوحة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebarCollapsed = !sidebarCollapsed;

            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
            } else {
                sidebar.classList.remove('collapsed');
            }
        }

        // اختيار رمز مالي
        function selectSymbol(symbol) {
            // إزالة التحديد السابق
            document.querySelectorAll('.symbol-item').forEach(item => {
                item.classList.remove('active');
            });

            // تحديد الرمز الجديد
            const symbolElement = document.querySelector(`[data-symbol="${symbol}"]`);
            if (symbolElement) {
                symbolElement.classList.add('active');
            }

            currentSymbol = symbol;

            // تحليل الرمز
            analyzeSymbol(symbol);

            // تحديث الرسم البياني
            updateChart(symbol);
        }

        // تغيير حجم textarea تلقائياً
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // معالج الضغط على المفاتيح
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                askAI();
            }
        }

        // سؤال الذكاء الاصطناعي
        async function askAI() {
            const questionInput = document.getElementById('user-question');
            const question = questionInput.value.trim();

            if (!question) return;

            // إخفاء رسالة الترحيب
            hideWelcomeMessage();

            // إضافة رسالة المستخدم
            addUserMessage(question);
            questionInput.value = '';
            autoResize(questionInput);

            // إضافة مؤشر التحميل
            addTypingIndicator();

            try {
                // تحقق إذا كان السؤال يتضمن طلب تحليل رمز
                if (question.includes('حلل') || question.includes('تحليل')) {
                    const symbolMatch = question.match(/([A-Z]{3,6})/);
                    if (symbolMatch && symbols.includes(symbolMatch[1])) {
                        await analyzeSymbolInChat(symbolMatch[1]);
                        return;
                    }
                }

                const response = await fetch('/api/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ question })
                });

                const data = await response.json();

                // إزالة مؤشر التحميل
                removeTypingIndicator();

                // إضافة إجابة الذكاء الاصطناعي
                addAIMessage(data);

            } catch (error) {
                removeTypingIndicator();
                addAIMessage({
                    answer: 'عذراً، حدث خطأ في الاتصال. حاول مرة أخرى.',
                    error: true
                });
            }
        }

        // إخفاء رسالة الترحيب
        function hideWelcomeMessage() {
            const welcomeMessage = document.getElementById('welcome-message');
            if (welcomeMessage) {
                welcomeMessage.style.display = 'none';
            }
        }

        // إضافة رسالة المستخدم
        function addUserMessage(text) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageGroup = document.createElement('div');
            messageGroup.className = 'message-group user';

            messageGroup.innerHTML = `
                <div class="message-avatar user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="message-content">
                    <div class="message-text">${text}</div>
                </div>
            `;

            messagesContainer.appendChild(messageGroup);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // إضافة مؤشر الكتابة
        function addTypingIndicator() {
            const messagesContainer = document.getElementById('chat-messages');
            const messageGroup = document.createElement('div');
            messageGroup.className = 'message-group ai';
            messageGroup.id = 'typing-indicator';

            messageGroup.innerHTML = `
                <div class="message-avatar ai-avatar-msg">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="typing-indicator">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                    <span style="margin-right: 0.5rem;">يكتب...</span>
                </div>
            `;

            messagesContainer.appendChild(messageGroup);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // إزالة مؤشر الكتابة
        function removeTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // إضافة رسالة الذكاء الاصطناعي
        function addAIMessage(data) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageGroup = document.createElement('div');
            messageGroup.className = 'message-group ai';

            let contentHtml = `<div class="message-text">${data.answer}</div>`;

            // إضافة المعلومات الإضافية
            if (data.source) {
                contentHtml += `
                    <div class="info-section">
                        <div class="info-title">
                            <i class="fas fa-book"></i>
                            المصدر
                        </div>
                        <div>${data.source}</div>
                    </div>
                `;
            }

            if (data.practical_tip) {
                contentHtml += `
                    <div class="info-section">
                        <div class="info-title">
                            <i class="fas fa-lightbulb"></i>
                            نصيحة عملية
                        </div>
                        <div>${data.practical_tip}</div>
                    </div>
                `;
            }

            if (data.strategies && data.strategies.length > 0) {
                contentHtml += `
                    <div class="info-section">
                        <div class="info-title">
                            <i class="fas fa-list"></i>
                            الاستراتيجيات
                        </div>
                        <ul class="feature-list">
                            ${data.strategies.map(strategy => `<li>${strategy}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }

            if (data.topics && data.topics.length > 0) {
                contentHtml += `
                    <div class="info-section">
                        <div class="info-title">
                            <i class="fas fa-graduation-cap"></i>
                            يمكنني مساعدتك في
                        </div>
                        <ul class="feature-list">
                            ${data.topics.map(topic => `<li>${topic}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }

            if (data.book_reference) {
                contentHtml += `<div class="book-reference">📖 ${data.book_reference}</div>`;
            }

            if (data.wisdom) {
                contentHtml += `
                    <div class="info-section">
                        <div class="info-title">
                            <i class="fas fa-quote-left"></i>
                            حكمة
                        </div>
                        <div style="font-style: italic;">${data.wisdom}</div>
                    </div>
                `;
            }

            messageGroup.innerHTML = `
                <div class="message-avatar ai-avatar-msg">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    ${contentHtml}
                    <div class="message-meta">
                        <i class="fas fa-clock"></i>
                        <span>${new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}</span>
                    </div>
                </div>
            `;

            messagesContainer.appendChild(messageGroup);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // تحليل رمز في المحادثة
        async function analyzeSymbolInChat(symbol) {
            try {
                const response = await fetch(`/api/analyze/${symbol}`);
                const data = await response.json();

                removeTypingIndicator();

                if (data.error) {
                    addAIMessage({
                        answer: `عذراً، لم أتمكن من العثور على الرمز ${symbol}. تأكد من كتابة الرمز بشكل صحيح.`,
                        error: true
                    });
                    return;
                }

                // إنشاء رسالة تحليل مفصلة
                addAnalysisMessage(data, symbol);

            } catch (error) {
                removeTypingIndicator();
                addAIMessage({
                    answer: 'عذراً، حدث خطأ أثناء تحليل الرمز. حاول مرة أخرى.',
                    error: true
                });
            }
        }

        // إضافة رسالة تحليل
        function addAnalysisMessage(data, symbol) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageGroup = document.createElement('div');
            messageGroup.className = 'message-group ai';

            const analysis = data.analysis;
            const technical = data.technical;
            const recommendation = data.recommendation;

            let badgeClass = 'hold-badge';
            let badgeIcon = 'fas fa-pause';
            if (recommendation.action === 'شراء') {
                badgeClass = 'buy-badge';
                badgeIcon = 'fas fa-arrow-up';
            } else if (recommendation.action === 'بيع') {
                badgeClass = 'sell-badge';
                badgeIcon = 'fas fa-arrow-down';
            }

            const contentHtml = `
                <div class="message-text">
                    <strong>تحليل شامل لـ ${symbol}</strong>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <i class="fas fa-chart-line"></i>
                        معلومات السعر
                    </div>
                    <div class="metric-grid">
                        <div class="metric-item">
                            <div class="metric-value">${analysis.current_price}</div>
                            <div class="metric-label">السعر الحالي</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" style="color: ${analysis.change >= 0 ? '#28a745' : '#dc3545'}">
                                ${analysis.change_percent.toFixed(2)}%
                            </div>
                            <div class="metric-label">التغيير اليومي</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${analysis.volume.toLocaleString()}</div>
                            <div class="metric-label">حجم التداول</div>
                        </div>
                    </div>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <i class="fas fa-lightbulb"></i>
                        التوصية
                    </div>
                    <div style="text-align: center; margin: 1rem 0;">
                        <div class="recommendation-badge ${badgeClass}">
                            <i class="${badgeIcon}"></i>
                            ${recommendation.action} - ثقة ${recommendation.confidence}
                        </div>
                    </div>
                    <p>${recommendation.reasoning}</p>
                    <div class="book-reference">${recommendation.educational_quote}</div>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <i class="fas fa-chart-bar"></i>
                        التحليل الفني
                    </div>
                    <div class="metric-grid">
                        <div class="metric-item">
                            <div class="metric-value">${technical.rsi}</div>
                            <div class="metric-label">RSI</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${technical.trend_strength}</div>
                            <div class="metric-label">قوة الاتجاه</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${technical.support_level.toFixed(4)}</div>
                            <div class="metric-label">الدعم</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${technical.resistance_level.toFixed(4)}</div>
                            <div class="metric-label">المقاومة</div>
                        </div>
                    </div>

                    ${technical.patterns && technical.patterns.length > 0 ? `
                        <div style="margin-top: 1rem;">
                            <strong>النماذج المكتشفة:</strong>
                            ${technical.patterns.map(pattern => `
                                <div style="margin: 0.5rem 0; padding: 0.5rem; background: #f8f9fa; border-radius: 6px;">
                                    <strong>${pattern.pattern}</strong>
                                    <span class="recommendation-badge ${pattern.signal.includes('bullish') ? 'buy-badge' : 'sell-badge'}" style="font-size: 0.7rem; padding: 0.2rem 0.5rem; margin-right: 0.5rem;">
                                        ${pattern.signal}
                                    </span>
                                    <div style="font-size: 0.8rem; margin-top: 0.25rem;">${pattern.description}</div>
                                    ${pattern.reliability ? `<div style="font-size: 0.7rem; color: #666;">الموثوقية: ${pattern.reliability}%</div>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>

                ${data.educational_note ? `
                    <div class="info-section">
                        <div class="info-title">
                            <i class="fas fa-graduation-cap"></i>
                            ملاحظة تعليمية
                        </div>
                        <div>${data.educational_note}</div>
                    </div>
                ` : ''}
            `;

            messageGroup.innerHTML = `
                <div class="message-avatar ai-avatar-msg">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    ${contentHtml}
                    <div class="message-meta">
                        <i class="fas fa-clock"></i>
                        <span>${new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}</span>
                    </div>
                </div>
            `;

            messagesContainer.appendChild(messageGroup);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // سؤال سريع
        function askQuickQuestion(question) {
            document.getElementById('user-question').value = question;
            askAI();
        }

        // مسح المحادثة
        function clearChat() {
            const messagesContainer = document.getElementById('chat-messages');
            messagesContainer.innerHTML = '';
            document.getElementById('welcome-message').style.display = 'block';
            messagesContainer.appendChild(document.getElementById('welcome-message'));
        }

        // تبديل وضع التحليل
        function toggleAnalysis() {
            // يمكن إضافة وضع تحليل منفصل هنا
            askQuickQuestion('أظهر لي قائمة الرموز المتاحة للتحليل');
        }

        // تحديث أسعار الرموز مع تأثيرات بصرية متقدمة
        function updateSymbolPrices() {
            const prioritySymbols = [
                'EURUSD=X', 'GBPUSD=X', 'USDJPY=X', 'GC=F', 'CL=F',
                'AAPL', 'MSFT', 'GOOGL', 'TSLA', 'BTC-USD', 'ETH-USD',
                '^GSPC', '^IXIC', '^DJI'
            ];

            if (currentSymbol && !prioritySymbols.includes(currentSymbol)) {
                prioritySymbols.push(currentSymbol);
            }

            prioritySymbols.forEach(symbol => {
                const priceElement = document.getElementById(`price-${symbol}`);
                if (!priceElement) return;

                fetch(`/api/data/${symbol}`)
                    .then(response => response.json())
                    .then(data => {
                        const changeElement = document.getElementById(`change-${symbol}`);

                        if (priceElement && changeElement) {
                            // حفظ السعر السابق للمقارنة
                            const oldPrice = parseFloat(priceElement.textContent) || 0;
                            const newPrice = data.price;

                            // تحديد عدد الخانات العشرية
                            let decimals = 4;
                            if (symbol.includes('JPY')) decimals = 2;
                            else if (symbol.startsWith('^')) decimals = 2;
                            else if (symbol.includes('-USD')) decimals = 2;

                            // تحديث السعر مع تأثير بصري
                            priceElement.textContent = newPrice.toFixed(decimals);

                            // تأثير بصري للتغيير
                            if (oldPrice !== 0 && oldPrice !== newPrice) {
                                const symbolItem = priceElement.closest('.symbol-item');
                                if (newPrice > oldPrice) {
                                    symbolItem.style.background = 'rgba(40, 167, 69, 0.2)';
                                    setTimeout(() => symbolItem.style.background = '', 1000);
                                } else if (newPrice < oldPrice) {
                                    symbolItem.style.background = 'rgba(220, 53, 69, 0.2)';
                                    setTimeout(() => symbolItem.style.background = '', 1000);
                                }
                            }

                            const changePercent = data.change_percent || 0;
                            const changeClass = changePercent >= 0 ? 'positive' : 'negative';
                            changeElement.className = `symbol-change ${changeClass}`;
                            changeElement.textContent = `${changePercent.toFixed(2)}%`;

                            // تخزين البيانات للاستخدام اللاحق
                            marketData[symbol] = data;
                        }
                    })
                    .catch(error => {
                        console.error(`خطأ في تحديث ${symbol}:`, error);
                        if (priceElement) {
                            priceElement.innerHTML = '<i class="fas fa-exclamation-triangle" style="color: #ff4444;"></i>';
                        }
                    });
            });

            // تحديث شريط الأسعار المتحرك
            updateMarketTicker();
        }

        // تحديث شريط الأسعار المتحرك
        function updateMarketTicker() {
            const ticker = document.getElementById('market-ticker');
            const majorSymbols = ['EURUSD=X', 'GBPUSD=X', 'USDJPY=X', 'GC=F', 'AAPL', 'BTC-USD', '^GSPC'];

            let tickerHTML = '';
            majorSymbols.forEach(symbol => {
                const data = marketData[symbol];
                if (data) {
                    const changeClass = data.change_percent >= 0 ? 'positive' : 'negative';
                    const arrow = data.change_percent >= 0 ? '↗' : '↘';
                    tickerHTML += `
                        <div class="ticker-item">
                            <strong>${symbol}</strong>
                            <span>${data.price.toFixed(2)}</span>
                            <span class="${changeClass}">${arrow} ${data.change_percent.toFixed(2)}%</span>
                        </div>
                    `;
                }
            });

            ticker.innerHTML = tickerHTML;
        }

        // تحليل رمز مالي متقدم
        async function analyzeSymbol(symbol) {
            try {
                showTypingIndicator();

                const response = await fetch(`/api/analyze/${symbol}`);
                const analysis = await response.json();

                hideTypingIndicator();

                if (analysis.error) {
                    addMessage('ai', `عذراً، حدث خطأ في تحليل ${symbol}: ${analysis.error}`);
                    return;
                }

                // إنشاء رسالة تحليل متقدمة
                const analysisMessage = createAdvancedAnalysisMessage(analysis);
                addMessage('ai', analysisMessage);

                // تحديث الرسم البياني
                updateChart(symbol);

            } catch (error) {
                hideTypingIndicator();
                addMessage('ai', `عذراً، حدث خطأ في الاتصال بالخادم: ${error.message}`);
            }
        }

        // إنشاء رسالة تحليل متقدمة
        function createAdvancedAnalysisMessage(analysis) {
            const data = analysis.analysis;
            const technical = analysis.technical;
            const recommendation = analysis.recommendation;

            return `
                <div class="advanced-analysis">
                    <div class="analysis-header">
                        <h3><i class="fas fa-chart-line"></i> تحليل شامل لـ ${data.symbol}</h3>
                        <div class="price-display">
                            <span class="current-price">${data.current_price.toFixed(4)}</span>
                            <span class="price-change ${data.change_percent >= 0 ? 'positive' : 'negative'}">
                                ${data.change_percent >= 0 ? '↗' : '↘'} ${data.change_percent.toFixed(2)}%
                            </span>
                        </div>
                    </div>

                    <div class="analysis-grid">
                        <div class="analysis-card">
                            <h4><i class="fas fa-chart-area"></i> التحليل الفني</h4>
                            <div class="indicator-grid">
                                <div class="indicator-item">
                                    <span class="indicator-label">RSI</span>
                                    <span class="indicator-value">${technical.rsi.value}</span>
                                    <span class="indicator-signal ${technical.rsi.analysis.signal}">${technical.rsi.analysis.condition}</span>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">MACD</span>
                                    <span class="indicator-value">${technical.macd.values.macd_line.toFixed(4)}</span>
                                    <span class="indicator-signal ${technical.macd.analysis.signal}">${technical.macd.analysis.condition}</span>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">الاتجاه</span>
                                    <span class="indicator-value">${technical.trend_strength}</span>
                                    <span class="indicator-signal ${technical.moving_averages.analysis.signal}">${technical.moving_averages.analysis.condition}</span>
                                </div>
                            </div>
                        </div>

                        <div class="analysis-card">
                            <h4><i class="fas fa-shield-alt"></i> إدارة المخاطر</h4>
                            <div class="risk-grid">
                                <div class="risk-item">
                                    <span>وقف الخسارة (ATR)</span>
                                    <span>${analysis.risk.stop_loss_strategies.atr_based.long_position}</span>
                                </div>
                                <div class="risk-item">
                                    <span>الهدف الأول</span>
                                    <span>${analysis.risk.profit_targets.target_1}</span>
                                </div>
                                <div class="risk-item">
                                    <span>نسبة المخاطرة</span>
                                    <span>${analysis.risk.profit_targets.risk_reward_1}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="recommendation-section">
                        <div class="recommendation-badge ${recommendation.action.toLowerCase()}">
                            <i class="fas fa-${recommendation.action === 'شراء' ? 'arrow-up' : recommendation.action === 'بيع' ? 'arrow-down' : 'pause'}"></i>
                            ${recommendation.action} - ثقة ${recommendation.confidence}
                        </div>
                        <p class="recommendation-reason">${recommendation.reasoning}</p>
                        <p class="expert-quote"><i class="fas fa-quote-left"></i> ${recommendation.educational_quote}</p>
                    </div>
                </div>
            `;
        }

        // تحديث الرسم البياني
        async function updateChart(symbol) {
            if (!symbol) return;

            try {
                const response = await fetch(`/api/historical/${symbol}?period=1mo&interval=1d`);
                const data = await response.json();

                if (data.length === 0) return;

                // إنشاء رسم بياني متقدم
                createAdvancedChart(symbol, data);

            } catch (error) {
                console.error('خطأ في تحديث الرسم البياني:', error);
            }
        }

        // إنشاء رسم بياني متقدم
        function createAdvancedChart(symbol, data) {
            // إنشاء عنصر الرسم البياني إذا لم يكن موجوداً
            let chartContainer = document.getElementById('chart-container');
            if (!chartContainer) {
                chartContainer = document.createElement('div');
                chartContainer.id = 'chart-container';
                chartContainer.style.cssText = `
                    width: 100%;
                    height: 400px;
                    margin: 1rem 0;
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                    padding: 1rem;
                `;

                const messagesContainer = document.getElementById('chat-messages');
                messagesContainer.appendChild(chartContainer);
            }

            // إنشاء الرسم البياني باستخدام Chart.js
            const ctx = document.createElement('canvas');
            chartContainer.innerHTML = `<h4 style="text-align: center; margin-bottom: 1rem;"><i class="fas fa-chart-candlestick"></i> الرسم البياني لـ ${symbol}</h4>`;
            chartContainer.appendChild(ctx);

            const chartData = data.map(item => ({
                x: new Date(item.time * 1000),
                y: item.close
            }));

            if (chart) {
                chart.destroy();
            }

            chart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: symbol,
                        data: chartData,
                        borderColor: 'rgb(102, 126, 234)',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day'
                            }
                        },
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });
        }

        // عرض نظرة عامة على السوق
        function showMarketOverview() {
            const overviewMessage = `
                <div class="market-overview">
                    <h3><i class="fas fa-globe"></i> نظرة عامة على الأسواق العالمية</h3>
                    <div class="market-grid">
                        <div class="market-card">
                            <h4>📈 الأسهم الأمريكية</h4>
                            <div class="market-stats">
                                <div class="stat-item">
                                    <span>S&P 500</span>
                                    <span id="sp500-price">جاري التحميل...</span>
                                </div>
                                <div class="stat-item">
                                    <span>NASDAQ</span>
                                    <span id="nasdaq-price">جاري التحميل...</span>
                                </div>
                                <div class="stat-item">
                                    <span>DOW JONES</span>
                                    <span id="dow-price">جاري التحميل...</span>
                                </div>
                            </div>
                        </div>

                        <div class="market-card">
                            <h4>💱 العملات الرئيسية</h4>
                            <div class="market-stats">
                                <div class="stat-item">
                                    <span>EUR/USD</span>
                                    <span id="eurusd-price">جاري التحميل...</span>
                                </div>
                                <div class="stat-item">
                                    <span>GBP/USD</span>
                                    <span id="gbpusd-price">جاري التحميل...</span>
                                </div>
                                <div class="stat-item">
                                    <span>USD/JPY</span>
                                    <span id="usdjpy-price">جاري التحميل...</span>
                                </div>
                            </div>
                        </div>

                        <div class="market-card">
                            <h4>🥇 السلع</h4>
                            <div class="market-stats">
                                <div class="stat-item">
                                    <span>الذهب</span>
                                    <span id="gold-price">جاري التحميل...</span>
                                </div>
                                <div class="stat-item">
                                    <span>النفط</span>
                                    <span id="oil-price">جاري التحميل...</span>
                                </div>
                                <div class="stat-item">
                                    <span>البيتكوين</span>
                                    <span id="btc-price">جاري التحميل...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            addMessage('ai', overviewMessage);

            // تحديث البيانات
            updateMarketOverviewData();
        }

        // تحديث بيانات نظرة عامة على السوق
        function updateMarketOverviewData() {
            const symbols = {
                '^GSPC': 'sp500-price',
                '^IXIC': 'nasdaq-price',
                '^DJI': 'dow-price',
                'EURUSD=X': 'eurusd-price',
                'GBPUSD=X': 'gbpusd-price',
                'USDJPY=X': 'usdjpy-price',
                'GC=F': 'gold-price',
                'CL=F': 'oil-price',
                'BTC-USD': 'btc-price'
            };

            Object.keys(symbols).forEach(symbol => {
                fetch(`/api/data/${symbol}`)
                    .then(response => response.json())
                    .then(data => {
                        const element = document.getElementById(symbols[symbol]);
                        if (element) {
                            const changeClass = data.change_percent >= 0 ? 'positive' : 'negative';
                            element.innerHTML = `
                                <span>${data.price.toFixed(2)}</span>
                                <small class="${changeClass}">${data.change_percent.toFixed(2)}%</small>
                            `;
                        }
                    })
                    .catch(error => console.error(`خطأ في تحديث ${symbol}:`, error));
            });
        }

        // عرض التحليل الفني المتقدم
        function showTechnicalAnalysis() {
            if (!currentSymbol) {
                addMessage('ai', 'يرجى اختيار رمز مالي أولاً من القائمة الجانبية للحصول على التحليل الفني المتقدم.');
                return;
            }

            analyzeSymbol(currentSymbol);
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            loadSymbols();

            // تركيز على حقل الإدخال
            document.getElementById('user-question').focus();

            // بدء التحديث التلقائي للأسعار
            updateInterval = setInterval(() => {
                updateSymbolPrices();
            }, 15000); // كل 15 ثانية

            // تحديث أولي
            setTimeout(() => {
                updateSymbolPrices();
            }, 2000);

            // تأثيرات بصرية متقدمة
            gsap.from('.sidebar', {duration: 1, x: -350, ease: 'power2.out'});
            gsap.from('.chat-header', {duration: 1, y: -100, ease: 'power2.out'});
            gsap.from('.chat-messages', {duration: 1, opacity: 0, delay: 0.5});

            // إضافة مستمع للنقر خارج المحادثة
            document.addEventListener('click', function(event) {
                const suggestionsContainer = document.getElementById('suggestions-container');
                const inputWrapper = document.querySelector('.input-wrapper');

                if (!inputWrapper.contains(event.target)) {
                    // يمكن إضافة منطق إخفاء الاقتراحات هنا
                }
            });
        });

        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover على الاقتراحات
            const suggestions = document.querySelectorAll('.suggestion-chip');
            suggestions.forEach(suggestion => {
                suggestion.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                suggestion.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // تحديث حالة زر الإرسال
            const input = document.getElementById('user-question');
            const sendButton = document.getElementById('send-button');

            input.addEventListener('input', function() {
                if (this.value.trim()) {
                    sendButton.disabled = false;
                    sendButton.style.opacity = '1';
                } else {
                    sendButton.disabled = true;
                    sendButton.style.opacity = '0.5';
                }
            });

            // تعيين الحالة الأولية
            sendButton.disabled = true;
            sendButton.style.opacity = '0.5';
        });
    </script>
</body>
</html>
