<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة التداول المتقدمة - تشارت مثل MetaTrader</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #ffffff;
            direction: rtl;
            overflow: hidden;
        }

        .trading-container {
            display: flex;
            height: 100vh;
            background: #1e1e1e;
        }

        .sidebar {
            width: 300px;
            background: #2d2d2d;
            border-right: 1px solid #404040;
            padding: 1rem;
            overflow-y: auto;
        }

        .chart-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1e1e1e;
        }

        .toolbar {
            background: #2d2d2d;
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chart-container {
            flex: 1;
            position: relative;
            background: #1e1e1e;
        }

        .symbol-list {
            background: #2d2d2d;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .symbol-item {
            padding: 0.75rem;
            border-bottom: 1px solid #404040;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .symbol-item:hover {
            background: #404040;
        }

        .symbol-item.active {
            background: #0d6efd;
        }

        .symbol-item:last-child {
            border-bottom: none;
        }

        .symbol-name {
            font-weight: bold;
            font-size: 0.9rem;
        }

        .symbol-price {
            font-size: 0.85rem;
            color: #ffffff;
        }

        .symbol-change {
            font-size: 0.75rem;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
        }

        .positive {
            background: #198754;
            color: white;
        }

        .negative {
            background: #dc3545;
            color: white;
        }

        .neutral {
            background: #6c757d;
            color: white;
        }

        .timeframe-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .timeframe-btn {
            background: #404040;
            border: none;
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: background 0.2s;
        }

        .timeframe-btn:hover {
            background: #505050;
        }

        .timeframe-btn.active {
            background: #0d6efd;
        }

        .current-symbol {
            font-size: 1.2rem;
            font-weight: bold;
            color: #ffffff;
        }

        .current-price {
            font-size: 1.1rem;
            margin-left: 1rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.8rem;
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: #198754;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .market-info {
            background: #2d2d2d;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .market-info h5 {
            color: #ffffff;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.3rem;
            font-size: 0.85rem;
        }

        .info-label {
            color: #aaaaaa;
        }

        .info-value {
            color: #ffffff;
        }
    </style>
</head>
<body>
    <div class="trading-container">
        <!-- الشريط الجانبي -->
        <div class="sidebar">
            <!-- معلومات السوق -->
            <div class="market-info">
                <h5><i class="fas fa-chart-line"></i> معلومات السوق</h5>
                <div class="info-item">
                    <span class="info-label">الرمز الحالي:</span>
                    <span class="info-value" id="current-symbol-info">--</span>
                </div>
                <div class="info-item">
                    <span class="info-label">السعر:</span>
                    <span class="info-value" id="current-price-info">--</span>
                </div>
                <div class="info-item">
                    <span class="info-label">التغيير:</span>
                    <span class="info-value" id="current-change-info">--</span>
                </div>
                <div class="info-item">
                    <span class="info-label">آخر تحديث:</span>
                    <span class="info-value" id="last-update-info">--</span>
                </div>
            </div>

            <!-- قائمة الرموز -->
            <div class="symbol-list">
                <h5 style="padding: 1rem; margin: 0; color: #ffffff; border-bottom: 1px solid #404040;">
                    <i class="fas fa-list"></i> الرموز المالية
                </h5>
                <div id="symbols-container">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- منطقة التشارت -->
        <div class="chart-area">
            <!-- شريط الأدوات -->
            <div class="toolbar">
                <div style="display: flex; align-items: center;">
                    <span class="current-symbol" id="current-symbol">اختر رمز مالي</span>
                    <span class="current-price" id="current-price">--</span>
                </div>

                <div class="timeframe-buttons">
                    <button class="timeframe-btn active" data-timeframe="1m">1د</button>
                    <button class="timeframe-btn" data-timeframe="5m">5د</button>
                    <button class="timeframe-btn" data-timeframe="15m">15د</button>
                    <button class="timeframe-btn" data-timeframe="1h">1س</button>
                    <button class="timeframe-btn" data-timeframe="4h">4س</button>
                    <button class="timeframe-btn" data-timeframe="1d">1ي</button>
                </div>

                <div class="status-indicator">
                    <div class="live-dot"></div>
                    <span>مباشر</span>
                </div>
            </div>

            <!-- التشارت -->
            <div class="chart-container" id="chart-container">
                <!-- سيتم إنشاء التشارت هنا -->
            </div>
        </div>
    </div>

    <script>
        let chart = null;
        let currentSymbol = null;
        let updateInterval = null;
        let symbols = {};

        // إنشاء التشارت المتقدم
        function createChart() {
            const chartContainer = document.getElementById('chart-container');

            chart = LightweightCharts.createChart(chartContainer, {
                width: chartContainer.clientWidth,
                height: chartContainer.clientHeight,
                layout: {
                    background: { color: '#1e1e1e' },
                    textColor: '#ffffff',
                },
                grid: {
                    vertLines: { color: '#404040' },
                    horzLines: { color: '#404040' },
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                },
                rightPriceScale: {
                    borderColor: '#404040',
                    textColor: '#ffffff',
                },
                timeScale: {
                    borderColor: '#404040',
                    textColor: '#ffffff',
                    timeVisible: true,
                    secondsVisible: true,
                },
                watermark: {
                    color: 'rgba(255, 255, 255, 0.1)',
                    visible: true,
                    text: 'منصة التداول المتقدمة',
                    fontSize: 24,
                    horzAlign: 'center',
                    vertAlign: 'center',
                },
            });

            // إضافة خط السعر
            const lineSeries = chart.addLineSeries({
                color: '#00ff88',
                lineWidth: 2,
                crosshairMarkerVisible: true,
                crosshairMarkerRadius: 6,
                crosshairMarkerBorderColor: '#ffffff',
                crosshairMarkerBackgroundColor: '#00ff88',
                lastValueVisible: true,
                priceLineVisible: true,
            });

            return lineSeries;
        }

        // تحميل قائمة الرموز
        function loadSymbols() {
            fetch('/api/symbols')
                .then(response => response.json())
                .then(data => {
                    symbols = data;
                    updateSymbolsList();
                })
                .catch(error => {
                    console.error('خطأ في تحميل الرموز:', error);
                });
        }

        // تحديث قائمة الرموز
        function updateSymbolsList() {
            const container = document.getElementById('symbols-container');
            container.innerHTML = '';

            Object.keys(symbols).forEach(symbol => {
                const symbolDiv = document.createElement('div');
                symbolDiv.className = 'symbol-item';
                symbolDiv.onclick = () => selectSymbol(symbol);

                symbolDiv.innerHTML = `
                    <div>
                        <div class="symbol-name">${symbol}</div>
                        <div style="font-size: 0.75rem; color: #aaaaaa;">${symbols[symbol]}</div>
                    </div>
                    <div>
                        <div class="symbol-price" id="price-${symbol}">--</div>
                        <div class="symbol-change" id="change-${symbol}">--</div>
                    </div>
                `;

                container.appendChild(symbolDiv);
            });
        }

        // اختيار رمز
        function selectSymbol(symbol) {
            // إزالة التحديد السابق
            document.querySelectorAll('.symbol-item').forEach(item => {
                item.classList.remove('active');
            });

            // تحديد الرمز الجديد
            event.currentTarget.classList.add('active');
            currentSymbol = symbol;

            // تحديث العرض
            document.getElementById('current-symbol').textContent = symbol;
            document.getElementById('current-symbol-info').textContent = symbol;

            // تحديث التشارت
            updateChart();
        }

        // تحديث التشارت
        function updateChart() {
            if (!currentSymbol || !chart) return;

            fetch(`/api/chart/${currentSymbol}`)
                .then(response => response.json())
                .then(data => {
                    const lineSeries = chart.getAllSeries()[0];
                    if (lineSeries) {
                        // تحويل البيانات لتنسيق TradingView
                        const chartData = data.timestamps.map((time, index) => ({
                            time: Math.floor(Date.now() / 1000) - (data.timestamps.length - index) * 5, // 5 ثوانٍ بين كل نقطة
                            value: data.prices[index]
                        }));

                        lineSeries.setData(chartData);

                        // تحديث السعر الحالي
                        if (data.prices.length > 0) {
                            const currentPrice = data.prices[data.prices.length - 1];
                            document.getElementById('current-price').textContent = currentPrice.toFixed(4);
                        }
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحديث التشارت:', error);
                });
        }

        // تحديث أسعار الرموز
        function updateSymbolPrices() {
            Object.keys(symbols).forEach(symbol => {
                fetch(`/api/data/${symbol}`)
                    .then(response => response.json())
                    .then(data => {
                        const priceElement = document.getElementById(`price-${symbol}`);
                        const changeElement = document.getElementById(`change-${symbol}`);

                        if (priceElement && changeElement) {
                            priceElement.textContent = data.price.toFixed(4);

                            const changePercent = data.change_percent || 0;
                            const changeClass = changePercent >= 0 ? 'positive' : 'negative';
                            changeElement.className = `symbol-change ${changeClass}`;
                            changeElement.textContent = `${changePercent.toFixed(2)}%`;

                            // تحديث معلومات الرمز الحالي
                            if (symbol === currentSymbol) {
                                document.getElementById('current-price-info').textContent = data.price.toFixed(4);
                                document.getElementById('current-change-info').textContent = `${changePercent.toFixed(2)}%`;
                                document.getElementById('current-price').textContent = data.price.toFixed(4);

                                // تحديث لون السعر
                                const priceDisplay = document.getElementById('current-price');
                                priceDisplay.style.color = changePercent >= 0 ? '#00ff88' : '#ff4444';
                            }
                        }
                    })
                    .catch(error => {
                        console.error(`خطأ في تحديث ${symbol}:`, error);
                    });
            });

            // تحديث وقت آخر تحديث
            const now = new Date();
            document.getElementById('last-update-info').textContent = now.toLocaleTimeString('ar-SA');
        }

        // معالج أزرار الإطار الزمني
        function setupTimeframeButtons() {
            document.querySelectorAll('.timeframe-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.timeframe-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // هنا يمكن إضافة منطق تغيير الإطار الزمني
                    console.log('تم تغيير الإطار الزمني إلى:', this.dataset.timeframe);
                });
            });
        }

        // تغيير حجم التشارت عند تغيير حجم النافذة
        function setupResizeHandler() {
            window.addEventListener('resize', () => {
                if (chart) {
                    const chartContainer = document.getElementById('chart-container');
                    chart.applyOptions({
                        width: chartContainer.clientWidth,
                        height: chartContainer.clientHeight,
                    });
                }
            });
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء التشارت
            const lineSeries = createChart();

            // تحميل الرموز
            loadSymbols();

            // إعداد معالجات الأحداث
            setupTimeframeButtons();
            setupResizeHandler();

            // بدء التحديث التلقائي
            updateInterval = setInterval(() => {
                updateSymbolPrices();
                if (currentSymbol) {
                    updateChart();
                }
            }, 3000);

            // تحديث أولي
            setTimeout(() => {
                updateSymbolPrices();
            }, 1000);
        });

        // تنظيف عند إغلاق الصفحة
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>
