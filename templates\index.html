<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M&M AI - منصة التداول الذكية المتقدمة</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1e2a38 50%, #2d3e50 100%);
            color: #ffffff;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(10, 14, 26, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(0, 178, 255, 0.3);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: #00B2FF;
            text-decoration: none;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #00FF88;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Main Container */
        .main-container {
            margin-top: 80px;
            padding: 2rem;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Dashboard Grid */
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
            height: calc(100vh - 120px);
        }

        /* Market Data Section */
        .market-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(0, 178, 255, 0.2);
            overflow-y: auto;
        }

        .section-title {
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            color: #00B2FF;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Market Cards */
        .market-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .market-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid rgba(0, 178, 255, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .market-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 178, 255, 0.2);
        }

        .symbol-name {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #ffffff;
        }

        .price-display {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .price-change {
            font-size: 1rem;
            margin-bottom: 1rem;
        }

        .positive { color: #00FF88; }
        .negative { color: #FF4757; }
        .neutral { color: #FFC107; }

        .market-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #8B9DC3;
        }

        /* Chat Section */
        .chat-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(0, 178, 255, 0.2);
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .chat-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(0, 178, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .ai-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #00B2FF, #00FF88);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            max-height: calc(100vh - 300px);
        }

        .message {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 10px;
            max-width: 85%;
            word-wrap: break-word;
        }

        .user-message {
            background: rgba(0, 178, 255, 0.2);
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: rgba(0, 255, 136, 0.2);
            margin-right: auto;
        }

        .message-time {
            font-size: 0.8rem;
            color: #8B9DC3;
            margin-top: 0.5rem;
        }

        .chat-input-container {
            padding: 1rem;
            border-top: 1px solid rgba(0, 178, 255, 0.2);
        }

        .chat-input {
            display: flex;
            gap: 0.5rem;
        }

        .chat-input input {
            flex: 1;
            padding: 1rem;
            border: 1px solid rgba(0, 178, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
        }

        .chat-input input::placeholder {
            color: #8B9DC3;
        }

        .chat-input button {
            padding: 1rem 1.5rem;
            background: linear-gradient(45deg, #00B2FF, #00FF88);
            border: none;
            border-radius: 25px;
            color: #ffffff;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s;
            font-family: 'Cairo', sans-serif;
        }

        .chat-input button:hover {
            transform: scale(1.05);
        }

        .chat-input button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Loading Animation */
        .typing-indicator {
            display: none;
            padding: 1rem;
            background: rgba(0, 255, 136, 0.2);
            border-radius: 10px;
            margin-bottom: 1rem;
            max-width: 85%;
        }

        .typing-dots {
            display: flex;
            gap: 0.3rem;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00FF88;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        /* Real-time Updates */
        .update-flash {
            animation: flash 0.5s ease-in-out;
        }

        @keyframes flash {
            0% { background: rgba(0, 178, 255, 0.3); }
            100% { background: transparent; }
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .dashboard {
                grid-template-columns: 1fr;
                grid-template-rows: 1fr 500px;
            }
            
            .market-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }
            
            .nav {
                padding: 0 1rem;
            }
            
            .logo {
                font-size: 1.5rem;
            }
        }

        /* Connection Status */
        .connection-status {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            z-index: 1000;
        }

        .connected {
            background: rgba(0, 255, 136, 0.2);
            color: #00FF88;
            border: 1px solid #00FF88;
        }

        .disconnected {
            background: rgba(255, 71, 87, 0.2);
            color: #FF4757;
            border: 1px solid #FF4757;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <a href="#" class="logo">
                <i class="fas fa-chart-line"></i> M&M AI Trading
            </a>
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>البيانات المباشرة</span>
            </div>
        </nav>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <div class="dashboard">
            <!-- Market Data Section -->
            <div class="market-section">
                <h2 class="section-title">
                    <i class="fas fa-chart-bar"></i>
                    البيانات المباشرة - تحديث كل ثانية
                </h2>
                
                <div class="market-grid" id="marketGrid">
                    <!-- Market cards will be populated by JavaScript -->
                </div>
            </div>

            <!-- AI Chat Section -->
            <div class="chat-section">
                <div class="chat-header">
                    <div class="ai-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div>
                        <h3>M&M AI Trading Expert</h3>
                        <p style="color: #8B9DC3; font-size: 0.9rem;">مساعدك الذكي للتداول</p>
                    </div>
                </div>

                <div class="chat-messages" id="chatMessages">
                    <div class="message ai-message">
                        <strong>🤖 M&M AI:</strong> مرحباً! أنا مساعدك الذكي المتخصص في التداول والتحليل المالي.<br><br>
                        يمكنني مساعدتك في:<br>
                        📊 التحليل الفني المتقدم<br>
                        💰 تحليل الأسعار والتوقعات<br>
                        🎯 استراتيجيات التداول<br>
                        🛡️ إدارة المخاطر<br>
                        📚 التعليم والنصائح<br><br>
                        ما الذي تريد معرفته اليوم؟
                        <div class="message-time" id="welcomeTime"></div>
                    </div>
                </div>

                <div class="typing-indicator" id="typingIndicator">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>

                <div class="chat-input-container">
                    <div class="chat-input">
                        <input type="text" id="messageInput" placeholder="اكتب سؤالك هنا... (مثل: حلل لي AAPL)" />
                        <button id="sendButton" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Connection Status -->
    <div class="connection-status connected" id="connectionStatus">
        <i class="fas fa-wifi"></i> متصل
    </div>

    <script>
        // إعداد Socket.IO
        const socket = io();
        let isConnected = false;
        let marketData = {};

        // عناصر DOM
        const marketGrid = document.getElementById('marketGrid');
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const typingIndicator = document.getElementById('typingIndicator');
        const connectionStatus = document.getElementById('connectionStatus');

        // تهيئة الوقت للرسالة الترحيبية
        document.getElementById('welcomeTime').textContent = new Date().toLocaleTimeString('ar-SA');

        // معالجة الاتصال
        socket.on('connect', function() {
            isConnected = true;
            updateConnectionStatus(true);
            console.log('🔗 Connected to server');
        });

        socket.on('disconnect', function() {
            isConnected = false;
            updateConnectionStatus(false);
            console.log('🔌 Disconnected from server');
        });

        // تحديث حالة الاتصال
        function updateConnectionStatus(connected) {
            if (connected) {
                connectionStatus.className = 'connection-status connected';
                connectionStatus.innerHTML = '<i class="fas fa-wifi"></i> متصل';
            } else {
                connectionStatus.className = 'connection-status disconnected';
                connectionStatus.innerHTML = '<i class="fas fa-wifi-slash"></i> غير متصل';
            }
        }

        // استقبال تحديثات السوق
        socket.on('market_update', function(data) {
            marketData[data.symbol] = data;
            updateMarketCard(data);
        });

        // إنشاء/تحديث بطاقة السوق
        function updateMarketCard(data) {
            let card = document.getElementById(`card-${data.symbol}`);

            if (!card) {
                card = createMarketCard(data);
                marketGrid.appendChild(card);
            } else {
                // تحديث البيانات مع تأثير بصري
                card.classList.add('update-flash');
                setTimeout(() => card.classList.remove('update-flash'), 500);
            }

            // تحديث المحتوى
            const priceElement = card.querySelector('.price-display');
            const changeElement = card.querySelector('.price-change');
            const volumeElement = card.querySelector('.volume');
            const timeElement = card.querySelector('.update-time');

            priceElement.textContent = `$${data.price.toFixed(2)}`;

            const changeText = `${data.change >= 0 ? '+' : ''}${data.change.toFixed(2)} (${data.change_percent >= 0 ? '+' : ''}${data.change_percent.toFixed(2)}%)`;
            changeElement.textContent = changeText;
            changeElement.className = `price-change ${data.change >= 0 ? 'positive' : 'negative'}`;

            if (volumeElement) {
                volumeElement.textContent = formatNumber(data.volume);
            }

            if (timeElement) {
                timeElement.textContent = new Date(data.timestamp).toLocaleTimeString('ar-SA');
            }
        }

        // إنشاء بطاقة سوق جديدة
        function createMarketCard(data) {
            const card = document.createElement('div');
            card.className = 'market-card';
            card.id = `card-${data.symbol}`;

            const symbolName = getSymbolDisplayName(data.symbol);
            const changeClass = data.change >= 0 ? 'positive' : 'negative';

            card.innerHTML = `
                <div class="symbol-name">
                    ${getSymbolIcon(data.symbol)} ${symbolName}
                </div>
                <div class="price-display">$${data.price.toFixed(2)}</div>
                <div class="price-change ${changeClass}">
                    ${data.change >= 0 ? '+' : ''}${data.change.toFixed(2)} (${data.change_percent >= 0 ? '+' : ''}${data.change_percent.toFixed(2)}%)
                </div>
                <div class="market-details">
                    <div><strong>الحجم:</strong> <span class="volume">${formatNumber(data.volume)}</span></div>
                    <div><strong>الأعلى:</strong> $${data.high.toFixed(2)}</div>
                    <div><strong>الأدنى:</strong> $${data.low.toFixed(2)}</div>
                    <div><strong>التحديث:</strong> <span class="update-time">${new Date(data.timestamp).toLocaleTimeString('ar-SA')}</span></div>
                </div>
            `;

            return card;
        }

        // الحصول على اسم الرمز للعرض
        function getSymbolDisplayName(symbol) {
            const names = {
                'AAPL': 'Apple Inc.',
                'GOOGL': 'Alphabet Inc.',
                'MSFT': 'Microsoft Corp.',
                'TSLA': 'Tesla Inc.',
                'AMZN': 'Amazon.com Inc.',
                'GC=F': 'الذهب',
                'SI=F': 'الفضة',
                'CL=F': 'النفط الخام',
                'BTC-USD': 'Bitcoin',
                'ETH-USD': 'Ethereum',
                'EURUSD=X': 'EUR/USD',
                'GBPUSD=X': 'GBP/USD',
                'USDJPY=X': 'USD/JPY'
            };
            return names[symbol] || symbol;
        }

        // الحصول على أيقونة الرمز
        function getSymbolIcon(symbol) {
            const icons = {
                'AAPL': '<i class="fab fa-apple"></i>',
                'GOOGL': '<i class="fab fa-google"></i>',
                'MSFT': '<i class="fab fa-microsoft"></i>',
                'TSLA': '<i class="fas fa-car"></i>',
                'AMZN': '<i class="fab fa-amazon"></i>',
                'GC=F': '<i class="fas fa-coins"></i>',
                'SI=F': '<i class="fas fa-medal"></i>',
                'CL=F': '<i class="fas fa-oil-can"></i>',
                'BTC-USD': '<i class="fab fa-bitcoin"></i>',
                'ETH-USD': '<i class="fab fa-ethereum"></i>',
                'EURUSD=X': '<i class="fas fa-euro-sign"></i>',
                'GBPUSD=X': '<i class="fas fa-pound-sign"></i>',
                'USDJPY=X': '<i class="fas fa-yen-sign"></i>'
            };
            return icons[symbol] || '<i class="fas fa-chart-line"></i>';
        }

        // تنسيق الأرقام
        function formatNumber(num) {
            if (num >= 1000000000) {
                return (num / 1000000000).toFixed(1) + 'B';
            } else if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        // معالجة رسائل الدردشة
        socket.on('chat_response', function(data) {
            hideTypingIndicator();
            addMessage(data.message, 'ai', data.timestamp);
            enableChatInput();
        });

        socket.on('chat_error', function(data) {
            hideTypingIndicator();
            addMessage(data.error, 'ai', data.timestamp, true);
            enableChatInput();
        });

        // إرسال رسالة
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || !isConnected) return;

            // إضافة رسالة المستخدم
            addMessage(message, 'user');

            // إرسال للخادم
            socket.emit('chat_message', {
                message: message,
                user_id: 'user_' + Date.now()
            });

            // تنظيف وتعطيل الإدخال
            messageInput.value = '';
            disableChatInput();
            showTypingIndicator();
        }

        // إضافة رسالة للدردشة
        function addMessage(content, sender, timestamp = null, isError = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;

            const time = timestamp ? new Date(timestamp).toLocaleTimeString('ar-SA') : new Date().toLocaleTimeString('ar-SA');
            const senderName = sender === 'user' ? '👤 أنت' : '🤖 M&M AI';

            if (isError) {
                messageDiv.style.borderLeft = '3px solid #FF4757';
            }

            messageDiv.innerHTML = `
                <strong>${senderName}:</strong> ${content}
                <div class="message-time">${time}</div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // إظهار مؤشر الكتابة
        function showTypingIndicator() {
            typingIndicator.style.display = 'block';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // إخفاء مؤشر الكتابة
        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }

        // تعطيل إدخال الدردشة
        function disableChatInput() {
            messageInput.disabled = true;
            sendButton.disabled = true;
        }

        // تفعيل إدخال الدردشة
        function enableChatInput() {
            messageInput.disabled = false;
            sendButton.disabled = false;
            messageInput.focus();
        }

        // معالجة الضغط على Enter
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // تحديث دوري لحالة الاتصال
        setInterval(() => {
            if (!isConnected) {
                socket.connect();
            }
        }, 5000);

        console.log('🚀 M&M AI Trading Platform loaded successfully');
    </script>
</body>
</html>
