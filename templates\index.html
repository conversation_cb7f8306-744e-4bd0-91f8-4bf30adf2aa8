<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستشار التداول الذكي - AI Trading Assistant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f7f7f8;
            direction: rtl;
            height: 100vh;
            overflow: hidden;
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .ai-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .header-info h1 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }

        .header-info p {
            font-size: 0.85rem;
            opacity: 0.9;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 0.5rem;
        }

        .header-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.5rem;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .header-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: #f7f7f8;
            scroll-behavior: smooth;
        }

        .message-group {
            margin-bottom: 1.5rem;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .message-group.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .user-avatar {
            background: #007bff;
            color: white;
        }

        .ai-avatar-msg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message-content {
            max-width: 70%;
            background: white;
            padding: 1rem 1.25rem;
            border-radius: 18px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .user .message-content {
            background: #007bff;
            color: white;
        }

        .message-text {
            line-height: 1.5;
            margin: 0;
        }

        .message-meta {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
            font-size: 0.75rem;
            opacity: 0.7;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.25rem;
            background: white;
            border-radius: 18px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .typing-dots {
            display: flex;
            gap: 0.25rem;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.5;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }
        
        .chat-input-container {
            padding: 1rem 1.5rem;
            background: white;
            border-top: 1px solid #e5e5e5;
        }

        .suggestions-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .suggestion-chip {
            background: #f0f0f0;
            border: 1px solid #e0e0e0;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
        }

        .suggestion-chip:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .input-wrapper {
            display: flex;
            align-items: flex-end;
            gap: 0.75rem;
            background: #f7f7f8;
            border-radius: 24px;
            padding: 0.75rem 1rem;
            border: 2px solid transparent;
            transition: border-color 0.2s;
        }

        .input-wrapper:focus-within {
            border-color: #667eea;
        }

        .chat-input {
            flex: 1;
            border: none;
            background: transparent;
            resize: none;
            outline: none;
            font-size: 1rem;
            line-height: 1.4;
            max-height: 120px;
            min-height: 24px;
        }

        .send-button {
            background: #667eea;
            border: none;
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .send-button:hover {
            background: #5a6fd8;
            transform: scale(1.05);
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .analysis-card {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin: 0.5rem 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e5e5;
        }

        .analysis-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
            font-weight: 600;
            color: #333;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 0.75rem;
            margin: 0.75rem 0;
        }

        .metric-item {
            text-align: center;
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metric-value {
            font-size: 1.25rem;
            font-weight: bold;
            color: #333;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .recommendation-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .buy-badge {
            background: #d4edda;
            color: #155724;
        }

        .sell-badge {
            background: #f8d7da;
            color: #721c24;
        }

        .hold-badge {
            background: #fff3cd;
            color: #856404;
        }

        .info-section {
            margin: 1rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .info-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .book-reference {
            font-size: 0.8rem;
            color: #666;
            font-style: italic;
            margin-top: 0.5rem;
        }

        .welcome-message {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .welcome-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 1rem 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .feature-list li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
        }

        .scrollbar-hide {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }

        @media (max-width: 768px) {
            .chat-container {
                height: 100vh;
                border-radius: 0;
            }

            .message-content {
                max-width: 85%;
            }

            .suggestions-container {
                overflow-x: auto;
                flex-wrap: nowrap;
                padding-bottom: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- Header -->
        <div class="chat-header">
            <div class="header-title">
                <div class="ai-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="header-info">
                    <h1>مستشار التداول الذكي</h1>
                    <p>مدعوم بمعرفة أشهر كتب التداول العالمية</p>
                </div>
            </div>
            <div class="header-actions">
                <button class="header-btn" onclick="clearChat()" title="مسح المحادثة">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="header-btn" onclick="toggleAnalysis()" title="تحليل الرموز">
                    <i class="fas fa-chart-line"></i>
                </button>
            </div>
        </div>

        <!-- Messages Area -->
        <div class="chat-messages scrollbar-hide" id="chat-messages">
            <!-- Welcome Message -->
            <div class="welcome-message" id="welcome-message">
                <div class="welcome-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h3>مرحباً بك في مستشار التداول الذكي!</h3>
                <p>أنا مساعدك الذكي المدعوم بمعرفة أشهر كتب التداول العالمية</p>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <i class="fas fa-book"></i>
                        المصادر المعرفية
                    </div>
                    <ul class="feature-list">
                        <li>Technical Analysis - John Murphy</li>
                        <li>The Intelligent Investor - Benjamin Graham</li>
                        <li>Market Wizards - Jack Schwager</li>
                        <li>Trading in the Zone - Mark Douglas</li>
                        <li>Japanese Candlestick Charting - Steve Nison</li>
                    </ul>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <i class="fas fa-lightbulb"></i>
                        ما يمكنني مساعدتك فيه
                    </div>
                    <ul class="feature-list">
                        <li>التحليل الفني والمؤشرات</li>
                        <li>إدارة المخاطر ووقف الخسارة</li>
                        <li>علم نفس التداول</li>
                        <li>استراتيجيات التداول</li>
                        <li>تحليل الرموز المالية</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Input Area -->
        <div class="chat-input-container">
            <!-- Quick Suggestions -->
            <div class="suggestions-container" id="suggestions-container">
                <div class="suggestion-chip" onclick="askQuickQuestion('ما هو مؤشر RSI؟')">
                    <i class="fas fa-chart-line"></i> ما هو RSI؟
                </div>
                <div class="suggestion-chip" onclick="askQuickQuestion('كيف أحسب وقف الخسارة؟')">
                    <i class="fas fa-shield-alt"></i> وقف الخسارة
                </div>
                <div class="suggestion-chip" onclick="askQuickQuestion('ما هو نموذج الرأس والكتفين؟')">
                    <i class="fas fa-shapes"></i> الرأس والكتفين
                </div>
                <div class="suggestion-chip" onclick="askQuickQuestion('كيف أتحكم في عواطف التداول؟')">
                    <i class="fas fa-brain"></i> علم النفس
                </div>
                <div class="suggestion-chip" onclick="askQuickQuestion('حلل رمز EURUSD')">
                    <i class="fas fa-search"></i> تحليل رمز
                </div>
            </div>

            <!-- Input Box -->
            <div class="input-wrapper">
                <textarea
                    class="chat-input"
                    id="user-question"
                    placeholder="اسأل عن أي شيء متعلق بالتداول..."
                    rows="1"
                    onkeydown="handleKeyDown(event)"
                    oninput="autoResize(this)"
                ></textarea>
                <button class="send-button" id="send-button" onclick="askAI()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        let symbols = [];
        let isAnalysisMode = false;

        // تحميل قائمة الرموز
        async function loadSymbols() {
            try {
                const response = await fetch('/api/symbols');
                symbols = await response.json();
            } catch (error) {
                console.error('خطأ في تحميل الرموز:', error);
            }
        }

        // تغيير حجم textarea تلقائياً
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // معالج الضغط على المفاتيح
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                askAI();
            }
        }

        // سؤال الذكاء الاصطناعي
        async function askAI() {
            const questionInput = document.getElementById('user-question');
            const question = questionInput.value.trim();

            if (!question) return;

            // إخفاء رسالة الترحيب
            hideWelcomeMessage();

            // إضافة رسالة المستخدم
            addUserMessage(question);
            questionInput.value = '';
            autoResize(questionInput);

            // إضافة مؤشر التحميل
            addTypingIndicator();

            try {
                // تحقق إذا كان السؤال يتضمن طلب تحليل رمز
                if (question.includes('حلل') || question.includes('تحليل')) {
                    const symbolMatch = question.match(/([A-Z]{3,6})/);
                    if (symbolMatch && symbols.includes(symbolMatch[1])) {
                        await analyzeSymbolInChat(symbolMatch[1]);
                        return;
                    }
                }

                const response = await fetch('/api/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ question })
                });

                const data = await response.json();

                // إزالة مؤشر التحميل
                removeTypingIndicator();

                // إضافة إجابة الذكاء الاصطناعي
                addAIMessage(data);

            } catch (error) {
                removeTypingIndicator();
                addAIMessage({
                    answer: 'عذراً، حدث خطأ في الاتصال. حاول مرة أخرى.',
                    error: true
                });
            }
        }

        // إخفاء رسالة الترحيب
        function hideWelcomeMessage() {
            const welcomeMessage = document.getElementById('welcome-message');
            if (welcomeMessage) {
                welcomeMessage.style.display = 'none';
            }
        }

        // إضافة رسالة المستخدم
        function addUserMessage(text) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageGroup = document.createElement('div');
            messageGroup.className = 'message-group user';

            messageGroup.innerHTML = `
                <div class="message-avatar user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="message-content">
                    <div class="message-text">${text}</div>
                </div>
            `;

            messagesContainer.appendChild(messageGroup);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // إضافة مؤشر الكتابة
        function addTypingIndicator() {
            const messagesContainer = document.getElementById('chat-messages');
            const messageGroup = document.createElement('div');
            messageGroup.className = 'message-group ai';
            messageGroup.id = 'typing-indicator';

            messageGroup.innerHTML = `
                <div class="message-avatar ai-avatar-msg">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="typing-indicator">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                    <span style="margin-right: 0.5rem;">يكتب...</span>
                </div>
            `;

            messagesContainer.appendChild(messageGroup);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // إزالة مؤشر الكتابة
        function removeTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // إضافة رسالة الذكاء الاصطناعي
        function addAIMessage(data) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageGroup = document.createElement('div');
            messageGroup.className = 'message-group ai';

            let contentHtml = `<div class="message-text">${data.answer}</div>`;

            // إضافة المعلومات الإضافية
            if (data.source) {
                contentHtml += `
                    <div class="info-section">
                        <div class="info-title">
                            <i class="fas fa-book"></i>
                            المصدر
                        </div>
                        <div>${data.source}</div>
                    </div>
                `;
            }

            if (data.practical_tip) {
                contentHtml += `
                    <div class="info-section">
                        <div class="info-title">
                            <i class="fas fa-lightbulb"></i>
                            نصيحة عملية
                        </div>
                        <div>${data.practical_tip}</div>
                    </div>
                `;
            }

            if (data.strategies && data.strategies.length > 0) {
                contentHtml += `
                    <div class="info-section">
                        <div class="info-title">
                            <i class="fas fa-list"></i>
                            الاستراتيجيات
                        </div>
                        <ul class="feature-list">
                            ${data.strategies.map(strategy => `<li>${strategy}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }

            if (data.topics && data.topics.length > 0) {
                contentHtml += `
                    <div class="info-section">
                        <div class="info-title">
                            <i class="fas fa-graduation-cap"></i>
                            يمكنني مساعدتك في
                        </div>
                        <ul class="feature-list">
                            ${data.topics.map(topic => `<li>${topic}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }

            if (data.book_reference) {
                contentHtml += `<div class="book-reference">📖 ${data.book_reference}</div>`;
            }

            if (data.wisdom) {
                contentHtml += `
                    <div class="info-section">
                        <div class="info-title">
                            <i class="fas fa-quote-left"></i>
                            حكمة
                        </div>
                        <div style="font-style: italic;">${data.wisdom}</div>
                    </div>
                `;
            }

            messageGroup.innerHTML = `
                <div class="message-avatar ai-avatar-msg">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    ${contentHtml}
                    <div class="message-meta">
                        <i class="fas fa-clock"></i>
                        <span>${new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}</span>
                    </div>
                </div>
            `;

            messagesContainer.appendChild(messageGroup);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // تحليل رمز في المحادثة
        async function analyzeSymbolInChat(symbol) {
            try {
                const response = await fetch(`/api/analyze/${symbol}`);
                const data = await response.json();

                removeTypingIndicator();

                if (data.error) {
                    addAIMessage({
                        answer: `عذراً، لم أتمكن من العثور على الرمز ${symbol}. تأكد من كتابة الرمز بشكل صحيح.`,
                        error: true
                    });
                    return;
                }

                // إنشاء رسالة تحليل مفصلة
                addAnalysisMessage(data, symbol);

            } catch (error) {
                removeTypingIndicator();
                addAIMessage({
                    answer: 'عذراً، حدث خطأ أثناء تحليل الرمز. حاول مرة أخرى.',
                    error: true
                });
            }
        }

        // إضافة رسالة تحليل
        function addAnalysisMessage(data, symbol) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageGroup = document.createElement('div');
            messageGroup.className = 'message-group ai';

            const analysis = data.analysis;
            const technical = data.technical;
            const recommendation = data.recommendation;

            let badgeClass = 'hold-badge';
            let badgeIcon = 'fas fa-pause';
            if (recommendation.action === 'شراء') {
                badgeClass = 'buy-badge';
                badgeIcon = 'fas fa-arrow-up';
            } else if (recommendation.action === 'بيع') {
                badgeClass = 'sell-badge';
                badgeIcon = 'fas fa-arrow-down';
            }

            const contentHtml = `
                <div class="message-text">
                    <strong>تحليل شامل لـ ${symbol}</strong>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <i class="fas fa-chart-line"></i>
                        معلومات السعر
                    </div>
                    <div class="metric-grid">
                        <div class="metric-item">
                            <div class="metric-value">${analysis.current_price}</div>
                            <div class="metric-label">السعر الحالي</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" style="color: ${analysis.change >= 0 ? '#28a745' : '#dc3545'}">
                                ${analysis.change_percent.toFixed(2)}%
                            </div>
                            <div class="metric-label">التغيير اليومي</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${analysis.volume.toLocaleString()}</div>
                            <div class="metric-label">حجم التداول</div>
                        </div>
                    </div>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <i class="fas fa-lightbulb"></i>
                        التوصية
                    </div>
                    <div style="text-align: center; margin: 1rem 0;">
                        <div class="recommendation-badge ${badgeClass}">
                            <i class="${badgeIcon}"></i>
                            ${recommendation.action} - ثقة ${recommendation.confidence}
                        </div>
                    </div>
                    <p>${recommendation.reasoning}</p>
                    <div class="book-reference">${recommendation.educational_quote}</div>
                </div>

                <div class="analysis-card">
                    <div class="analysis-header">
                        <i class="fas fa-chart-bar"></i>
                        التحليل الفني
                    </div>
                    <div class="metric-grid">
                        <div class="metric-item">
                            <div class="metric-value">${technical.rsi}</div>
                            <div class="metric-label">RSI</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${technical.trend_strength}</div>
                            <div class="metric-label">قوة الاتجاه</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${technical.support_level.toFixed(4)}</div>
                            <div class="metric-label">الدعم</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${technical.resistance_level.toFixed(4)}</div>
                            <div class="metric-label">المقاومة</div>
                        </div>
                    </div>

                    ${technical.patterns && technical.patterns.length > 0 ? `
                        <div style="margin-top: 1rem;">
                            <strong>النماذج المكتشفة:</strong>
                            ${technical.patterns.map(pattern => `
                                <div style="margin: 0.5rem 0; padding: 0.5rem; background: #f8f9fa; border-radius: 6px;">
                                    <strong>${pattern.pattern}</strong>
                                    <span class="recommendation-badge ${pattern.signal.includes('bullish') ? 'buy-badge' : 'sell-badge'}" style="font-size: 0.7rem; padding: 0.2rem 0.5rem; margin-right: 0.5rem;">
                                        ${pattern.signal}
                                    </span>
                                    <div style="font-size: 0.8rem; margin-top: 0.25rem;">${pattern.description}</div>
                                    ${pattern.reliability ? `<div style="font-size: 0.7rem; color: #666;">الموثوقية: ${pattern.reliability}%</div>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>

                ${data.educational_note ? `
                    <div class="info-section">
                        <div class="info-title">
                            <i class="fas fa-graduation-cap"></i>
                            ملاحظة تعليمية
                        </div>
                        <div>${data.educational_note}</div>
                    </div>
                ` : ''}
            `;

            messageGroup.innerHTML = `
                <div class="message-avatar ai-avatar-msg">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    ${contentHtml}
                    <div class="message-meta">
                        <i class="fas fa-clock"></i>
                        <span>${new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}</span>
                    </div>
                </div>
            `;

            messagesContainer.appendChild(messageGroup);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // سؤال سريع
        function askQuickQuestion(question) {
            document.getElementById('user-question').value = question;
            askAI();
        }

        // مسح المحادثة
        function clearChat() {
            const messagesContainer = document.getElementById('chat-messages');
            messagesContainer.innerHTML = '';
            document.getElementById('welcome-message').style.display = 'block';
            messagesContainer.appendChild(document.getElementById('welcome-message'));
        }

        // تبديل وضع التحليل
        function toggleAnalysis() {
            // يمكن إضافة وضع تحليل منفصل هنا
            askQuickQuestion('أظهر لي قائمة الرموز المتاحة للتحليل');
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            loadSymbols();

            // تركيز على حقل الإدخال
            document.getElementById('user-question').focus();

            // إضافة مستمع للنقر خارج المحادثة لإخفاء الاقتراحات
            document.addEventListener('click', function(event) {
                const suggestionsContainer = document.getElementById('suggestions-container');
                const inputWrapper = document.querySelector('.input-wrapper');

                if (!inputWrapper.contains(event.target)) {
                    // يمكن إضافة منطق إخفاء الاقتراحات هنا
                }
            });
        });

        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover على الاقتراحات
            const suggestions = document.querySelectorAll('.suggestion-chip');
            suggestions.forEach(suggestion => {
                suggestion.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                suggestion.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // تحديث حالة زر الإرسال
            const input = document.getElementById('user-question');
            const sendButton = document.getElementById('send-button');

            input.addEventListener('input', function() {
                if (this.value.trim()) {
                    sendButton.disabled = false;
                    sendButton.style.opacity = '1';
                } else {
                    sendButton.disabled = true;
                    sendButton.style.opacity = '0.5';
                }
            });

            // تعيين الحالة الأولية
            sendButton.disabled = true;
            sendButton.style.opacity = '0.5';
        });
    </script>
</body>
</html>
