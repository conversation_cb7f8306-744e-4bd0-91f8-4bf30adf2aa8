<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستشار التداول الذكي - AI Trading Advisor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .ai-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1rem 1.5rem;
        }
        
        .ai-chat {
            height: 500px;
            display: flex;
            flex-direction: column;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        
        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 10px;
            max-width: 80%;
        }
        
        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: left;
        }
        
        .ai-message {
            background: #e9ecef;
            color: #333;
            margin-right: auto;
        }
        
        .chat-input {
            display: flex;
            gap: 0.5rem;
        }
        
        .analysis-result {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .symbol-selector {
            margin-bottom: 1rem;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .recommendation-card {
            border-left: 4px solid;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0 10px 10px 0;
        }
        
        .buy-recommendation {
            border-left-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }
        
        .sell-recommendation {
            border-left-color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }
        
        .hold-recommendation {
            border-left-color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
        }
        
        .pattern-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
        }
        
        .pattern-signal {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .bullish-signal {
            background: #d4edda;
            color: #155724;
        }
        
        .bearish-signal {
            background: #f8d7da;
            color: #721c24;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
        }
        
        .spinner {
            display: inline-block;
            width: 2rem;
            height: 2rem;
            border: 0.25rem solid #f3f3f3;
            border-top: 0.25rem solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .wisdom-quote {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: #333;
            padding: 1rem;
            border-radius: 10px;
            margin-top: 1rem;
            font-style: italic;
            text-align: center;
        }
        
        .book-reference {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
        
        .quick-questions {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .quick-question {
            background: #e9ecef;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .quick-question:hover {
            background: #007bff;
            color: white;
        }
        
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="text-center text-white mb-0">
                <i class="fas fa-robot"></i>
                مستشار التداول الذكي
                <small class="d-block mt-2" style="font-size: 0.6em; opacity: 0.8;">
                    مدعوم بمعرفة أشهر كتب التداول العالمية
                </small>
            </h1>
        </div>
    </div>

    <div class="ai-container">
        <div class="main-grid">
            <!-- محادثة الذكاء الاصطناعي -->
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-comments"></i>
                        اسأل المستشار الذكي
                    </h3>
                </div>
                <div class="card-body ai-chat">
                    <div class="quick-questions">
                        <button class="quick-question" onclick="askQuickQuestion('ما هو مؤشر RSI؟')">ما هو RSI؟</button>
                        <button class="quick-question" onclick="askQuickQuestion('كيف أحسب وقف الخسارة؟')">وقف الخسارة</button>
                        <button class="quick-question" onclick="askQuickQuestion('ما هو نموذج الرأس والكتفين؟')">الرأس والكتفين</button>
                        <button class="quick-question" onclick="askQuickQuestion('كيف أتحكم في عواطف التداول؟')">علم النفس</button>
                        <button class="quick-question" onclick="askQuickQuestion('ما هي أفضل استراتيجية تداول؟')">الاستراتيجيات</button>
                    </div>
                    
                    <div class="chat-messages" id="chat-messages">
                        <div class="message ai-message">
                            <strong>🤖 المستشار الذكي:</strong><br>
                            مرحباً! أنا مستشارك الذكي للتداول، مدعوم بمعرفة أشهر كتب التداول العالمية مثل:<br>
                            📚 Technical Analysis - John Murphy<br>
                            📚 The Intelligent Investor - Benjamin Graham<br>
                            📚 Market Wizards - Jack Schwager<br>
                            📚 Trading in the Zone - Mark Douglas<br><br>
                            اسألني عن أي شيء متعلق بالتداول!
                        </div>
                    </div>
                    
                    <div class="chat-input">
                        <input type="text" class="form-control" id="user-question" 
                               placeholder="اسأل عن التحليل الفني، إدارة المخاطر، علم النفس..." 
                               onkeypress="handleEnter(event)">
                        <button class="btn btn-primary" onclick="askAI()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- تحليل الرموز -->
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-chart-line"></i>
                        تحليل ذكي للرموز
                    </h3>
                </div>
                <div class="card-body">
                    <div class="symbol-selector">
                        <select class="form-select" id="symbol-select" onchange="analyzeSymbol()">
                            <option value="">اختر رمز للتحليل</option>
                        </select>
                    </div>
                    
                    <div class="analysis-result" id="analysis-result">
                        <div class="text-center text-muted">
                            <i class="fas fa-chart-bar fa-3x mb-3"></i>
                            <p>اختر رمز مالي للحصول على تحليل شامل</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- حكمة اليوم -->
        <div class="wisdom-quote" id="daily-wisdom">
            <i class="fas fa-quote-left"></i>
            جاري تحميل حكمة اليوم...
            <i class="fas fa-quote-right"></i>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let symbols = [];

        // تحميل قائمة الرموز
        async function loadSymbols() {
            try {
                const response = await fetch('/api/symbols');
                symbols = await response.json();
                
                const select = document.getElementById('symbol-select');
                symbols.forEach(symbol => {
                    const option = document.createElement('option');
                    option.value = symbol;
                    option.textContent = symbol;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('خطأ في تحميل الرموز:', error);
            }
        }

        // سؤال الذكاء الاصطناعي
        async function askAI() {
            const questionInput = document.getElementById('user-question');
            const question = questionInput.value.trim();
            
            if (!question) return;
            
            // إضافة رسالة المستخدم
            addMessage(question, 'user');
            questionInput.value = '';
            
            // إضافة مؤشر التحميل
            addMessage('جاري التفكير...', 'ai', true);
            
            try {
                const response = await fetch('/api/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ question })
                });
                
                const data = await response.json();
                
                // إزالة مؤشر التحميل
                removeLastMessage();
                
                // إضافة إجابة الذكاء الاصطناعي
                addAIResponse(data);
                
            } catch (error) {
                removeLastMessage();
                addMessage('عذراً، حدث خطأ في الاتصال. حاول مرة أخرى.', 'ai');
            }
        }

        // إضافة رسالة للمحادثة
        function addMessage(text, sender, isLoading = false) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            if (isLoading) {
                messageDiv.innerHTML = `<div class="spinner"></div> ${text}`;
                messageDiv.id = 'loading-message';
            } else {
                if (sender === 'user') {
                    messageDiv.innerHTML = `<strong>👤 أنت:</strong><br>${text}`;
                } else {
                    messageDiv.innerHTML = `<strong>🤖 المستشار:</strong><br>${text}`;
                }
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // إضافة إجابة الذكاء الاصطناعي المفصلة
        function addAIResponse(data) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ai-message';
            
            let html = `<strong>🤖 المستشار الذكي:</strong><br>`;
            html += `<div style="margin-top: 0.5rem;">${data.answer}</div>`;
            
            if (data.source) {
                html += `<div class="book-reference">📚 المصدر: ${data.source}</div>`;
            }
            
            if (data.practical_tip) {
                html += `<div style="background: #e3f2fd; padding: 0.5rem; border-radius: 5px; margin-top: 0.5rem;">
                    <strong>💡 نصيحة عملية:</strong> ${data.practical_tip}
                </div>`;
            }
            
            if (data.strategies) {
                html += `<div style="margin-top: 0.5rem;"><strong>📋 الاستراتيجيات:</strong><ul>`;
                data.strategies.forEach(strategy => {
                    html += `<li>${strategy}</li>`;
                });
                html += `</ul></div>`;
            }
            
            if (data.topics) {
                html += `<div style="margin-top: 0.5rem;"><strong>📋 يمكنني مساعدتك في:</strong><ul>`;
                data.topics.forEach(topic => {
                    html += `<li>${topic}</li>`;
                });
                html += `</ul></div>`;
            }
            
            if (data.book_reference) {
                html += `<div class="book-reference">📖 مرجع: ${data.book_reference}</div>`;
            }
            
            if (data.wisdom) {
                html += `<div style="background: #fff3cd; padding: 0.5rem; border-radius: 5px; margin-top: 0.5rem; font-style: italic;">
                    💭 ${data.wisdom}
                </div>`;
            }
            
            messageDiv.innerHTML = html;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // إزالة آخر رسالة (مؤشر التحميل)
        function removeLastMessage() {
            const loadingMessage = document.getElementById('loading-message');
            if (loadingMessage) {
                loadingMessage.remove();
            }
        }

        // سؤال سريع
        function askQuickQuestion(question) {
            document.getElementById('user-question').value = question;
            askAI();
        }

        // معالج الضغط على Enter
        function handleEnter(event) {
            if (event.key === 'Enter') {
                askAI();
            }
        }

        // تحليل رمز مالي
        async function analyzeSymbol() {
            const select = document.getElementById('symbol-select');
            const symbol = select.value;
            
            if (!symbol) return;
            
            const resultContainer = document.getElementById('analysis-result');
            resultContainer.innerHTML = '<div class="loading"><div class="spinner"></div><p>جاري التحليل...</p></div>';
            
            try {
                const response = await fetch(`/api/analyze/${symbol}`);
                const data = await response.json();
                
                displayAnalysis(data);
                
            } catch (error) {
                resultContainer.innerHTML = '<div class="text-center text-danger">حدث خطأ في التحليل</div>';
            }
        }

        // عرض نتائج التحليل
        function displayAnalysis(data) {
            const container = document.getElementById('analysis-result');
            
            if (data.error) {
                container.innerHTML = `<div class="text-center text-danger">${data.error}</div>`;
                return;
            }
            
            const analysis = data.analysis;
            const technical = data.technical;
            const recommendation = data.recommendation;
            
            let html = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="metric-card">
                            <div class="metric-value">${analysis.current_price}</div>
                            <div class="metric-label">السعر الحالي</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="metric-card">
                            <div class="metric-value">${analysis.change_percent.toFixed(2)}%</div>
                            <div class="metric-label">التغيير اليومي</div>
                        </div>
                    </div>
                </div>
                
                <div class="recommendation-card ${recommendation.action === 'شراء' ? 'buy' : recommendation.action === 'بيع' ? 'sell' : 'hold'}-recommendation">
                    <h5><i class="fas fa-lightbulb"></i> التوصية</h5>
                    <p><strong>${recommendation.action}</strong> - ثقة: ${recommendation.confidence}</p>
                    <p>${recommendation.reasoning}</p>
                    <small>${recommendation.educational_quote}</small>
                </div>
                
                <h6><i class="fas fa-chart-line"></i> التحليل الفني</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>RSI:</strong> ${technical.rsi}</p>
                        <p><strong>قوة الاتجاه:</strong> ${technical.trend_strength}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>الدعم:</strong> ${technical.support_level.toFixed(4)}</p>
                        <p><strong>المقاومة:</strong> ${technical.resistance_level.toFixed(4)}</p>
                    </div>
                </div>
            `;
            
            if (technical.patterns && technical.patterns.length > 0) {
                html += `<h6><i class="fas fa-shapes"></i> النماذج المكتشفة</h6>`;
                technical.patterns.forEach(pattern => {
                    const signalClass = pattern.signal.includes('bullish') ? 'bullish-signal' : 'bearish-signal';
                    html += `
                        <div class="pattern-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>${pattern.pattern}</strong>
                                <span class="pattern-signal ${signalClass}">${pattern.signal}</span>
                            </div>
                            <p class="mb-1">${pattern.description}</p>
                            ${pattern.reliability ? `<small>الموثوقية: ${pattern.reliability}%</small><br>` : ''}
                            <small class="book-reference">${pattern.source}</small>
                        </div>
                    `;
                });
            }
            
            if (data.educational_note) {
                html += `
                    <div class="wisdom-quote mt-3">
                        <i class="fas fa-graduation-cap"></i>
                        ${data.educational_note}
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        // تحميل حكمة اليوم
        async function loadDailyWisdom() {
            try {
                const response = await fetch('/api/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ question: 'حكمة' })
                });
                
                const data = await response.json();
                if (data.wisdom) {
                    document.getElementById('daily-wisdom').innerHTML = `
                        <i class="fas fa-quote-left"></i>
                        ${data.wisdom}
                        <i class="fas fa-quote-right"></i>
                    `;
                }
            } catch (error) {
                console.error('خطأ في تحميل الحكمة:', error);
            }
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            loadSymbols();
            loadDailyWisdom();
        });
    </script>
</body>
</html>
