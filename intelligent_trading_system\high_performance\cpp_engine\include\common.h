/**
 * @file common.h
 * @brief Common definitions and utilities for the trading engine
 * <AUTHOR> AI Trading System
 * @version 1.0.0
 */

#ifndef COMMON_H
#define COMMON_H

#include <vector>
#include <string>
#include <memory>
#include <chrono>
#include <unordered_map>
#include <queue>
#include <mutex>
#include <atomic>
#include <thread>
#include <condition_variable>
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <random>

// Performance optimization
#ifdef _MSC_VER
    #define FORCE_INLINE __forceinline
#else
    #define FORCE_INLINE __attribute__((always_inline)) inline
#endif

// Platform-specific includes
#ifdef _WIN32
    #include <windows.h>
#else
    #include <sys/time.h>
    #include <unistd.h>
#endif

namespace TradingEngine {

// Type definitions
using Price = double;
using Volume = long long;
using Timestamp = std::chrono::high_resolution_clock::time_point;
using OrderId = std::string;
using Symbol = std::string;

// Enumerations
enum class OrderType {
    MARKET,
    LIMIT,
    STOP,
    STOP_LIMIT
};

enum class OrderSide {
    BUY,
    SELL
};

enum class OrderStatus {
    PENDING,
    PARTIALLY_FILLED,
    FILLED,
    CANCELLED,
    REJECTED
};

enum class TradingSignal {
    BUY,
    SELL,
    HOLD
};

// Market data structure
struct MarketData {
    Symbol symbol;
    Timestamp timestamp;
    Price open;
    Price high;
    Price low;
    Price close;
    Volume volume;
    Price bid;
    Price ask;
    Volume bid_size;
    Volume ask_size;
    
    MarketData() = default;
    MarketData(const Symbol& sym, Price o, Price h, Price l, Price c, Volume v)
        : symbol(sym), open(o), high(h), low(l), close(c), volume(v),
          timestamp(std::chrono::high_resolution_clock::now()) {}
};

// Order structure
struct Order {
    OrderId id;
    Symbol symbol;
    OrderType type;
    OrderSide side;
    Price price;
    Volume quantity;
    Volume filled_quantity;
    OrderStatus status;
    Timestamp created_time;
    Timestamp updated_time;
    
    Order() = default;
    Order(const OrderId& order_id, const Symbol& sym, OrderType t, OrderSide s, 
          Price p, Volume q)
        : id(order_id), symbol(sym), type(t), side(s), price(p), 
          quantity(q), filled_quantity(0), status(OrderStatus::PENDING),
          created_time(std::chrono::high_resolution_clock::now()),
          updated_time(created_time) {}
};

// Trade structure
struct Trade {
    OrderId buy_order_id;
    OrderId sell_order_id;
    Symbol symbol;
    Price price;
    Volume quantity;
    Timestamp timestamp;
    
    Trade(const OrderId& buy_id, const OrderId& sell_id, const Symbol& sym,
          Price p, Volume q)
        : buy_order_id(buy_id), sell_order_id(sell_id), symbol(sym),
          price(p), quantity(q), timestamp(std::chrono::high_resolution_clock::now()) {}
};

// Position structure
struct Position {
    Symbol symbol;
    Volume quantity;
    Price average_price;
    Price unrealized_pnl;
    Price realized_pnl;
    
    Position() = default;
    Position(const Symbol& sym, Volume q, Price avg_price)
        : symbol(sym), quantity(q), average_price(avg_price),
          unrealized_pnl(0.0), realized_pnl(0.0) {}
};

// Portfolio structure
struct Portfolio {
    std::string name;
    Price total_value;
    Price cash_balance;
    Price equity;
    std::unordered_map<Symbol, Position> positions;
    
    Portfolio(const std::string& portfolio_name, Price initial_cash)
        : name(portfolio_name), total_value(initial_cash), 
          cash_balance(initial_cash), equity(initial_cash) {}
};

// Technical indicator result
struct IndicatorResult {
    std::vector<Price> values;
    Timestamp calculated_time;
    
    IndicatorResult() : calculated_time(std::chrono::high_resolution_clock::now()) {}
};

// Risk metrics
struct RiskMetrics {
    Price var_95;           // Value at Risk 95%
    Price var_99;           // Value at Risk 99%
    Price expected_shortfall;
    Price max_drawdown;
    Price volatility;
    Price sharpe_ratio;
    Price sortino_ratio;
    
    RiskMetrics() : var_95(0), var_99(0), expected_shortfall(0),
                   max_drawdown(0), volatility(0), sharpe_ratio(0), sortino_ratio(0) {}
};

// Utility functions
class Utils {
public:
    // Get current timestamp
    static FORCE_INLINE Timestamp getCurrentTime() {
        return std::chrono::high_resolution_clock::now();
    }
    
    // Convert timestamp to milliseconds since epoch
    static FORCE_INLINE long long timestampToMs(const Timestamp& ts) {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            ts.time_since_epoch()).count();
    }
    
    // Generate unique order ID
    static std::string generateOrderId() {
        static std::atomic<long long> counter{0};
        auto now = getCurrentTime();
        auto ms = timestampToMs(now);
        return "ORD_" + std::to_string(ms) + "_" + std::to_string(++counter);
    }
    
    // Calculate percentage change
    static FORCE_INLINE Price percentageChange(Price old_value, Price new_value) {
        if (old_value == 0.0) return 0.0;
        return ((new_value - old_value) / old_value) * 100.0;
    }
    
    // Calculate moving average
    static Price movingAverage(const std::vector<Price>& prices, size_t period) {
        if (prices.size() < period) return 0.0;
        
        Price sum = 0.0;
        for (size_t i = prices.size() - period; i < prices.size(); ++i) {
            sum += prices[i];
        }
        return sum / period;
    }
    
    // Calculate standard deviation
    static Price standardDeviation(const std::vector<Price>& values) {
        if (values.empty()) return 0.0;
        
        Price mean = std::accumulate(values.begin(), values.end(), 0.0) / values.size();
        Price variance = 0.0;
        
        for (const auto& value : values) {
            variance += std::pow(value - mean, 2);
        }
        
        return std::sqrt(variance / values.size());
    }
    
    // High-precision timer
    class Timer {
    private:
        Timestamp start_time;
        
    public:
        Timer() : start_time(getCurrentTime()) {}
        
        void reset() {
            start_time = getCurrentTime();
        }
        
        double elapsedMs() const {
            auto now = getCurrentTime();
            return std::chrono::duration<double, std::milli>(now - start_time).count();
        }
        
        double elapsedUs() const {
            auto now = getCurrentTime();
            return std::chrono::duration<double, std::micro>(now - start_time).count();
        }
    };
};

// Thread-safe logger
class Logger {
private:
    static std::mutex log_mutex;
    static std::ofstream log_file;
    static bool initialized;
    
public:
    enum Level {
        DEBUG = 0,
        INFO = 1,
        WARNING = 2,
        ERROR = 3
    };
    
    static void initialize(const std::string& filename = "trading_engine.log");
    static void log(Level level, const std::string& message);
    static void debug(const std::string& message) { log(DEBUG, message); }
    static void info(const std::string& message) { log(INFO, message); }
    static void warning(const std::string& message) { log(WARNING, message); }
    static void error(const std::string& message) { log(ERROR, message); }
};

// Memory pool for high-frequency allocations
template<typename T, size_t PoolSize = 1000>
class MemoryPool {
private:
    alignas(T) char pool[sizeof(T) * PoolSize];
    std::vector<T*> free_list;
    std::mutex pool_mutex;
    
public:
    MemoryPool() {
        for (size_t i = 0; i < PoolSize; ++i) {
            free_list.push_back(reinterpret_cast<T*>(pool + i * sizeof(T)));
        }
    }
    
    T* allocate() {
        std::lock_guard<std::mutex> lock(pool_mutex);
        if (free_list.empty()) {
            return new T();  // Fallback to heap allocation
        }
        
        T* ptr = free_list.back();
        free_list.pop_back();
        return ptr;
    }
    
    void deallocate(T* ptr) {
        std::lock_guard<std::mutex> lock(pool_mutex);
        if (ptr >= reinterpret_cast<T*>(pool) && 
            ptr < reinterpret_cast<T*>(pool + sizeof(pool))) {
            free_list.push_back(ptr);
        } else {
            delete ptr;  // Was heap allocated
        }
    }
};

// Lock-free queue for high-frequency data
template<typename T>
class LockFreeQueue {
private:
    struct Node {
        std::atomic<T*> data;
        std::atomic<Node*> next;
        
        Node() : data(nullptr), next(nullptr) {}
    };
    
    std::atomic<Node*> head;
    std::atomic<Node*> tail;
    
public:
    LockFreeQueue() {
        Node* dummy = new Node;
        head.store(dummy);
        tail.store(dummy);
    }
    
    ~LockFreeQueue() {
        while (Node* const old_head = head.load()) {
            head.store(old_head->next);
            delete old_head;
        }
    }
    
    void enqueue(T item) {
        Node* new_node = new Node;
        T* data = new T(std::move(item));
        new_node->data.store(data);
        
        Node* prev_tail = tail.exchange(new_node);
        prev_tail->next.store(new_node);
    }
    
    bool dequeue(T& result) {
        Node* head_node = head.load();
        Node* next = head_node->next.load();
        
        if (next == nullptr) {
            return false;
        }
        
        T* data = next->data.load();
        if (data == nullptr) {
            return false;
        }
        
        result = *data;
        delete data;
        head.store(next);
        delete head_node;
        
        return true;
    }
};

} // namespace TradingEngine

#endif // COMMON_H
